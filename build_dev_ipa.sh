#!/bin/bash

# Exit on error
set -e

echo "Building Dev IPA..."

# Extract version from pubspec.yaml
VERSION=$(grep "version:" pubspec.yaml | sed 's/version: //g' | sed 's/ #.*//g' | tr -d '[:space:]')

echo "Building version: $VERSION"

# Build the UAT IPA
flutter build ipa -t lib/main.dart

# Rename the IPA to include "uat" and version in the filename
mv build/ios/ipa/mcdc.ipa build/ios/ipa/mcdc_dev_v$VERSION.ipa

# The IPA path
IPA_PATH="build/ios/ipa/mcdc_dev_v$VERSION.ipa"

# Cleanup process
if [ -f "$IPA_PATH" ]; then
  echo "Checking for unwanted files like ._Symbols in $IPA_PATH"
  unzip -l "$IPA_PATH" | grep ._Symbols && zip -d "$IPA_PATH" ._Symbols/ || echo "No ._Symbols found"
else
  echo "IPA not found at $IPA_PATH"
fi

echo "Dev IPA build completed successfully!"
echo "IPA location: $IPA_PATH"