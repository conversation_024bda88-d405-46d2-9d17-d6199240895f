import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/features/otp/presentation/components/otp_input_field.dart';

void main() {
  group('OtpInputField Backspace Behavior', () {
    late String capturedOtpValue;
    late String capturedCompletedValue;

    setUp(() {
      capturedOtpValue = '';
      capturedCompletedValue = '';
    });

    Widget createTestWidget() {
      return ScreenUtilInit(
        designSize: const Size(393, 852),
        builder: (context, child) => MaterialApp(
          home: Scaffold(
            body: OtpInputField(
              length: 6,
              onChanged: (value) {
                capturedOtpValue = value;
              },
              onCompleted: (value) {
                capturedCompletedValue = value;
              },
            ),
          ),
        ),
      );
    }

    testWidgets('should delete current field content when backspace is pressed on filled field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the first text field
      final firstField = find.byType(TextFormField).first;
      
      // Enter a digit in the first field
      await tester.enterText(firstField, '1');
      await tester.pump();
      
      // Verify the digit was entered
      expect(capturedOtpValue, '1');
      
      // Focus on the first field and simulate backspace
      await tester.tap(firstField);
      await tester.pump();
      
      // Simulate backspace key press
      await tester.sendKeyEvent(LogicalKeyboardKey.backspace);
      await tester.pump();
      
      // Verify the field was cleared
      expect(capturedOtpValue, '');
    });

    testWidgets('should move to previous field and delete its content when backspace is pressed on empty field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find text fields
      final firstField = find.byType(TextFormField).at(0);
      final secondField = find.byType(TextFormField).at(1);
      
      // Enter a digit in the first field
      await tester.enterText(firstField, '1');
      await tester.pump();
      
      // The cursor should automatically move to the second field
      // Now simulate backspace on the empty second field
      await tester.sendKeyEvent(LogicalKeyboardKey.backspace);
      await tester.pump();
      
      // Verify the first field was cleared and focus moved back
      expect(capturedOtpValue, '');
    });

    testWidgets('should handle multiple digits and backspace navigation correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find text fields
      final fields = List.generate(6, (index) => find.byType(TextFormField).at(index));
      
      // Enter digits in first three fields
      await tester.enterText(fields[0], '1');
      await tester.pump();
      await tester.enterText(fields[1], '2');
      await tester.pump();
      await tester.enterText(fields[2], '3');
      await tester.pump();
      
      // Verify OTP value
      expect(capturedOtpValue, '123');
      
      // Focus on the fourth field (which should be empty) and press backspace
      await tester.tap(fields[3]);
      await tester.pump();
      await tester.sendKeyEvent(LogicalKeyboardKey.backspace);
      await tester.pump();
      
      // Should clear the third field and move focus there
      expect(capturedOtpValue, '12');
      
      // Press backspace again (now on third field with focus)
      await tester.sendKeyEvent(LogicalKeyboardKey.backspace);
      await tester.pump();
      
      // Should clear the second field and move focus there
      expect(capturedOtpValue, '1');
    });

    testWidgets('should not move beyond first field when pressing backspace', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the first text field
      final firstField = find.byType(TextFormField).first;
      
      // Focus on the first field (which is empty) and press backspace
      await tester.tap(firstField);
      await tester.pump();
      await tester.sendKeyEvent(LogicalKeyboardKey.backspace);
      await tester.pump();
      
      // Should remain empty and not cause any errors
      expect(capturedOtpValue, '');
    });

    testWidgets('should handle typing new number in filled field correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the first text field
      final firstField = find.byType(TextFormField).first;
      
      // Enter a digit in the first field
      await tester.enterText(firstField, '1');
      await tester.pump();
      
      // Verify the digit was entered
      expect(capturedOtpValue, '1');
      
      // Focus on the first field and enter a new digit
      await tester.tap(firstField);
      await tester.pump();
      await tester.enterText(firstField, '5');
      await tester.pump();
      
      // Should replace the existing digit
      expect(capturedOtpValue, '5');
    });
  });
}
