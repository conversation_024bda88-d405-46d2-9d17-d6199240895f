import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';
import 'package:mcdc/features/user/domain/usecases/validate_member_register.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_state.dart';

import 'validate_register_cubit_force_error_test.mocks.dart';

@GenerateMocks([ValidateMemberRegister])
void main() {
  group('ValidateRegisterCubit forceErrorText behavior', () {
    late ValidateRegisterCubit cubit;
    late MockValidateMemberRegister mockValidateMemberRegister;

    setUp(() {
      mockValidateMemberRegister = MockValidateMemberRegister();
      cubit = ValidateRegisterCubit(
        validateMemberRegister: mockValidateMemberRegister,
      );
    });

    tearDown(() {
      cubit.close();
    });

    test('getFieldError returns null when state is idle', () {
      // Arrange
      expect(cubit.state, const ValidateRegisterState.idle());

      // Act & Assert
      expect(cubit.getFieldError('username'), isNull);
      expect(cubit.getFieldError('email'), isNull);
      expect(cubit.getFieldError('phone'), isNull);
    });

    test('getFieldError returns error when state is invalid', () {
      // Arrange
      const error = RegisterValidationError(
        username: ['Username is required'],
        email: ['Email is invalid'],
        phone: null,
      );

      // Act
      cubit.emit(ValidateRegisterState.invalid(error));

      // Assert
      expect(cubit.getFieldError('username'), 'Username is required');
      expect(cubit.getFieldError('email'), 'Email is invalid');
      expect(cubit.getFieldError('phone'), isNull);
    });

    test('clearFieldError removes specific field error', () {
      // Arrange
      const error = RegisterValidationError(
        username: ['Username is required'],
        email: ['Email is invalid'],
        phone: ['Phone is required'],
      );
      cubit.emit(ValidateRegisterState.invalid(error));

      // Act
      cubit.clearFieldError('username');

      // Assert
      expect(cubit.getFieldError('username'), isNull);
      expect(cubit.getFieldError('email'), 'Email is invalid');
      expect(cubit.getFieldError('phone'), 'Phone is required');
    });

    test('clearFieldError resets to idle when no errors remain', () {
      // Arrange
      const error = RegisterValidationError(
        username: ['Username is required'],
        email: null,
        phone: null,
      );
      cubit.emit(ValidateRegisterState.invalid(error));

      // Act
      cubit.clearFieldError('username');

      // Assert
      expect(cubit.state, const ValidateRegisterState.idle());
    });

    blocTest<ValidateRegisterCubit, ValidateRegisterState>(
      'validateRegistrationForm preserves error state during validation',
      build: () {
        when(mockValidateMemberRegister(any)).thenAnswer(
          (_) async => const Right(RegisterValidationError(
            username: ['Username already exists'],
            email: null,
            phone: null,
          )),
        );
        return cubit;
      },
      act: (cubit) async {
        // First set an error state
        cubit.emit(const ValidateRegisterState.invalid(
          RegisterValidationError(username: ['Previous error']),
        ));
        
        // Then validate form (this should not reset the state immediately)
        await cubit.validateRegistrationForm(
          username: 'testuser',
          email: '<EMAIL>',
          phone: '0123456789',
          password: 'password123',
          confirmPassword: 'password123',
        );
      },
      expect: () => [
        const ValidateRegisterState.invalid(
          RegisterValidationError(username: ['Previous error']),
        ),
        const ValidateRegisterState.loading(),
        const ValidateRegisterState.invalid(
          RegisterValidationError(username: ['Username already exists']),
        ),
      ],
    );
  });
}
