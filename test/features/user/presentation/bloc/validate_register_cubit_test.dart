import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';
import 'package:mcdc/features/user/domain/usecases/validate_member_register.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_state.dart';

import 'validate_register_cubit_test.mocks.dart';

@GenerateMocks([ValidateMemberRegister])
void main() {
  late ValidateRegisterCubit cubit;
  late MockValidateMemberRegister mockValidateMemberRegister;

  setUp(() {
    mockValidateMemberRegister = MockValidateMemberRegister();
    cubit = ValidateRegisterCubit(validateMemberRegister: mockValidateMemberRegister);
  });

  tearDown(() {
    cubit.close();
  });

  group('ValidateRegisterCubit', () {
    test('initial state is idle', () {
      expect(cubit.state, const ValidateRegisterState.idle());
    });

    group('validateField', () {
      test('validates username correctly', () {
        // Empty username
        expect(cubit.validateField(fieldName: 'username', value: ''), 'กรุณาระบุ');
        
        // Too short username
        expect(cubit.validateField(fieldName: 'username', value: 'abc'), 'กรุณาระบุอย่างน้อย 4 ตัวอักษร');
        
        // Invalid characters
        expect(cubit.validateField(fieldName: 'username', value: 'user#'), 'ภาษาอังกฤษ ตัวเลข และสัญลักษณ์ @ _ - . เท่านั้น');
        
        // Valid username
        expect(cubit.validateField(fieldName: 'username', value: 'user123'), null);
        expect(cubit.validateField(fieldName: 'username', value: 'user@test'), null);
        expect(cubit.validateField(fieldName: 'username', value: 'user_test'), null);
        expect(cubit.validateField(fieldName: 'username', value: 'user-test'), null);
        expect(cubit.validateField(fieldName: 'username', value: 'user.test'), null);
      });

      test('validates email correctly', () {
        // Empty email
        expect(cubit.validateField(fieldName: 'email', value: ''), 'กรุณาระบุ');
        
        // Invalid email format
        expect(cubit.validateField(fieldName: 'email', value: 'invalid-email'), 'รูปแบบอีเมลไม่ถูกต้อง');
        
        // Valid email
        expect(cubit.validateField(fieldName: 'email', value: '<EMAIL>'), null);
      });

      test('validates phone correctly', () {
        // Empty phone
        expect(cubit.validateField(fieldName: 'phone', value: ''), 'กรุณาระบุ');
        
        // Non-numeric phone
        expect(cubit.validateField(fieldName: 'phone', value: '123abc4567'), 'ตัวเลขเท่านั้น');
        
        // Wrong length
        expect(cubit.validateField(fieldName: 'phone', value: '123456789'), 'กรุณาระบุตัวเลข 10 หลัก');
        expect(cubit.validateField(fieldName: 'phone', value: '12345678901'), 'กรุณาระบุตัวเลข 10 หลัก');
        
        // Valid phone
        expect(cubit.validateField(fieldName: 'phone', value: '0812345678'), null);
      });

      test('validates password correctly', () {
        // Empty password
        expect(cubit.validateField(fieldName: 'password', value: ''), 'กรุณาระบุ');
        
        // Too short password
        expect(cubit.validateField(fieldName: 'password', value: '123'), 'กรุณาระบุอย่างน้อย 4 ตัวอักษร');
        
        // Valid password
        expect(cubit.validateField(fieldName: 'password', value: '1234'), null);
        expect(cubit.validateField(fieldName: 'password', value: 'password123'), null);
      });

      test('validates confirm password correctly', () {
        // Empty confirm password
        expect(cubit.validateField(fieldName: 'confirmPassword', value: ''), 'กรุณาระบุ');
        
        // Passwords don't match
        expect(cubit.validateField(fieldName: 'confirmPassword', value: 'password1', confirmValue: 'password2'), 'รหัสผ่านไม่ตรงกัน');
        
        // Passwords match
        expect(cubit.validateField(fieldName: 'confirmPassword', value: 'password123', confirmValue: 'password123'), null);
      });
    });

    blocTest<ValidateRegisterCubit, ValidateRegisterState>(
      'emits [loading, valid] when validation succeeds',
      build: () {
        when(mockValidateMemberRegister(any)).thenAnswer(
          (_) async => const Right(RegisterValidationError()),
        );
        return cubit;
      },
      act: (cubit) => cubit.validateRegistrationForm(
        username: 'testuser',
        email: '<EMAIL>',
        phone: '0812345678',
        password: 'password123',
        confirmPassword: 'password123',
      ),
      expect: () => [
        const ValidateRegisterState.loading(),
        const ValidateRegisterState.valid(),
      ],
    );

    blocTest<ValidateRegisterCubit, ValidateRegisterState>(
      'emits [loading, invalid] when API returns validation errors',
      build: () {
        when(mockValidateMemberRegister(any)).thenAnswer(
          (_) async => const Right(RegisterValidationError(
            username: ['ชื่อผู้ใช้งานนี้มีอยู่ในระบบแล้ว'],
            email: ['อีเมลนี้มีอยู่ในระบบแล้ว'],
          )),
        );
        return cubit;
      },
      act: (cubit) => cubit.validateRegistrationForm(
        username: 'testuser',
        email: '<EMAIL>',
        phone: '0812345678',
        password: 'password123',
        confirmPassword: 'password123',
      ),
      expect: () => [
        const ValidateRegisterState.loading(),
        const ValidateRegisterState.invalid(RegisterValidationError(
          username: ['ชื่อผู้ใช้งานนี้มีอยู่ในระบบแล้ว'],
          email: ['อีเมลนี้มีอยู่ในระบบแล้ว'],
        )),
      ],
    );

    blocTest<ValidateRegisterCubit, ValidateRegisterState>(
      'emits [invalid] when local validation fails',
      build: () => cubit,
      act: (cubit) => cubit.validateRegistrationForm(
        username: 'ab', // Too short
        email: 'invalid-email', // Invalid format
        phone: '123', // Too short
        password: 'password123',
        confirmPassword: 'password123',
      ),
      expect: () => [
        isA<ValidateRegisterInvalid>(),
      ],
    );
  });
}
