import 'package:flutter_test/flutter_test.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_login_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_login_state.dart';
import 'package:mcdc/features/user/domain/entities/login_validation_error.dart';

void main() {
  group('ValidateLoginCubit', () {
    late ValidateLoginCubit cubit;

    setUp(() {
      cubit = ValidateLoginCubit();
    });

    tearDown(() {
      cubit.close();
    });

    test('initial state should be ValidateLoginIdle', () {
      expect(cubit.state, isA<ValidateLoginIdle>());
    });

    test('should have correct initial properties', () {
      expect(cubit.isFormValid, false);
      expect(cubit.isLoading, false);
      expect(cubit.hasError, false);
    });

    test('should reset to idle state', () {
      cubit.reset();
      expect(cubit.state, isA<ValidateLoginIdle>());
    });

    test('should clear field errors correctly', () {
      // Create a mock invalid state
      final error = LoginValidationError(
        username: ['Username error'],
        password: ['Password error'],
      );
      cubit.emit(ValidateLoginInvalid(error));

      expect(cubit.state, isA<ValidateLoginInvalid>());

      // Clear username error
      cubit.clearFieldError('username');

      final state = cubit.state as ValidateLoginInvalid;
      expect(state.error.hasUsernameError, false);
      expect(state.error.hasPasswordError, true);
    });

    test('should get field errors correctly when in invalid state', () {
      // Create invalid state
      final error = LoginValidationError(
        username: ['Username error'],
        password: ['Password error'],
      );
      cubit.emit(ValidateLoginInvalid(error));

      expect(cubit.getFieldError('username'), 'Username error');
      expect(cubit.getFieldError('password'), 'Password error');
      expect(cubit.getFieldError('unknown'), null);
    });

    test('should return null for field errors when in idle state', () {
      expect(cubit.getFieldError('username'), null);
      expect(cubit.getFieldError('password'), null);
    });
  });
}
