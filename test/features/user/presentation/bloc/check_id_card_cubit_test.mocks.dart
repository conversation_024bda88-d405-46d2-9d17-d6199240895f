// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in mcdc/test/features/user/presentation/bloc/check_id_card_cubit_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:mcdc/core/error/failures.dart' as _i5;
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart'
    as _i6;
import 'package:mcdc/features/user/domain/usecases/check_duplicate_id_card.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CheckDuplicateIdCard].
///
/// See the documentation for Mockito's code generation for more information.
class MockCheckDuplicateIdCard extends _i1.Mock
    implements _i3.CheckDuplicateIdCard {
  MockCheckDuplicateIdCard() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.CheckIdCardResult>> call(
    _i3.CheckDuplicateIdCardParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i6.CheckIdCardResult>
            >.value(
              _FakeEither_0<_i5.Failure, _i6.CheckIdCardResult>(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.CheckIdCardResult>>);
}
