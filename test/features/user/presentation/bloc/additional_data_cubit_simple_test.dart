import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_member_types.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_government_sectors.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_ministries_by_government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_departments_by_ministry.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_state.dart';

import 'additional_data_cubit_test.mocks.dart';

@GenerateMocks([
  GetMemberTypes,
  GetGovernmentSectors,
  GetMinistriesByGovernmentSector,
  GetDepartmentsByMinistry,
])
void main() {
  late AdditionalDataCubit cubit;
  late MockGetMemberTypes mockGetMemberTypes;
  late MockGetGovernmentSectors mockGetGovernmentSectors;
  late MockGetMinistriesByGovernmentSector mockGetMinistriesByGovernmentSector;
  late MockGetDepartmentsByMinistry mockGetDepartmentsByMinistry;

  // Test data
  const tMemberTypeGovernment = MemberType(
    id: 1,
    nameTh: 'ภาครัฐ',
    nameEn: 'Government',
  );

  const tGovernmentSector = GovernmentSector(
    id: 1,
    nameTh: 'กระทรวงการคลัง',
    nameEn: 'Ministry of Finance',
  );

  final tGovernmentSectors = [tGovernmentSector];

  setUp(() {
    mockGetMemberTypes = MockGetMemberTypes();
    mockGetGovernmentSectors = MockGetGovernmentSectors();
    mockGetMinistriesByGovernmentSector = MockGetMinistriesByGovernmentSector();
    mockGetDepartmentsByMinistry = MockGetDepartmentsByMinistry();

    cubit = AdditionalDataCubit(
      mockGetMemberTypes,
      mockGetGovernmentSectors,
      mockGetMinistriesByGovernmentSector,
      mockGetDepartmentsByMinistry,
    );
  });

  tearDown(() {
    cubit.close();
  });

  group('AdditionalDataCubit - updateMemberType fix', () {
    test('initial state should be AdditionalDataInitial', () {
      expect(cubit.state, equals(const AdditionalDataState.initial()));
    });

    blocTest<AdditionalDataCubit, AdditionalDataState>(
      'should emit state when member type is updated (this tests the fix)',
      build: () => cubit,
      seed: () => AdditionalDataState.loaded(
        governmentAgencies: tGovernmentSectors,
        ministries: const [],
        departments: const [],
      ),
      act: (cubit) {
        // This is the key test - updateMemberType should emit state
        cubit.updateMemberType(tMemberTypeGovernment);
      },
      expect: () => [
        // The state should be emitted after updateMemberType
        AdditionalDataState.loaded(
          governmentAgencies: tGovernmentSectors,
          ministries: const [],
          departments: const [],
        ),
      ],
      verify: (cubit) {
        expect(cubit.selectedMemberType, equals(tMemberTypeGovernment));
      },
    );

    test('updateMemberType should update selectedMemberType', () {
      // Set initial state
      cubit.emit(AdditionalDataState.loaded(
        governmentAgencies: tGovernmentSectors,
        ministries: const [],
        departments: const [],
      ));

      // Update member type
      cubit.updateMemberType(tMemberTypeGovernment);

      // Verify the member type was updated
      expect(cubit.selectedMemberType, equals(tMemberTypeGovernment));
    });
  });
}
