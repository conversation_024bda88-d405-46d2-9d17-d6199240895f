import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_member_types.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_government_sectors.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_ministries_by_government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_departments_by_ministry.dart';
import 'package:mcdc/features/user/domain/entities/organization.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_state.dart';

import 'additional_data_cubit_test.mocks.dart';

@GenerateMocks([
  GetMemberTypes,
  GetGovernmentSectors,
  GetMinistriesByGovernmentSector,
  GetDepartmentsByMinistry,
])
void main() {
  late AdditionalDataCubit cubit;
  late MockGetMemberTypes mockGetMemberTypes;
  late MockGetGovernmentSectors mockGetGovernmentSectors;
  late MockGetMinistriesByGovernmentSector mockGetMinistriesByGovernmentSector;
  late MockGetDepartmentsByMinistry mockGetDepartmentsByMinistry;

  // Test data
  const tMemberTypeGovernment = MemberType(
    id: 1,
    nameTh: 'ภาครัฐ',
    nameEn: 'Government',
  );

  const tMemberTypePrivate = MemberType(
    id: 2,
    nameTh: 'เอกชน',
    nameEn: 'Private',
  );

  const tGovernmentSector = GovernmentSector(
    id: 1,
    nameTh: 'กระทรวงการคลัง',
    nameEn: 'Ministry of Finance',
  );

  final tMemberTypes = [tMemberTypeGovernment, tMemberTypePrivate];
  final tGovernmentSectors = [tGovernmentSector];

  setUp(() {
    mockGetMemberTypes = MockGetMemberTypes();
    mockGetGovernmentSectors = MockGetGovernmentSectors();
    mockGetMinistriesByGovernmentSector = MockGetMinistriesByGovernmentSector();
    mockGetDepartmentsByMinistry = MockGetDepartmentsByMinistry();

    cubit = AdditionalDataCubit(
      mockGetMemberTypes,
      mockGetGovernmentSectors,
      mockGetMinistriesByGovernmentSector,
      mockGetDepartmentsByMinistry,
    );
  });

  tearDown(() {
    cubit.close();
  });

  group('AdditionalDataCubit', () {
    test('initial state should be AdditionalDataInitial', () {
      expect(cubit.state, equals(const AdditionalDataState.initial()));
    });

    group('initialize', () {
      blocTest<AdditionalDataCubit, AdditionalDataState>(
        'should emit [loading, loaded] when initialization is successful',
        setUp: () {
          when(
            mockGetMemberTypes(NoParams()),
          ).thenAnswer((_) async => Right(tMemberTypes));
          when(
            mockGetGovernmentSectors(NoParams()),
          ).thenAnswer((_) async => Right(tGovernmentSectors));
        },
        build: () => cubit,
        act: (cubit) => cubit.initialize(),
        expect:
            () => [
              const AdditionalDataState.loading(),
              AdditionalDataState.loaded(
                governmentAgencies: tGovernmentSectors,
                ministries: const [],
                departments: const [],
              ),
            ],
        verify: (_) {
          verify(mockGetMemberTypes(NoParams()));
          verify(mockGetGovernmentSectors(NoParams()));
        },
      );

      blocTest<AdditionalDataCubit, AdditionalDataState>(
        'should emit [loading, error] when member types fetch fails',
        setUp: () {
          when(mockGetMemberTypes(NoParams())).thenAnswer(
            (_) async => const Left(ServerFailure(message: 'Server error')),
          );
          when(
            mockGetGovernmentSectors(NoParams()),
          ).thenAnswer((_) async => Right(tGovernmentSectors));
        },
        build: () => cubit,
        act: (cubit) => cubit.initialize(),
        expect:
            () => [
              const AdditionalDataState.loading(),
              const AdditionalDataState.error(message: 'Server error'),
            ],
      );
    });

    group('updateMemberType', () {
      setUp(() {
        // Initialize cubit with test data
        when(
          mockGetMemberTypes(NoParams()),
        ).thenAnswer((_) async => Right(tMemberTypes));
        when(
          mockGetGovernmentSectors(NoParams()),
        ).thenAnswer((_) async => Right(tGovernmentSectors));
      });

      blocTest<AdditionalDataCubit, AdditionalDataState>(
        'should emit loaded state when member type is updated',
        build: () => cubit,
        act: (cubit) {
          // First initialize the cubit
          cubit.initialize();
          // Then update member type
          cubit.updateMemberType(tMemberTypeGovernment);
        },
        expect:
            () => [
              const AdditionalDataState.loading(),
              AdditionalDataState.loaded(
                governmentAgencies: tGovernmentSectors,
                ministries: const [],
                departments: const [],
              ),
              // This is the key test - the state should be emitted again after updateMemberType
              AdditionalDataState.loaded(
                governmentAgencies: tGovernmentSectors,
                ministries: const [],
                departments: const [],
              ),
            ],
        verify: (cubit) {
          expect(cubit.selectedMemberType, equals(tMemberTypeGovernment));
        },
      );

      blocTest<AdditionalDataCubit, AdditionalDataState>(
        'should clear government-related selections when private sector is selected',
        build: () => cubit,
        act: (cubit) {
          // First initialize the cubit
          cubit.initialize();
          // Set government member type first
          cubit.updateMemberType(tMemberTypeGovernment);
          // Then switch to private sector
          cubit.updateMemberType(tMemberTypePrivate);
        },
        expect:
            () => [
              const AdditionalDataState.loading(),
              AdditionalDataState.loaded(
                governmentAgencies: tGovernmentSectors,
                ministries: const [],
                departments: const [],
              ),
              // After selecting government
              AdditionalDataState.loaded(
                governmentAgencies: tGovernmentSectors,
                ministries: const [],
                departments: const [],
              ),
              // After selecting private - should emit state again
              AdditionalDataState.loaded(
                governmentAgencies: tGovernmentSectors,
                ministries: const [],
                departments: const [],
              ),
            ],
        verify: (cubit) {
          expect(cubit.selectedMemberType, equals(tMemberTypePrivate));
          expect(cubit.selectedGovernmentSector, isNull);
          expect(cubit.selectedMinistry, isNull);
          expect(cubit.selectedDepartment, isNull);
        },
      );
    });
  });
}
