import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/enums/user_type.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';
import 'package:mcdc/features/user/domain/entities/user.dart';
import 'package:mcdc/features/user/domain/entities/token.dart';
import 'package:mcdc/features/user/domain/entities/session_info.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';
import 'package:mcdc/features/user/domain/usecases/consultant_login.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';

import 'consultant_login_test.mocks.dart';

@GenerateMocks([UserRepository, AuthStorageDataSource])
void main() {
  late ConsultantLogin usecase;
  late MockUserRepository mockUserRepository;
  late MockAuthStorageDataSource mockAuthStorage;

  setUp(() {
    mockUserRepository = MockUserRepository();
    mockAuthStorage = MockAuthStorageDataSource();
    usecase = ConsultantLogin(mockUserRepository, mockAuthStorage);
  });

  const testUsername = 'testconsultant';
  const testPassword = 'testpassword';
  const testParams = ConsultantLoginParams(
    username: testUsername,
    password: testPassword,
  );

  final testUser = User(
    id: 1,
    name: 'Test Consultant',
    email: '<EMAIL>',
    phone: '0901234567',
    username: testUsername,
    consultType: 1,
    consultTypeDisplay: 'ที่ปรึกษาอิสระ',
    corporateTypeId: null,
    corporateTypeDisplay: 'ไม่ระบุ',
    makerName: 'Test Maker',
    makerPhone: '0901234567',
    makerEmail: '<EMAIL>',
    verify: '1',
    verifyDisplay: 'ยืนยันแล้ว',
    score: null,
    isActiveMatching: true,
    isNotification: '1',
    status: '1',
    statusDisplay: 'Active',
    lang: 'th',
    userType: UserType.consultant,
  );

  final testTokens = Token(
    accessToken: 'test_access_token',
    refreshToken: 'test_refresh_token',
  );

  final testSessionInfo = SessionInfo(
    loginTime: DateTime.now(),
    userType: UserType.consultant,
  );

  final testLoginData = LoginData(
    user: testUser,
    tokens: testTokens,
    sessionInfo: testSessionInfo,
  );

  group('ConsultantLogin', () {
    test('should return LoginData when consultant login is successful', () async {
      // arrange
      when(mockUserRepository.consultantLogin(
        username: anyNamed('username'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => Right(testLoginData));

      when(mockAuthStorage.saveLoginData(any))
          .thenAnswer((_) async => Future.value());

      // act
      final result = await usecase(testParams);

      // assert
      expect(result, Right(testLoginData));
      verify(mockUserRepository.consultantLogin(
        username: testUsername,
        password: testPassword,
      ));
      verify(mockAuthStorage.saveLoginData(testLoginData));
    });

    test('should return Failure when consultant login fails', () async {
      // arrange
      const testFailure = ServerFailure(message: 'Login failed');
      when(mockUserRepository.consultantLogin(
        username: anyNamed('username'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => const Left(testFailure));

      // act
      final result = await usecase(testParams);

      // assert
      expect(result, const Left(testFailure));
      verify(mockUserRepository.consultantLogin(
        username: testUsername,
        password: testPassword,
      ));
      verifyNever(mockAuthStorage.saveLoginData(any));
    });

    test('should still return success even if saving to storage fails', () async {
      // arrange
      when(mockUserRepository.consultantLogin(
        username: anyNamed('username'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => Right(testLoginData));

      when(mockAuthStorage.saveLoginData(any))
          .thenThrow(Exception('Storage error'));

      // act
      final result = await usecase(testParams);

      // assert
      expect(result, Right(testLoginData));
      verify(mockUserRepository.consultantLogin(
        username: testUsername,
        password: testPassword,
      ));
      verify(mockAuthStorage.saveLoginData(testLoginData));
    });
  });
}
