import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/features/faq/presentation/faq_page.dart';

void main() {
  group('FaqPage', () {
    testWidgets('should display FAQ page with tabs', (
      WidgetTester tester,
    ) async {
      // Build the widget with ScreenUtil initialization
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) => const MaterialApp(home: FaqPage()),
        ),
      );

      // Verify that the app bar title is displayed
      expect(find.text('คำถามที่พบบ่อย'), findsOneWidget);

      // Verify that some tabs are displayed (they are in a scrollable list)
      expect(find.text('ทั้งหมด'), findsOneWidget);
      expect(find.text('วิธีการคำนวณ'), findsOneWidget);

      // Verify that FAQ items are displayed
      expect(
        find.text('สามารถดูวิธีการคำนวณระบบจับคู่ที่ปรึกษา ได้อย่างไร'),
        findsOneWidget,
      );
      expect(find.text('พบที่ปรึกษาที่ใช่ อย่างไร'), findsOneWidget);
    });

    testWidgets('should filter FAQs by category when tab is selected', (
      WidgetTester tester,
    ) async {
      // Build the widget with ScreenUtil initialization
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(393, 852),
          builder: (context, child) => const MaterialApp(home: FaqPage()),
        ),
      );

      // Tap on "วิธีการคำนวณ" tab
      await tester.tap(find.text('วิธีการคำนวณ'));
      await tester.pumpAndSettle();

      // Verify that only calculation-related FAQ is shown
      expect(
        find.text('สามารถดูวิธีการคำนวณระบบจับคู่ที่ปรึกษา ได้อย่างไร'),
        findsOneWidget,
      );
      expect(find.text('พบที่ปรึกษาที่ใช่ อย่างไร'), findsNothing);
    });
  });
}
