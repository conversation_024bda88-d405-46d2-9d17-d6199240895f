import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

/// A success dialog widget that matches the Figma design
class DialogSuccess extends StatelessWidget {
  final String? title;
  final Widget? icon;
  final Duration? autoDismissDuration;
  final VoidCallback? afterDismiss;

  const DialogSuccess({
    super.key,
    this.title,
    this.icon,
    this.autoDismissDuration,
    this.afterDismiss,
  });

  /// Shows the success dialog
  static Future<void> show({
    required BuildContext context,
    String? title,
    Widget? icon,
    Duration autoDismissDuration = const Duration(seconds: 2),
    VoidCallback? afterDismiss,
  }) async {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => DialogSuccess(
            title: title,
            icon: icon,
            autoDismissDuration: autoDismissDuration,
            afterDismiss: afterDismiss,
          ),
    );

    // Auto dismiss after specified duration
    await Future.delayed(autoDismissDuration);
    if (context.mounted) {
      Navigator.of(context).pop();
      afterDismiss?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 36.w),
      child: Container(
        width: min(340.w, 0.8.sw),
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: _buildContent(l10n),
      ),
    );
  }

  Widget _buildContent(AppLocalizations l10n) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 95.w, vertical: 66.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 131.w,
            height: 132.h,
            child:
                icon ??
                Image.asset('assets/images/success.gif', fit: BoxFit.cover),
          ),
          24.h.verticalSpace,
          Text(
            title ?? l10n.success,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
              height: 1.51,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
