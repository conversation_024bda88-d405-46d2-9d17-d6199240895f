import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';

/// A warning dialog widget that matches the Figma design
class DialogWarning extends StatelessWidget {
  final String? title;
  final String message;
  final String? okText;
  final VoidCallback? onOk;
  final Widget? icon;

  const DialogWarning({
    super.key,
    required this.title,
    required this.message,
    this.okText,
    this.onOk,
    this.icon,
  });

  /// Shows the confirmation dialog
  static Future<bool?> show({
    required BuildContext context,
    String? title,
    required String message,
    String? okText,
    Widget? icon,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => DialogWarning(
            title: title,
            message: message,
            okText: okText,
            icon: icon,
            onOk: () => Navigator.of(context).pop(false),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 36.w),
      child: Container(
        width: min(340.w, 0.8.sw),
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: _buildContent(l10n),
      ),
    );
  }

  Widget _buildContent(AppLocalizations l10n) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 38.5.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          31.h.verticalSpace,
          SizedBox(
            width: 138.w,
            height: 132.h,
            child:
                icon ??
                Image.asset(
                  'assets/images/exclamation_mark.gif',
                  fit: BoxFit.cover,
                ),
          ),
          24.h.verticalSpace,
          _buildTextContent(),
          24.h.verticalSpace,
          _buildButtons(l10n),
          31.h.verticalSpace,
        ],
      ),
    );
  }

  Widget _buildTextContent() {
    return Column(
      children: [
        if (title != null && title!.isNotEmpty)
          Text(
            title!,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefault,
              height: 1.51,
            ),
            textAlign: TextAlign.center,
          ),
        8.h.verticalSpace,
        Text(
          message,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.51,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildButtons(AppLocalizations l10n) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        PrimaryButton(
          text: okText ?? l10n.ok,
          onPressed: onOk,
          height: 40.h,
          borderRadius: 20.r,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
        ),
      ],
    );
  }
}
