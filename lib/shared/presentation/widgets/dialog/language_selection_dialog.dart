import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_bloc.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_event.dart';

class LanguageSelectionDialog extends StatefulWidget {
  const LanguageSelectionDialog({super.key});

  @override
  State<LanguageSelectionDialog> createState() =>
      _LanguageSelectionDialogState();
}

class _LanguageSelectionDialogState extends State<LanguageSelectionDialog> {
  String? selectedLanguage;

  @override
  void initState() {
    super.initState();
    // Set initial selection based on current language
    final languageBloc = context.read<LanguageBloc>();
    selectedLanguage = languageBloc.isThaiSelected ? 'th' : 'en';
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Text(
        l10n.selectLanguage,
        style: TextStyle(
          fontFamily: AppFonts.notoSansThai,
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.textDefaultDark,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildLanguageOption(
            value: 'th',
            title: l10n.thai,
            isSelected: selectedLanguage == 'th',
            onTap: () => setState(() => selectedLanguage = 'th'),
          ),
          SizedBox(height: 12.h),
          _buildLanguageOption(
            value: 'en',
            title: l10n.english,
            isSelected: selectedLanguage == 'en',
            onTap: () => setState(() => selectedLanguage = 'en'),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            l10n.cancel,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              color: AppColors.textSubdude,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            if (selectedLanguage != null) {
              final locale = Locale(selectedLanguage!);
              context.read<LanguageBloc>().add(ChangeLanguageEvent(locale));
            }
            Navigator.of(context).pop();
          },
          child: Text(
            l10n.confirm,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageOption({
    required String value,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.textPrimary : AppColors.borderDefault,
            width: 1.5,
          ),
          borderRadius: BorderRadius.circular(8.r),
          color:
              isSelected
                  ? AppColors.textPrimary.withOpacity(0.05)
                  : Colors.transparent,
        ),
        child: Row(
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected ? AppColors.textPrimary : AppColors.textSubdude,
              size: 20.w,
            ),
            SizedBox(width: 12.w),
            Text(
              title,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color:
                    isSelected
                        ? AppColors.textPrimary
                        : AppColors.textDefaultDark,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show the language selection dialog
Future<void> showLanguageSelectionDialog(BuildContext context) {
  return showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return const LanguageSelectionDialog();
    },
  );
}
