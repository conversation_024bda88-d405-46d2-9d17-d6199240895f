import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class ContentTile extends StatelessWidget {
  const ContentTile({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.date,
    required this.tag,
    this.imageHeight,
    this.imageWidth,
    this.onTap,
  });

  final String imageUrl;
  final String title;
  final String date;
  final String tag;
  final double? imageHeight;
  final double? imageWidth;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            child: Image.network(
              imageUrl,
              width: imageWidth ?? 398.w,
              height: imageHeight ?? 267.h,
              fit: BoxFit.cover,
            ),
          ),
          8.verticalSpace,
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textDefaultDark,
              height: 1.5,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          8.verticalSpace,
          Row(
            children: [
              SvgPicture.asset(
                'assets/icons/calendar.svg',
                width: 16.w,
                height: 16.w,
              ),
              8.horizontalSpace,
              Text(
                date,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            children: [
              SvgPicture.asset(
                'assets/icons/price_tag.svg',
                width: 16.w,
                height: 16.w,
              ),
              8.horizontalSpace,
              Text(
                tag,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
