import 'package:flutter/material.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class MainBackground extends StatelessWidget {
  final Color? backgroundColor;
  final Widget? child;
  final EdgeInsetsGeometry? padding;

  const MainBackground({
    super.key,
    this.backgroundColor,
    this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? EdgeInsets.zero,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surfaceDefault,
      ),
      child: child,
    );
  }
}
