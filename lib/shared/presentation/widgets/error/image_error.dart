import 'package:flutter/material.dart';
import 'package:mcdc/core/constants/app_colors.dart';

/// A widget that displays when an image fails to load.
///
/// This widget shows an error icon and message, and can be customized with
/// specific width and height values.
class ImageError extends StatelessWidget {
  /// Creates an image error widget.
  ///
  /// The [width] and [height] parameters are optional. If not provided,
  /// the widget will size itself to its parent constraints.
  const ImageError({
    super.key,
    this.width,
    this.height,
    this.message,
    this.icon = Icons.broken_image_outlined,
    this.iconSize = 32.0,
    this.iconColor,
    this.textStyle,
    this.backgroundColor,
  });

  /// The width of the error widget.
  final double? width;

  /// The height of the error widget.
  final double? height;

  /// The message to display.
  final String? message;

  /// The icon to display.
  final IconData icon;

  /// The size of the icon.
  final double iconSize;

  /// The color of the icon.
  final Color? iconColor;

  /// The style of the message text.
  final TextStyle? textStyle;

  /// The background color of the widget.
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: width,
      height: height,
      color: backgroundColor ?? AppColors.surfaceSubdude,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? AppColors.iconDefault,
            ),
            if (message != null) ...[
              const SizedBox(height: 8),
              Text(
                message!,
                style:
                    textStyle ??
                    theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
