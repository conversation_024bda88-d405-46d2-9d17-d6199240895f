import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? width;
  final double? height;
  final double? borderRadius;
  final EdgeInsets? padding;
  final bool isLoading;
  final bool isDisabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? borderColor;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.width,
    this.height,
    this.borderRadius,
    this.padding,
    this.isLoading = false,
    this.isDisabled = false,
    this.prefixIcon,
    this.suffixIcon,
    this.textStyle,
    this.backgroundColor,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultStyle = theme.elevatedButtonTheme.style;

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isDisabled || isLoading ? null : onPressed,
        style: defaultStyle?.copyWith(
          backgroundColor:
              backgroundColor != null
                  ? WidgetStateProperty.all(backgroundColor)
                  : null,
          foregroundColor:
              textStyle?.color != null
                  ? WidgetStateProperty.all(textStyle!.color)
                  : null,
          shape:
              borderRadius != null || borderColor != null
                  ? WidgetStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(borderRadius ?? 20.r),
                      side:
                          borderColor != null
                              ? BorderSide(color: borderColor!, width: 1.0)
                              : BorderSide.none,
                    ),
                  )
                  : null,
          padding: padding != null ? WidgetStateProperty.all(padding) : null,
          textStyle:
              textStyle != null ? WidgetStateProperty.all(textStyle) : null,
        ),
        child:
            isLoading
                ? SizedBox(
                  width: 24.w,
                  height: 24.w,
                  child: const CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
                : Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (prefixIcon != null) ...[
                      prefixIcon!,
                      SizedBox(width: 8.w),
                    ],
                    Flexible(
                      fit: FlexFit.loose,
                      child: Text(
                        text,
                        style:
                            textStyle ??
                            TextStyle(
                              fontFamily: AppFonts.notoSansThai,
                              color: Colors.white,
                              fontWeight: FontWeight.w400,
                              fontSize: 16.sp,
                              height: 1.5.sp,
                            ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (suffixIcon != null) ...[
                      SizedBox(width: 8.w),
                      suffixIcon!,
                    ],
                  ],
                ),
      ),
    );
  }
}
