import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/shared/presentation/widgets/webview/custom_webview.dart';

class TabsWithWebView extends StatefulWidget {
  const TabsWithWebView({super.key, required this.tabs});

  final List<TabItem> tabs;

  @override
  State<TabsWithWebView> createState() => _TabsWithWebViewState();
}

class _TabsWithWebViewState extends State<TabsWithWebView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: widget.tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        16.verticalSpace,
        TabBar(
          controller: _tabController,
          isScrollable: true,
          labelPadding: EdgeInsets.symmetric(horizontal: 12.w),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          labelColor: AppColors.textSecondary,
          unselectedLabelColor: AppColors.textSubdude,
          indicatorColor: AppColors.borderSecondary,
          indicatorWeight: 2.h,
          indicatorSize: TabBarIndicatorSize.tab,
          tabAlignment: TabAlignment.start,
          dividerHeight: 0,
          dividerColor: Colors.transparent,
          tabs:
              widget.tabs
                  .map(
                    (tab) => Tab(
                      height: 44.h,
                      child: Text(
                        tab.title,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          height: 1.5.sp,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),
        24.verticalSpace,
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: widget.tabs.map(_buildTabContent).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTabContent(TabItem tab) {
    return SizedBox(
      height: 800.h,
      width: 1.sw,
      child: CustomWebView(url: WebUri(tab.url)),
    );
  }
}

class TabItem {
  final String title;
  final String url;
  final double? height;
  final double? width;

  TabItem({required this.title, required this.url, this.height, this.width});
}
