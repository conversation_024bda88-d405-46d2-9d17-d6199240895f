import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class CustomWebView extends StatefulWidget {
  const CustomWebView({
    super.key,
    required this.url,
    this.onWebViewCreated,
    this.onLoadStop,
    this.onPermissionRequest,
    this.onProgressChanged,
    this.onUpdateVisitedHistory,
    this.initialSettings,
  });

  final WebUri url;
  final CustomWebViewSettings? initialSettings;

  final void Function(InAppWebViewController controller)? onWebViewCreated;
  final void Function(InAppWebViewController controller, WebUri? url)?
      onLoadStop;
  final Future<PermissionResponse?> Function(InAppWebViewController controller,
      PermissionRequest permissionRequest)? onPermissionRequest;
  final void Function(InAppWebViewController controller, int progress)?
      onProgressChanged;
  final void Function(
          InAppWebViewController controller, WebUri? url, bool? isReload)?
      onUpdateVisitedHistory;

  @override
  State<CustomWebView> createState() => _CustomWebViewState();
}

class _CustomWebViewState extends State<CustomWebView> {
  final CookieManager cookieManager = CookieManager.instance();

  InAppWebViewController? webViewController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  String checkAppDomain(WebUri url) {
    if (url.host.endsWith('myappdomain.com')) {
      return url.host;
    } else {
      return "";
    }
  }

  Future<void> refresh(InAppWebViewController controller) async {
    await controller.reload();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InAppWebView(
        initialUrlRequest: URLRequest(
          url: widget.url,
        ),
        initialSettings: widget.initialSettings ?? CustomWebViewSettings(),
        onWebViewCreated: (controller) async {
          widget.onWebViewCreated?.call(controller);
          webViewController = controller;

          controller.addJavaScriptHandler(
              handlerName: "refresh",
              callback: (args) async {
                await refresh(controller);
              });
          await controller.reload();
        },
        onLoadStop: (controller, url) async {
          widget.onLoadStop?.call(controller, url);
        },
        onProgressChanged: (controller, progress) {
          widget.onProgressChanged?.call(controller, progress);
        },
        onPermissionRequest: widget.onPermissionRequest ??
            (controller, permissionRequest) async {
              return PermissionResponse(
                action: PermissionResponseAction.GRANT,
                resources: permissionRequest.resources,
              );
            },
        onReceivedServerTrustAuthRequest: (controller, challenge) async {
          return ServerTrustAuthResponse(
              action: ServerTrustAuthResponseAction.PROCEED);
        },
      ),
    );
  }
}

class CustomWebViewSettings extends InAppWebViewSettings {
  CustomWebViewSettings({
    super.userAgent = null,
    super.useHybridComposition = true,
    super.allowsInlineMediaPlayback = true,
    super.mediaPlaybackRequiresUserGesture = false,
    super.isInspectable = kDebugMode,
    super.supportZoom = false,
  }) : super();
}
