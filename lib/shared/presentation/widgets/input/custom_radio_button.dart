import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

/// Custom radio button widget that matches the Figma design
class CustomRadioButton<T> extends StatelessWidget {
  const CustomRadioButton({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.label,
    this.isVertical = false,
  });

  final T value;
  final T? groupValue;
  final ValueChanged<T?> onChanged;
  final String label;

  /// If true, the radio button will be displayed radio and label vertically
  /// Default is false
  final bool isVertical;

  bool get isSelected => value == groupValue;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(8.r),
      child:
          isVertical
              ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textDefaultDark,
                      height: 1.5.sp,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  _buildRadioIcon(),
                ],
              )
              : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textDefaultDark,
                      height: 1.5.sp,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  _buildRadioIcon(),
                ],
              ),
    );
  }

  Widget _buildRadioIcon() {
    return Container(
      width: 20.w,
      height: 20.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: isSelected ? AppColors.textPrimary : AppColors.borderDefault,
          width: isSelected ? 2 : 1,
        ),
        color: isSelected ? AppColors.textPrimary : Colors.transparent,
      ),
      child:
          isSelected
              ? Center(
                child: Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                ),
              )
              : null,
    );
  }
}
