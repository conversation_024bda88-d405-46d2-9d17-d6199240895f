import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class DropDownField extends StatelessWidget {
  const DropDownField({
    super.key,
    required this.onChange,
    required this.items,
    required this.hintText,
    this.initialValue,
    this.selectedValue,
    this.validator,
    this.ifNullSelectFirst = false,
  });

  final void Function(String) onChange;
  final String? initialValue;
  final List<String> items;
  final String? selectedValue;
  final String hintText;
  final String? Function()? validator;
  final bool ifNullSelectFirst;

  @override
  Widget build(BuildContext context) {
    final isInValid = validator != null && validator!() != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          height: 56.h,
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: isInValid ? AppColors.critical : AppColors.borderDefault,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8.r),
            /* boxShadow: [
              BoxShadow(
                color: Colors.grey.withAlpha(102),
                blurRadius: 5,
                spreadRadius: 0,
                offset: const Offset(0, 0),
              ),
            ], */
          ),
          child: DropdownButton<String?>(
            value:
                selectedValue != null
                    ? items.contains(selectedValue)
                        ? selectedValue
                        : ifNullSelectFirst
                        ? items.first
                        : null
                    : initialValue,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textDefaultDark,
              height: 1.5.sp,
            ),
            onChanged: (newValue) {
              if (newValue == null) return;
              onChange(newValue);
            },
            underline: const SizedBox.shrink(),
            isExpanded: true,
            hint: Text(
              hintText,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textSubdude,
                height: 1.5.sp,
              ),
            ),
            icon: const Icon(Icons.expand_more, color: AppColors.iconDefault),
            items:
                items
                    .map(
                      (e) => DropdownMenuItem<String>(
                        value: e,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textDefaultDark,
                            height: 1.5.sp,
                          ),
                        ),
                      ),
                    )
                    .toList(),
          ),
        ),
        isInValid
            ? Padding(
              padding: EdgeInsets.only(top: 12.sp, left: 12.sp),
              child: Text(
                validator!()!,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.critical,
                  height: 1.5.sp,
                ),
              ),
            )
            : const SizedBox.shrink(),
      ],
    );
  }
}
