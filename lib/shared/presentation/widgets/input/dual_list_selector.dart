import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

/// Model for expertise items with group headers
class ExpertiseItem {
  final String id;
  final String code;
  final String title;
  final String description;
  final String groupHeader;

  const ExpertiseItem({
    required this.id,
    required this.code,
    required this.title,
    required this.description,
    required this.groupHeader,
  });

  String get displayText => '$code : $title\n$description';
}

/// Dual list selector widget for expertise selection
class DualListSelector extends StatefulWidget {
  const DualListSelector({
    super.key,
    required this.label,
    required this.availableItems,
    required this.selectedItems,
    required this.onSelectionChanged,
    this.height = 230,
  });

  final String label;
  final List<ExpertiseItem> availableItems;
  final List<ExpertiseItem> selectedItems;
  final Function(List<ExpertiseItem>) onSelectionChanged;
  final double height;

  @override
  State<DualListSelector> createState() => _DualListSelectorState();
}

class _DualListSelectorState extends State<DualListSelector> {
  final TextEditingController _leftSearchController = TextEditingController();
  final TextEditingController _rightSearchController = TextEditingController();

  List<ExpertiseItem> _filteredAvailableItems = [];
  List<ExpertiseItem> _filteredSelectedItems = [];

  @override
  void initState() {
    super.initState();
    _updateFilteredLists();
    _leftSearchController.addListener(_filterAvailableItems);
    _rightSearchController.addListener(_filterSelectedItems);
  }

  @override
  void dispose() {
    _leftSearchController.dispose();
    _rightSearchController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(DualListSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.availableItems != widget.availableItems ||
        oldWidget.selectedItems != widget.selectedItems) {
      _updateFilteredLists();
    }
  }

  void _updateFilteredLists() {
    _filterAvailableItems();
    _filterSelectedItems();
  }

  void _filterAvailableItems() {
    final query = _leftSearchController.text.toLowerCase();
    setState(() {
      _filteredAvailableItems =
          widget.availableItems
              .where(
                (item) =>
                    !widget.selectedItems.any(
                      (selected) => selected.id == item.id,
                    ),
              )
              .where(
                (item) =>
                    query.isEmpty ||
                    item.code.toLowerCase().contains(query) ||
                    item.title.toLowerCase().contains(query) ||
                    item.description.toLowerCase().contains(query),
              )
              .toList();
    });
  }

  void _filterSelectedItems() {
    final query = _rightSearchController.text.toLowerCase();
    setState(() {
      _filteredSelectedItems =
          widget.selectedItems
              .where(
                (item) =>
                    query.isEmpty ||
                    item.code.toLowerCase().contains(query) ||
                    item.title.toLowerCase().contains(query) ||
                    item.description.toLowerCase().contains(query),
              )
              .toList();
    });
  }

  void _moveToSelected(ExpertiseItem item) {
    final newSelected = List<ExpertiseItem>.from(widget.selectedItems)
      ..add(item);
    widget.onSelectionChanged(newSelected);
    _updateFilteredLists();
  }

  void _moveToAvailable(ExpertiseItem item) {
    final newSelected = List<ExpertiseItem>.from(widget.selectedItems)
      ..removeWhere((selected) => selected.id == item.id);
    widget.onSelectionChanged(newSelected);
    _updateFilteredLists();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          widget.label,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        8.h.verticalSpace,

        // Dual list container
        Container(
          height: widget.height.h,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.borderDefault),
            borderRadius: BorderRadius.circular(8.r),
            color: Colors.white,
          ),
          child: Row(
            children: [
              // Available items list
              Expanded(
                child: _buildListSection(
                  title: l10n.availableItemsLabel,
                  searchController: _leftSearchController,
                  items: _filteredAvailableItems,
                  onItemTap: _moveToSelected,
                  isLeftSide: true,
                ),
              ),

              // Transfer buttons
              SizedBox(
                width: 40.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.swap_horiz,
                      color: AppColors.iconDefault,
                      size: 24.r,
                    ),
                  ],
                ),
              ),

              // Selected items list
              Expanded(
                child: _buildListSection(
                  title: l10n.selectedItemsLabel,
                  searchController: _rightSearchController,
                  items: _filteredSelectedItems,
                  onItemTap: _moveToAvailable,
                  isLeftSide: false,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildListSection({
    required String title,
    required TextEditingController searchController,
    required List<ExpertiseItem> items,
    required Function(ExpertiseItem) onItemTap,
    required bool isLeftSide,
  }) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: EdgeInsets.all(8.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            title,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
            ),
          ),
          8.h.verticalSpace,

          // Search field
          SizedBox(
            height: 40.h,
            child: TextField(
              controller: searchController,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textDefaultDark,
              ),
              decoration: InputDecoration(
                hintText: l10n.searchInListHint,
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textSubdude,
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6.r),
                  borderSide: const BorderSide(
                    color: AppColors.borderDefault,
                    width: 1,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6.r),
                  borderSide: const BorderSide(
                    color: AppColors.borderDefault,
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6.r),
                  borderSide: const BorderSide(
                    color: AppColors.borderSecondary,
                    width: 1,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                suffixIcon: Icon(
                  Icons.search,
                  color: AppColors.textSubdude,
                  size: 16.r,
                ),
              ),
            ),
          ),
          8.h.verticalSpace,

          // Items list
          Expanded(child: _buildItemsList(items, onItemTap)),
        ],
      ),
    );
  }

  Widget _buildItemsList(
    List<ExpertiseItem> items,
    Function(ExpertiseItem) onItemTap,
  ) {
    if (items.isEmpty) {
      return Center(
        child: Text(
          'ไม่มีรายการ',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
          ),
        ),
      );
    }

    // Group items by header
    final groupedItems = <String, List<ExpertiseItem>>{};
    for (final item in items) {
      groupedItems.putIfAbsent(item.groupHeader, () => []).add(item);
    }

    return ListView.builder(
      itemCount: groupedItems.length,
      itemBuilder: (context, groupIndex) {
        final groupHeader = groupedItems.keys.elementAt(groupIndex);
        final groupItems = groupedItems[groupHeader]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group header
            if (groupHeader.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
                margin: EdgeInsets.only(bottom: 4.h),
                decoration: BoxDecoration(
                  color: AppColors.surfaceSecondary,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  groupHeader,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ],

            // Group items
            ...groupItems.map((item) => _buildItemTile(item, onItemTap)),

            8.h.verticalSpace,
          ],
        );
      },
    );
  }

  Widget _buildItemTile(ExpertiseItem item, Function(ExpertiseItem) onItemTap) {
    return InkWell(
      onTap: () => onItemTap(item),
      borderRadius: BorderRadius.circular(6.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(8.w),
        margin: EdgeInsets.only(bottom: 4.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.borderDefault),
          borderRadius: BorderRadius.circular(6.r),
          color: Colors.white,
        ),
        child: Text(
          item.displayText,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.3,
          ),
        ),
      ),
    );
  }
}
