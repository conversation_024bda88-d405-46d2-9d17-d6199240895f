import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_rounded_date_picker/flutter_rounded_date_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/extensions/date_time_extensions.dart';

class CustomDateField extends StatelessWidget {
  const CustomDateField({
    super.key,
    required this.label,
    required this.hintText,
    this.initialDate,
    this.onDateSelected,
    this.validator,
    this.readOnly = false,
    this.enabled = true,
    this.isRequired = false,
    this.firstDate,
    this.lastDate,
  });

  final String label;
  final String hintText;
  final DateTime? initialDate;
  final void Function(DateTime)? onDateSelected;
  final String? Function(String?)? validator;
  final bool readOnly;
  final bool enabled;
  final bool isRequired;
  final DateTime? firstDate;
  final DateTime? lastDate;

  @override
  Widget build(BuildContext context) {
    final TextEditingController controller = TextEditingController(
      text: initialDate?.toThaiDateString() ?? '',
    );

    return TextFormField(
      controller: controller,
      validator: validator,
      readOnly: true,
      enabled: enabled,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5.sp,
      ),
      onTap:
          readOnly || !enabled
              ? null
              : () async {
                final DateTime? picked = await _showDatePicker(context);
                if (picked != null && onDateSelected != null) {
                  controller.text = picked.toThaiDateString();
                  onDateSelected!(picked);
                }
              },
      decoration: InputDecoration(
        label:
            isRequired
                ? RichText(
                  text: TextSpan(
                    text: label,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textDefaultDark,
                      height: 1.5.sp,
                      fontFamily: AppFonts.kanit,
                    ),
                    children: [
                      TextSpan(
                        text: " *",
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.critical,
                          height: 1.5.sp,
                          fontFamily: AppFonts.kanit,
                        ),
                      ),
                    ],
                  ),
                )
                : Text(
                  label,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textDefaultDark,
                    height: 1.5.sp,
                  ),
                ),
        hintText: hintText,
        hintStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.hintText,
          height: 1.5.sp,
        ),
        errorStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.critical,
          height: 1.5.sp,
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: const BorderSide(
            color: AppColors.borderDefault,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: const BorderSide(
            color: AppColors.borderDefault,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: const BorderSide(
            color: AppColors.borderSecondary,
            width: 1,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: const BorderSide(color: AppColors.critical, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: const BorderSide(color: AppColors.critical, width: 1),
        ),
        filled: true,
        fillColor: AppColors.inputBackgroundColor,
        suffixIcon: Padding(
          padding: EdgeInsets.all(18.r),
          child: SvgPicture.asset(
            'assets/icons/calendar.svg',
            width: 16.r,
            height: 16.r,
            colorFilter: const ColorFilter.mode(
              AppColors.iconDefault,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }

  Future<DateTime?> _showDatePicker(BuildContext context) async {
    final now = DateTime.now();
    return showRoundedDatePicker(
      context: context,
      initialDate: initialDate ?? now,
      firstDate: firstDate ?? DateTime(now.year - 5),
      lastDate: lastDate ?? DateTime(now.year + 1),
      borderRadius: 20.r,
      height: min(450.h, 0.8.sh),
      theme: ThemeData(
        primaryColor: AppColors.buttonPrimary,
        highlightColor: AppColors.highlightColor,
        fontFamily: AppFonts.notoSansThai,
        dialogTheme: const DialogTheme(
          backgroundColor: AppColors.inputBackgroundColor,
        ),
      ),
      fontFamily: AppFonts.notoSansThai,
      locale: const Locale("th", "TH"),
      era: EraMode.BUDDHIST_YEAR,
      styleDatePicker: MaterialRoundedDatePickerStyle(
        textStyleYearButton: TextStyle(
          fontSize: 28.sp,
          color: AppColors.textDefaultWhite,
        ),
        textStyleDayButton: TextStyle(
          fontSize: 24.sp,
          color: AppColors.textDefaultWhite,
          fontWeight: FontWeight.w400,
        ),
        textStyleDayHeader: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textSubdude,
          fontWeight: FontWeight.w400,
        ),
        textStyleCurrentDayOnCalendar: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w400,
        ),
        textStyleDayOnCalendar: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textDefaultDark,
        ),
        textStyleDayOnCalendarSelected: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textDefaultWhite,
          fontWeight: FontWeight.w400,
        ),
        textStyleMonthYearHeader: TextStyle(
          fontSize: 16.sp,
          color: AppColors.textDefaultDark,
          fontWeight: FontWeight.w400,
        ),
        decorationDateSelected: const BoxDecoration(
          color: AppColors.buttonPrimary,
          shape: BoxShape.circle,
        ),
        backgroundPicker: Colors.white,
        backgroundActionBar: AppColors.surfaceDefault,
        backgroundHeaderMonth: AppColors.surfaceDefault,
        textStyleButtonPositive: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 18.sp,
          height: 1.5.sp,
          color: AppColors.textPrimary,
        ),
        textStyleButtonNegative: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 18.sp,
          height: 1.5.sp,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }
}
