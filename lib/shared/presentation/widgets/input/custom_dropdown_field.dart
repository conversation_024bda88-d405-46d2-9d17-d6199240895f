import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

class CustomDropdownField<T> extends StatelessWidget {
  const CustomDropdownField({
    super.key,
    required this.onChange,
    required this.items,
    required this.hintText,
    required this.displayText,
    required this.label,
    this.initialValue,
    this.selectedValue,
    this.validator,
    this.isRequired = false,
  });

  final void Function(T) onChange;
  final T? initialValue;
  final List<T> items;
  final T? selectedValue;
  final String hintText;
  final String label;
  final String Function(T) displayText;
  final String? Function()? validator;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return FormField<T>(
      initialValue: selectedValue,
      validator: (_) => validator != null ? validator!() : null,
      builder: (FormFieldState<T> field) {
        final isInValid = field.hasError;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(
              width: double.infinity,
              child: DropdownMenu<T>(
                initialSelection: selectedValue ?? initialValue,
                onSelected: (T? value) {
                  if (value != null) {
                    onChange(value);
                    field.didChange(value);
                  }
                },
                errorText: isInValid ? field.errorText : null,
                textStyle: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                  height: 1.5.sp,
                ),
                menuStyle: MenuStyle(
                  backgroundColor: WidgetStateProperty.all(
                    AppColors.inputBackgroundColor,
                  ),
                  elevation: WidgetStateProperty.all(4),
                  shape: WidgetStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
                inputDecorationTheme: InputDecorationTheme(
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 8.h,
                  ),
                  errorStyle: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.critical,
                    height: 1.5.sp,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: const BorderSide(
                      color: AppColors.borderDefault,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: BorderSide(
                      color:
                          isInValid
                              ? AppColors.critical
                              : AppColors.borderDefault,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: const BorderSide(
                      color: AppColors.borderDefault,
                      width: 1,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: const BorderSide(
                      color: AppColors.critical,
                      width: 1,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    borderSide: const BorderSide(
                      color: AppColors.critical,
                      width: 1,
                    ),
                  ),
                  filled: true,
                  fillColor: AppColors.inputBackgroundColor,
                ),
                trailingIcon: const Icon(
                  Icons.expand_more,
                  color: AppColors.iconDefault,
                ),
                selectedTrailingIcon: const Icon(
                  Icons.expand_less,
                  color: AppColors.iconDefault,
                ),
                dropdownMenuEntries:
                    items.map((T item) {
                      return DropdownMenuEntry<T>(
                        value: item,
                        label: displayText(item),
                        style: MenuItemButton.styleFrom(
                          textStyle: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textDefaultDark,
                            height: 1.5.sp,
                          ),
                        ),
                      );
                    }).toList(),
                label:
                    isRequired
                        ? RichText(
                          text: TextSpan(
                            text: label,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textDefaultDark,
                              height: 1.5.sp,
                              fontFamily: AppFonts.kanit,
                            ),
                            children: [
                              TextSpan(
                                text: " *",
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.critical,
                                  height: 1.5.sp,
                                  fontFamily: AppFonts.kanit,
                                ),
                              ),
                            ],
                          ),
                        )
                        : Text(
                          label,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textDefaultDark,
                            height: 1.5.sp,
                          ),
                        ),
                width: MediaQuery.of(context).size.width,
                menuHeight: 250.h,
              ),
            ),
          ],
        );
      },
    );
  }
}
