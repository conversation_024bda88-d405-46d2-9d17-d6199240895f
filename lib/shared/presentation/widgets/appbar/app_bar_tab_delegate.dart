import 'package:flutter/material.dart';
import 'package:mcdc/core/constants/app_colors.dart';

/// A SliverPersistentHeaderDelegate implementation for TabBar widgets.
///
/// This delegate is used to create a persistent header that stays pinned
/// at the top of the screen when scrolling, typically used for tab bars
/// in a NestedScrollView.
class AppBarTabDelegate extends SliverPersistentHeaderDelegate {
  /// The TabBar widget to display in the header.
  final TabBar tabBar;

  /// Creates an [AppBarTabDelegate] with the given TabBar.
  AppBarTabDelegate(this.tabBar);

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: AppColors.backgroundColor, child: tabBar);
  }

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  bool shouldRebuild(covariant AppBarTabDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
