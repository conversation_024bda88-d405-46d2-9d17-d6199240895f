import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

class AppBarCommon extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const AppBarCommon({
    super.key,
    required this.title,
    this.onBackPressed,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: preferredSize,
      child: AppBar(
        title: Text(
          title,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        centerTitle: false,
        titleSpacing: 0,
        leading: IconButton(
          icon: SvgPicture.asset(
            'assets/icons/arrow_left.svg',
            width: 24.w,
            height: 24.w,
            colorFilter: const ColorFilter.mode(
              AppColors.iconDefault,
              BlendMode.srcIn,
            ),
          ),
          highlightColor: AppColors.highlightColor,
          splashColor: AppColors.splashColor,
          onPressed: onBackPressed ?? () => context.router.maybePop(),
        ),
        actions: actions,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            /* gradient: LinearGradient(
              colors: [Color(0xFFCED3FF), Color(0xFFF6F8FF)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ) */
            color: AppColors.backgroundDefault,
          ),
        ),
        elevation: 0,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 16.h);
}
