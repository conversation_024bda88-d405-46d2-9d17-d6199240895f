import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class SliverAppBarHome extends StatelessWidget {
  final String title;
  final String? fontFamily;

  const SliverAppBarHome({super.key, required this.title, this.fontFamily});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      pinned: false,
      centerTitle: false,
      automaticallyImplyLeading: false,
      actions: const <Widget>[],
      surfaceTintColor: Colors.white,
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(32.h),
        child: Container(),
      ),
      flexibleSpace: Stack(
        children: [
          _buildBackground(),
          Column(
            children: [
              // Top section with status bar
              60.h.verticalSpace, // Status bar space
              // Header with title and action buttons
              Padding(
                padding: EdgeInsets.fromLTRB(16.w, 17.h, 16.w, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: fontFamily,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textDefaultWhite,
                        height: 1.5,
                      ),
                    ),
                    _buildActionButtons(context),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        decoration: const BoxDecoration(color: AppColors.surfacePrimarySoft),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        _buildActionButton(
          iconPath: 'assets/icons/message_dots_new.svg',
          onPressed: () => context.router.pushPath('/chat'),
        ),
        SizedBox(width: 8.w),
        _buildActionButton(
          iconPath: 'assets/icons/bell_new.svg',
          onPressed: () => context.router.pushPath('/notification'),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String iconPath,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 40.w,
      height: 32.h,
      decoration: const BoxDecoration(color: Colors.transparent),
      child: IconButton(
        onPressed: onPressed,
        iconSize: 24.w,
        icon: SvgPicture.asset(
          iconPath,
          width: 24.r,
          height: 24.r,
          colorFilter: const ColorFilter.mode(
            AppColors.iconWhite,
            BlendMode.srcIn,
          ),
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
}
