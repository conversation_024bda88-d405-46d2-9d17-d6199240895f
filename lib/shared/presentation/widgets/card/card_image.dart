import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/shared/presentation/widgets/error/image_error.dart';

class CardImage extends StatelessWidget {
  const CardImage({super.key, required this.imageUrl, this.width, this.height});

  final String imageUrl;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return (imageUrl.toLowerCase().endsWith('.svg')
        ? SvgPicture.network(
          imageUrl,
          width: width ?? 90.w,
          height: height ?? 90.w,
          fit: BoxFit.contain,
          placeholderBuilder:
              (BuildContext context) => const CircularProgressIndicator(),
          semanticsLabel: 'Logo',
          allowDrawingOutsideViewBox: true,
        )
        : Image.network(
          imageUrl,
          width: width ?? 90.w,
          height: height ?? 90.w,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            if (kDebugMode) {
              developer.log('Error loading image: $error', name: 'IMAGE_DEBUG');
            }
            return ImageError(width: width ?? 90.w, height: height ?? 90.w);
          },
        ));
  }
}
