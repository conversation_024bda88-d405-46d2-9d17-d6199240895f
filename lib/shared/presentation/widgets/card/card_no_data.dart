import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class CardNoData extends StatelessWidget {
  const CardNoData({super.key, this.title});

  final String? title;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      alignment: Alignment.center,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/images/no_data.svg',
              width: 161.w,
              height: 161.w,
              fit: BoxFit.contain,
            ),
            14.verticalSpace,
            Text(
              title ?? l10n.noData,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textSubdude,
                height: 1.5.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
