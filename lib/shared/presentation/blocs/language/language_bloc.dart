import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_event.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_state.dart';

class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  LanguageBloc() : super(const LanguageState.initial()) {
    on<ChangeLanguageEvent>(_onChangeLanguage);
    on<LoadSavedLanguageEvent>(_onLoadSavedLanguage);
  }

  void _onChangeLanguage(
    ChangeLanguageEvent event,
    Emitter<LanguageState> emit,
  ) {
    emit(state.copyWith(locale: event.locale));
    // TODO: Save to local storage when implemented
  }

  void _onLoadSavedLanguage(
    LoadSavedLanguageEvent event,
    Emitter<LanguageState> emit,
  ) {
    // TODO: Load from local storage when implemented
    // For now, keep the default Thai locale
    emit(const LanguageState.initial());
  }

  // Helper methods for easy access
  bool get isThaiSelected => state.locale.languageCode == 'th';
  bool get isEnglishSelected => state.locale.languageCode == 'en';

  // Supported locales
  static const List<Locale> supportedLocales = [Locale('th'), Locale('en')];
}
