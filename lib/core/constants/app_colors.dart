import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  static const primary = Color(0xFFFFFFFF);
  // static const primaryDark = Color(0xFF1F3C88);
  // static const primaryLight = Color(0xFF5572F7);
  static const secondary = Color(0xFF818BFF);
  static const backgroundColor = Color(0xFFFFFFFF);
  static const backgroundDefault = Color(0xFFFFFFFF);
  static const backgroundColorSecondary = Color(0xFFF3F5F7);
  static const inputBackgroundColor = Color(0xFFFFFFFF);
  static const splashColor = Color(0x80818BFF); // 80 = 50% opacity
  static const highlightColor = Color(0x80818BFF); // 80 = 50% opacity
  static const titleColor = Color(0xFF404788);
  static const primaryText = Color(0xFF32373F);
  static const textPrimary = Color(0xFF0A4C9A);
  static const textSecondary = Color(0xFF818BFF);
  static const textDefaultWhite = Color(0xFFFFFFFF);
  static const textDefault = Color(0xFF0A3A57);
  static const textDefaultDark = Color(0xFF36475A);
  static const textSubdude = Color(0xFF86868B);
  static const textWarning = Color(0xFFFFC115);
  static const textSuccess = Color(0xFF66C215);
  static const textSuccessDark = Color(0xFF228A00);
  static const textCritical = Color(0xFFEF3E4C);
  static const labelText = Color(0xFF939595);
  static const hintText = Color(0xFF9E9E9E);
  static const redText = Color(0xFFEF4D56);
  static const critical = Color(0xFFEF3E4C);
  static const borderDefault = Color(0xFFD2D2D7);
  static const borderPrimary = Color(0xFF0A4C9A);
  static const borderSecondary = Color(0xFF818BFF);
  static const borderSubdude = Color(0xFFE5E6E6);
  static const disActiveColor = Color(0xFFF4F5F9);
  static const topBar = Color(0xFF455CC7);
  static const topBarLight = Color(0xFFDDDFFF);
  static const dividerColor = Color(0xFFD8D9D9);
  static const buttonPrimary = Color(0xFF0A4C9A);
  static const buttonSecondary = Color(0xFFFFFFFF);
  static const surfaceDefault = Color(0xFFF8F8F8);
  static const surfacePrimary = Color(0xFF0A4C9A);
  static const surfacePrimarySoft = Color(0xFF1F6AC3);
  static const surfacePrimarySubdude = Color(0xFFECF4FF);
  static const surfaceSecondary = Color(0xFF818BFF);
  static const surfaceSubdude = Color(0xFFF6F7F8);
  static const surfaceSecondarySubdude = Color(0xFFEBEBFF);
  static const surfaceCriticalSoft = Color(0xFFF75965);
  static const surfaceWarning = Color(0xFFFFC115);
  static const surfaceSuccess = Color(0xFF66A73E);
  static const iconDefault = Color(0xFF36475A);
  static const iconPrimary = Color(0xFF0A4C9A);
  static const iconSecondary = Color(0xFF818BFF);
  static const iconCritical = Color(0xFFEF3E4C);
  static const iconSubdude = Color(0xFF939595);
  static const iconWhite = Color(0xFFFFFFFF);
  static const laoding = Color(0xFF404688);
  static const secondarySubdude = Color(0xFFEBEBFF);
  static const surfaceProgressIndicator = Color(0xFFE2EFFF);
}
