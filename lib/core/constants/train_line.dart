/**
 * Constants for train line data used in the application.
 * This file contains definitions for metro train lines and inter-city train lines.
 */

/// Metro train lines with their title, colors and image paths
const List<Map<String, String>> metroTrainLines = [
  {
    'title': 'รถไฟฟ้าสายสีเขียวอ่อน',
    'iconColor': "#66B446",
    'iconBackgroundColor': "#F1F9EA",
    'img_path': 'assets/images/map_light_green_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีเขียวเข้ม',
    'iconColor': "#1A8F45",
    'iconBackgroundColor': "#B1E1C3",
    'img_path': 'assets/images/map_dark_green_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีน้ำเงิน',
    'iconColor': "#126DB0",
    'iconBackgroundColor': "#D1ECFF",
    'img_path': 'assets/images/map_blue_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีม่วง',
    'iconColor': "#5B2E81",
    'iconBackgroundColor': "#F0DFFF",
    'img_path': 'assets/images/map_purple_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีชมพู',
    'iconColor': "#DD61A2",
    'iconBackgroundColor': "#FFD7EC",
    'img_path': 'assets/images/map_pink_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีเหลือง',
    'iconColor': "#FECB4D",
    'iconBackgroundColor': "#FFF5DC",
    'img_path': 'assets/images/map_yellow_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีส้ม',
    'iconColor': "#EE7535",
    'iconBackgroundColor': "#FFDDCC",
    'img_path': 'assets/images/map_orange_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีแดงเข้ม',
    'iconColor': "#BD2030",
    'iconBackgroundColor': "#FFC9CF",
    'img_path': 'assets/images/map_light_red_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีแดงอ่อน',
    'iconColor': "#EF6470",
    'iconBackgroundColor': "#FFDCDF",
    'img_path': 'assets/images/map_dark_red_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายท่าอากาศยาน',
    'iconColor': "#7E1013",
    'iconBackgroundColor': "#F8989B",
    'img_path': 'assets/images/map_airport_link_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีทอง',
    'iconColor': "#CB982E",
    'iconBackgroundColor': "#FFE9BC",
    'img_path': 'assets/images/map_golden_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีน้ำตาล',
    'iconColor': "#764C29",
    'iconBackgroundColor': "#E3C8B3",
    'img_path': 'assets/images/map_brown_line.png'
  },
  {
    'title': 'รถไฟฟ้าสายสีเทา',
    'iconColor': "#838487",
    'iconBackgroundColor': "#E0E0E0",
    'img_path': 'assets/images/map_gray_line.png'
  },
];

/// Inter-city train lines with their title and image paths
const List<Map<String, String>> intercityTrainLines = [
  {
    'title': 'รถไฟฟ้าสายเหนือ',
    'iconColor': "#818BFF",
    'iconBackgroundColor': "#EBEBFF",
    'img_path': 'assets/images/map_northern_railway.png'
  },
  {
    'title': 'รถไฟฟ้าสายใต้',
    'iconColor': "#818BFF",
    'iconBackgroundColor': "#EBEBFF",
    'img_path': 'assets/images/map_southern_railway.png'
  },
  {
    'title': 'รถไฟฟ้าสายตะวันออกเฉียงเหนือ',
    'iconColor': "#818BFF",
    'iconBackgroundColor': "#EBEBFF",
    'img_path': 'assets/images/map_northeastern_railway.png'
  },
  {
    'title': 'รถไฟฟ้าสายตะวันออก',
    'iconColor': "#818BFF",
    'iconBackgroundColor': "#EBEBFF",
    'img_path': 'assets/images/map_eastern_railway.png'
  },
];

/// Map images for different map types
const List<Map<String, String>> mapImages = [
  {'title': 'แผนที่รถไฟฟ้า', 'image': 'assets/images/metro_map.png'},
  {'title': 'แผนที่รถไฟระหว่างเมือง', 'image': 'assets/images/thai_map.png'},
];
