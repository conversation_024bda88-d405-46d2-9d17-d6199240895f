import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'app_colors.dart';
import 'app_fonts.dart';
import 'styles.dart';

class AppTheme {
  static ThemeData appTheme() {
    return ThemeData(
      fontFamily: AppFonts.notoSansThai,
      colorScheme: const ColorScheme.light().copyWith(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
      ),
      // This makes the visual density adapt to the platform that you run
      // the app on. For desktop platforms, the controls will be smaller and
      // closer together (more dense) than on mobile platforms.
      visualDensity: VisualDensity.adaptivePlatformDensity,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonPrimary,
          foregroundColor: Colors.white,
          elevation: 1,
          shadowColor: const Color(0x0D2A3647),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          textStyle: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontSize: 16.sp,
            height: 1.5.sp,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderSide: const BorderSide(
            color: AppColors.borderDefault,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(8.0),
        ),
        labelStyle: kLabelStyle,
        hintStyle: kHintStyle,
        contentPadding: const EdgeInsets.all(8.0),
      ),
      appBarTheme: const AppBarTheme(elevation: 0.0),
      splashColor: AppColors.splashColor,
      highlightColor: AppColors.highlightColor,
      splashFactory: InkRipple.splashFactory,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: AppColors.secondary,
        selectionHandleColor: AppColors.secondary,
        selectionColor: AppColors.highlightColor,
      ),
      // This is to prevent the page from being frozen when the user navigates
      //https://github.com/flutter/flutter/issues/119897#issuecomment-1435650406
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: <TargetPlatform, PageTransitionsBuilder>{
          TargetPlatform.android: ZoomPageTransitionsBuilder(
            allowEnterRouteSnapshotting: false,
          ),
        },
      ),
    );
  }
}
