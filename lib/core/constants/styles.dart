import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_fonts.dart';

const kTitleStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  color: AppColors.titleColor,
  fontSize: 24.0,
  fontWeight: FontWeight.w600,
  height: 1.5,
);

const kSubtitleStyle = TextStyle(
  color: AppColors.primaryText,
  fontSize: 18.0,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kHeaderStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  color: AppColors.textDefaultDark,
  fontSize: 20.0,
  fontWeight: FontWeight.w600,
  height: 1.5,
);

const kBodyStyle = TextStyle(
  color: AppColors.primaryText,
  fontSize: 16,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kBodyBoldStyle = TextStyle(
  color: AppColors.primaryText,
  fontSize: 16,
  fontWeight: FontWeight.bold,
  height: 1.5,
);

const kBodyHighlightStyle = TextStyle(
  color: AppColors.primary,
  fontSize: 16,
  fontWeight: FontWeight.w600,
  height: 1.5,
);

const kSmallStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  color: AppColors.textPrimary,
  fontSize: 14,
  fontWeight: FontWeight.w400,
  height: 1.5,
);

const kLabelStyle = TextStyle(
  fontSize: 16,
  fontFamily: AppFonts.notoSansThai,
  color: AppColors.labelText,
  fontWeight: FontWeight.w400,
  height: 1.5,
);

const kWhiteLabelStyle = TextStyle(
  fontSize: 16,
  color: Colors.white,
  fontWeight: FontWeight.w400,
  height: 1.5,
);

const kHintStyle = TextStyle(
  color: AppColors.hintText,
  fontSize: 16.0,
  fontWeight: FontWeight.normal,
);

const kButtonTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 20.0,
  fontWeight: FontWeight.w600,
);

const kDialogTitleStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  color: AppColors.primaryText,
  fontSize: 22.0,
  fontWeight: FontWeight.w600,
  height: 1.5,
);

const kDialogBodyStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  color: AppColors.primaryText,
  fontSize: 16,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kSelectedLabelStyle = TextStyle(
  fontFamily: AppFonts.kanit,
  color: AppColors.secondary,
  fontSize: 10,
  fontWeight: FontWeight.normal,
  height: 1.7,
);

const kUnselectedLabelStyle = TextStyle(
  fontFamily: AppFonts.kanit,
  color: AppColors.labelText,
  fontSize: 10,
  fontWeight: FontWeight.normal,
  height: 1.7,
);

const kPageTitleStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  color: Colors.white,
  fontSize: 17.0,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kDashboardTopicStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 16,
  color: AppColors.primaryText,
  fontWeight: FontWeight.normal,
  height: 1.0,
);

const kDashboardLabelStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 12,
  color: AppColors.labelText,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kDashboardBigNumStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 30,
  color: AppColors.primary,
  fontWeight: FontWeight.w700,
  height: 1.0,
);

const kDashboardNormalNumStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 20,
  color: AppColors.primaryText,
  fontWeight: FontWeight.w700,
  height: 1,
);

const kDashboardBodyBoldStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 14,
  color: AppColors.primaryText,
  fontWeight: FontWeight.w700,
  height: 1,
);

const kColorInfoLabel = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 14,
  color: AppColors.primaryText,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kColorInfoLabelBold = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 14,
  color: AppColors.primaryText,
  fontWeight: FontWeight.bold,
  height: 1,
);

const kColorInfoValue = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 14,
  color: AppColors.labelText,
  fontWeight: FontWeight.normal,
  height: 1,
);

const kNormalLabelStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 14,
  color: AppColors.labelText,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kNormalInputStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 14,
  color: AppColors.primaryText,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kCardTopicStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 16,
  color: AppColors.primaryText,
  fontWeight: FontWeight.bold,
  height: 1,
);

const kCardLabelStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 14,
  color: AppColors.primaryText,
  fontWeight: FontWeight.bold,
  height: 1.5,
);

const kCardValueStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 14,
  color: AppColors.labelText,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kTabTitleStyle = TextStyle(
  fontFamily: AppFonts.notoSansThai,
  fontSize: 16,
  color: AppColors.labelText,
  fontWeight: FontWeight.normal,
  height: 1.0,
);

const kDateStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  color: AppColors.primaryText,
  fontSize: 16,
  fontWeight: FontWeight.normal,
  height: 1.0,
);

const kDateHeaderStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  color: AppColors.labelText,
  fontSize: 14,
  fontWeight: FontWeight.normal,
  height: 1.0,
);

const kDisableDateStyle = TextStyle(
  fontFamily: AppFonts.sarabun,
  color: AppColors.labelText,
  fontSize: 16,
  fontWeight: FontWeight.normal,
  height: 1.0,
);

const kListItemHeader = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 16,
  color: AppColors.primaryText,
  fontWeight: FontWeight.bold,
  height: 1.5,
);

const kListItemSubHeader = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 12,
  color: AppColors.labelText,
  fontWeight: FontWeight.normal,
  height: 1.5,
);

const kTextLabelHighlight = TextStyle(
  fontFamily: AppFonts.sarabun,
  fontSize: 16,
  color: AppColors.primaryText,
  fontWeight: FontWeight.normal,
  height: 1.0,
);
