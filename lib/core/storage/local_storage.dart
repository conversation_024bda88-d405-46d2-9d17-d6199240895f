/// Abstract interface for secure storage operations
abstract class LocalStorage {
  /// Store a value securely
  Future<void> store(String key, String value);

  /// Retrieve a value securely
  Future<String?> retrieve(String key);

  /// Delete a value
  Future<void> delete(String key);

  /// Clear all stored values
  Future<void> clear();

  /// Check if a key exists
  Future<bool> containsKey(String key);
}
