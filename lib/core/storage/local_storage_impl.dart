import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:mcdc/core/storage/local_storage.dart';

/// Implementation of [LocalStorage] using flutter_secure_storage
class LocalStorageImpl implements LocalStorage {
  static const _storage = FlutterSecureStorage();

  @override
  Future<void> store(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  @override
  Future<String?> retrieve(String key) async {
    return await _storage.read(key: key);
  }

  @override
  Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }

  @override
  Future<void> clear() async {
    await _storage.deleteAll();
  }

  @override
  Future<bool> contains<PERSON>ey(String key) async {
    return await _storage.containsKey(key: key);
  }
}