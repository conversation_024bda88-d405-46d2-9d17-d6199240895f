import 'package:get_it/get_it.dart';
import 'package:mcdc/features/user/data/repositories/user_repository_impl.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';
import 'package:mcdc/features/poll/data/repositories/feedback_repository_impl.dart';
import 'package:mcdc/features/user/data/repositories/user_profile_repository_impl.dart';
import 'package:mcdc/features/poll/domain/repositories/feedback_repository.dart';
import 'package:mcdc/features/user/domain/repositories/user_profile_repository.dart';
import 'package:mcdc/features/otp/data/repositories/otp_repository_impl.dart';
import 'package:mcdc/features/otp/domain/repositories/otp_repository.dart';
import 'package:mcdc/features/master_data/data/repositories/master_data_repository_impl.dart';
import 'package:mcdc/features/master_data/domain/repositories/master_data_repository.dart';

/// Initialize all repositories used in the application
void initRepositories(GetIt sl) {
  // User Repository
  sl.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(networkInfo: sl(), userApiDataSource: sl()),
  );

  // Feedback Repository
  sl.registerLazySingleton<FeedbackRepository>(
    () => FeedbackRepositoryImpl(localDataSource: sl()),
  );

  // User Profile Repository
  sl.registerLazySingleton<UserProfileRepository>(
    () => UserProfileRepositoryImpl(localDataSource: sl()),
  );

  // OTP Repository
  sl.registerLazySingleton<OtpRepository>(
    () => OtpRepositoryImpl(apiDataSource: sl(), networkInfo: sl()),
  );

  // Master Data Repository
  sl.registerLazySingleton<MasterDataRepository>(
    () => MasterDataRepositoryImpl(apiDataSource: sl(), networkInfo: sl()),
  );
}
