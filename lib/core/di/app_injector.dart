import 'package:get_it/get_it.dart';
import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_client_impl.dart';
import 'package:mcdc/core/api/data_connection_checker.dart';
import 'package:mcdc/core/api/network_info.dart';
import 'package:mcdc/core/di/cubits_injector.dart';
import 'package:mcdc/core/di/datasources_injector.dart';
import 'package:mcdc/core/di/repository_injector.dart';
import 'package:mcdc/core/di/usecases_injector.dart';
import 'package:mcdc/core/storage/local_storage.dart';
import 'package:mcdc/core/storage/local_storage_impl.dart';

/// GetIt is a service locator package named get_it that's predefined in pubspec.yaml under dependencies.
/// Behind the scenes, get_it keeps track of all your registered objects.
/// The service locator is a global singleton that you can access from anywhere within your app.
GetIt _sl = GetIt.instance;

class AppInjector {
  static final AppInjector _singleton = AppInjector._internal();

  factory AppInjector() => _singleton;

  /// The _internal construction is just a name often given to constructors
  /// that are private to the class (the name is not required to be ._internal
  /// you can create a private constructor using any Class._someName construction).
  AppInjector._internal();

  static T get<T extends Object>() => _sl<T>();

  /// Getter for service locator
  static GetIt get sl => _sl;

  /// This function is where you register your services. You should call it before you build the UI.
  /// That means calling it first thing in main.dart.
  static Future<void> init() async {
    // Core
    _initCore();
    // Bloc
    initCubits(_sl);
    // Use cases
    initUseCases(_sl);
    // Repository
    initRepositories(_sl);
    // Data sources
    initDataSources(_sl);
  }
}

void _initCore() {
  /*_sl.registerLazySingleton(() => InputValidator());*/
  _sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(_sl()));
  _sl.registerLazySingleton(() => DataConnectionChecker());
  _sl.registerLazySingleton<ApiClient>(() => ApiClientImpl(authStorage: _sl()));
  _sl.registerLazySingleton<LocalStorage>(() => LocalStorageImpl());
} // end of _initCommons
