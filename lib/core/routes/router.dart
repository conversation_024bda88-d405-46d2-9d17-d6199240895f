import 'package:auto_route/auto_route.dart';
import 'package:mcdc/core/routes/router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => const RouteType.material(); //.cupertino, .adaptive ..etc

  @override
  List<AutoRoute> get routes => [
    // MainPage is generated as MainRoute because
    // of the replaceInRouteName property
    AutoRoute(path: '/welcome', page: WelcomeRoute.page, initial: true),
    AutoRoute(path: '/login', page: LoginRoute.page),
    AutoRoute(path: '/forgot-password', page: ForgotPasswordRoute.page),
    AutoRoute(path: '/create-password', page: CreatePasswordRoute.page),
    AutoRoute(path: '/otp', page: OtpRoute.page),
    AutoRoute(path: '/my-profile', page: MyProfileRoute.page),
    AutoRoute(path: '/edit-profile', page: EditProfileRoute.page),
    AutoRoute(path: '/edit-password', page: EditPasswordRoute.page),
    AutoRoute(path: '/delete-account', page: DeleteAccountRoute.page),
    AutoRoute(path: '/main', page: MainRoute.page),
    AutoRoute(path: '/chat', page: ChatRoute.page),
    AutoRoute(path: '/pay-fee', page: PayFeeRoute.page),
    AutoRoute(path: '/qr-payment/:id', page: QrPaymentRoute.page),
    AutoRoute(path: '/privacy-policy', page: PrivacyPolicyRoute.page),
    AutoRoute(path: '/member-register', page: MemberRegisterRoute.page),
    AutoRoute(path: '/how-to-login-thai-id', page: HowToLoginThaiIdRoute.page),
    AutoRoute(
      path: '/suitable-consultant/:projectId',
      page: SuitableConsultantRoute.page,
    ),
    AutoRoute(path: '/consultant-info', page: ConsultantInfoRoute.page),
    AutoRoute(path: '/search', page: SearchHomeRoute.page),
    AutoRoute(
      path: '/search/project-results',
      page: SearchResultProjectRoute.page,
    ),
    AutoRoute(
      path: '/search/consult-results',
      page: SearchResultConsultRoute.page,
    ),
    AutoRoute(
      path: '/search/project-detail/:projectId',
      page: ProjectDetailRoute.page,
    ),
    AutoRoute(
      path: '/tracking/detail/:trackingId',
      page: TrackingDetailRoute.page,
    ),
    AutoRoute(path: '/service-info', page: ServiceInfoRoute.page),
    AutoRoute(path: '/notification', page: NotificationRoute.page),
    AutoRoute(path: '/news/:id', page: NewsDetailRoute.page),
    AutoRoute(path: '/faq', page: FaqRoute.page),
    AutoRoute(path: '/feedback-form', page: FeedbackFormRoute.page),
    AutoRoute(path: '/user-data-policy', page: UserDataPolicyRoute.page),
    AutoRoute(path: '/application-policy', page: ApplicationPolicyRoute.page),
    AutoRoute(path: '/about-us', page: AboutUsRoute.page),
  ];

  @override
  List<AutoRouteGuard> get guards => [
    // optionally add root guards here
  ];
}
