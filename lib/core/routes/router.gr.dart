// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i35;
import 'package:flutter/material.dart' as _i36;
import 'package:mcdc/core/enums/user_type.dart' as _i37;
import 'package:mcdc/features/chat/presentation/chat_page.dart' as _i3;
import 'package:mcdc/features/consultant_matching/presentation/consultant_info_page.dart'
    as _i4;
import 'package:mcdc/features/consultant_matching/presentation/matching_home_page.dart'
    as _i16;
import 'package:mcdc/features/consultant_matching/presentation/suitable_consultant_page.dart'
    as _i30;
import 'package:mcdc/features/dashboard/presentation/dashboard_home_page.dart'
    as _i6;
import 'package:mcdc/features/dashboard/presentation/service_info_page.dart'
    as _i29;
import 'package:mcdc/features/faq/presentation/faq_page.dart' as _i10;
import 'package:mcdc/features/main/presentation/main_page.dart' as _i15;
import 'package:mcdc/features/news/domain/entities/news.dart' as _i38;
import 'package:mcdc/features/news/presentation/news_detail_page.dart' as _i19;
import 'package:mcdc/features/notification/presentation/notification_page.dart'
    as _i20;
import 'package:mcdc/features/otp/presentation/otp_page.dart' as _i21;
import 'package:mcdc/features/payment/presentation/pay_fee_page.dart' as _i22;
import 'package:mcdc/features/payment/presentation/qr_payment_page.dart'
    as _i25;
import 'package:mcdc/features/policy/presentation/about_us_page.dart' as _i1;
import 'package:mcdc/features/policy/presentation/application_policy_page.dart'
    as _i2;
import 'package:mcdc/features/policy/presentation/user_data_policy_page.dart'
    as _i33;
import 'package:mcdc/features/poll/presentation/feedback_form_page.dart'
    as _i11;
import 'package:mcdc/features/search/presentation/project_detail_page.dart'
    as _i24;
import 'package:mcdc/features/search/presentation/search_home_page.dart'
    as _i26;
import 'package:mcdc/features/search/presentation/search_result_consult_page.dart'
    as _i27;
import 'package:mcdc/features/search/presentation/search_result_project_page.dart'
    as _i28;
import 'package:mcdc/features/tracking/presentation/components/tracking_status_card.dart'
    as _i39;
import 'package:mcdc/features/tracking/presentation/tracking_detail_page.dart'
    as _i31;
import 'package:mcdc/features/tracking/presentation/tracking_home_page.dart'
    as _i32;
import 'package:mcdc/features/user/presentation/create_password_page.dart'
    as _i5;
import 'package:mcdc/features/user/presentation/delete_account_page.dart'
    as _i7;
import 'package:mcdc/features/user/presentation/edit_password_page.dart' as _i8;
import 'package:mcdc/features/user/presentation/edit_profile_page.dart' as _i9;
import 'package:mcdc/features/user/presentation/forgot_password_page.dart'
    as _i12;
import 'package:mcdc/features/user/presentation/how_to_login_thai_id.dart'
    as _i13;
import 'package:mcdc/features/user/presentation/login_page.dart' as _i14;
import 'package:mcdc/features/user/presentation/member_register_page.dart'
    as _i17;
import 'package:mcdc/features/user/presentation/my_profile_page.dart' as _i18;
import 'package:mcdc/features/user/presentation/privacy_policy_page.dart'
    as _i23;
import 'package:mcdc/features/user/presentation/welcome_page.dart' as _i34;

/// generated route for
/// [_i1.AboutUsPage]
class AboutUsRoute extends _i35.PageRouteInfo<void> {
  const AboutUsRoute({List<_i35.PageRouteInfo>? children})
    : super(AboutUsRoute.name, initialChildren: children);

  static const String name = 'AboutUsRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i1.AboutUsPage();
    },
  );
}

/// generated route for
/// [_i2.ApplicationPolicyPage]
class ApplicationPolicyRoute extends _i35.PageRouteInfo<void> {
  const ApplicationPolicyRoute({List<_i35.PageRouteInfo>? children})
    : super(ApplicationPolicyRoute.name, initialChildren: children);

  static const String name = 'ApplicationPolicyRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i2.ApplicationPolicyPage();
    },
  );
}

/// generated route for
/// [_i3.ChatPage]
class ChatRoute extends _i35.PageRouteInfo<void> {
  const ChatRoute({List<_i35.PageRouteInfo>? children})
    : super(ChatRoute.name, initialChildren: children);

  static const String name = 'ChatRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i3.ChatPage();
    },
  );
}

/// generated route for
/// [_i4.ConsultantInfoPage]
class ConsultantInfoRoute extends _i35.PageRouteInfo<void> {
  const ConsultantInfoRoute({List<_i35.PageRouteInfo>? children})
    : super(ConsultantInfoRoute.name, initialChildren: children);

  static const String name = 'ConsultantInfoRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i4.ConsultantInfoPage();
    },
  );
}

/// generated route for
/// [_i5.CreatePasswordPage]
class CreatePasswordRoute extends _i35.PageRouteInfo<CreatePasswordRouteArgs> {
  CreatePasswordRoute({
    _i36.Key? key,
    required String email,
    required String otpToken,
    List<_i35.PageRouteInfo>? children,
  }) : super(
         CreatePasswordRoute.name,
         args: CreatePasswordRouteArgs(
           key: key,
           email: email,
           otpToken: otpToken,
         ),
         initialChildren: children,
       );

  static const String name = 'CreatePasswordRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<CreatePasswordRouteArgs>();
      return _i5.CreatePasswordPage(
        key: args.key,
        email: args.email,
        otpToken: args.otpToken,
      );
    },
  );
}

class CreatePasswordRouteArgs {
  const CreatePasswordRouteArgs({
    this.key,
    required this.email,
    required this.otpToken,
  });

  final _i36.Key? key;

  final String email;

  final String otpToken;

  @override
  String toString() {
    return 'CreatePasswordRouteArgs{key: $key, email: $email, otpToken: $otpToken}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CreatePasswordRouteArgs) return false;
    return key == other.key &&
        email == other.email &&
        otpToken == other.otpToken;
  }

  @override
  int get hashCode => key.hashCode ^ email.hashCode ^ otpToken.hashCode;
}

/// generated route for
/// [_i6.DashboardHomePage]
class DashboardHomeRoute extends _i35.PageRouteInfo<void> {
  const DashboardHomeRoute({List<_i35.PageRouteInfo>? children})
    : super(DashboardHomeRoute.name, initialChildren: children);

  static const String name = 'DashboardHomeRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i6.DashboardHomePage();
    },
  );
}

/// generated route for
/// [_i7.DeleteAccountPage]
class DeleteAccountRoute extends _i35.PageRouteInfo<void> {
  const DeleteAccountRoute({List<_i35.PageRouteInfo>? children})
    : super(DeleteAccountRoute.name, initialChildren: children);

  static const String name = 'DeleteAccountRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i7.DeleteAccountPage();
    },
  );
}

/// generated route for
/// [_i8.EditPasswordPage]
class EditPasswordRoute extends _i35.PageRouteInfo<void> {
  const EditPasswordRoute({List<_i35.PageRouteInfo>? children})
    : super(EditPasswordRoute.name, initialChildren: children);

  static const String name = 'EditPasswordRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i8.EditPasswordPage();
    },
  );
}

/// generated route for
/// [_i9.EditProfilePage]
class EditProfileRoute extends _i35.PageRouteInfo<void> {
  const EditProfileRoute({List<_i35.PageRouteInfo>? children})
    : super(EditProfileRoute.name, initialChildren: children);

  static const String name = 'EditProfileRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i9.EditProfilePage();
    },
  );
}

/// generated route for
/// [_i10.FaqPage]
class FaqRoute extends _i35.PageRouteInfo<void> {
  const FaqRoute({List<_i35.PageRouteInfo>? children})
    : super(FaqRoute.name, initialChildren: children);

  static const String name = 'FaqRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i10.FaqPage();
    },
  );
}

/// generated route for
/// [_i11.FeedbackFormPage]
class FeedbackFormRoute extends _i35.PageRouteInfo<void> {
  const FeedbackFormRoute({List<_i35.PageRouteInfo>? children})
    : super(FeedbackFormRoute.name, initialChildren: children);

  static const String name = 'FeedbackFormRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i11.FeedbackFormPage();
    },
  );
}

/// generated route for
/// [_i12.ForgotPasswordPage]
class ForgotPasswordRoute extends _i35.PageRouteInfo<void> {
  const ForgotPasswordRoute({List<_i35.PageRouteInfo>? children})
    : super(ForgotPasswordRoute.name, initialChildren: children);

  static const String name = 'ForgotPasswordRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i12.ForgotPasswordPage();
    },
  );
}

/// generated route for
/// [_i13.HowToLoginThaiIdPage]
class HowToLoginThaiIdRoute extends _i35.PageRouteInfo<void> {
  const HowToLoginThaiIdRoute({List<_i35.PageRouteInfo>? children})
    : super(HowToLoginThaiIdRoute.name, initialChildren: children);

  static const String name = 'HowToLoginThaiIdRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i13.HowToLoginThaiIdPage();
    },
  );
}

/// generated route for
/// [_i14.LoginPage]
class LoginRoute extends _i35.PageRouteInfo<LoginRouteArgs> {
  LoginRoute({
    _i36.Key? key,
    _i37.UserType? userType,
    List<_i35.PageRouteInfo>? children,
  }) : super(
         LoginRoute.name,
         args: LoginRouteArgs(key: key, userType: userType),
         initialChildren: children,
       );

  static const String name = 'LoginRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<LoginRouteArgs>(
        orElse: () => const LoginRouteArgs(),
      );
      return _i14.LoginPage(key: args.key, userType: args.userType);
    },
  );
}

class LoginRouteArgs {
  const LoginRouteArgs({this.key, this.userType});

  final _i36.Key? key;

  final _i37.UserType? userType;

  @override
  String toString() {
    return 'LoginRouteArgs{key: $key, userType: $userType}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LoginRouteArgs) return false;
    return key == other.key && userType == other.userType;
  }

  @override
  int get hashCode => key.hashCode ^ userType.hashCode;
}

/// generated route for
/// [_i15.MainPage]
class MainRoute extends _i35.PageRouteInfo<void> {
  const MainRoute({List<_i35.PageRouteInfo>? children})
    : super(MainRoute.name, initialChildren: children);

  static const String name = 'MainRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i15.MainPage();
    },
  );
}

/// generated route for
/// [_i16.MatchingHomePage]
class MatchingHomeRoute extends _i35.PageRouteInfo<void> {
  const MatchingHomeRoute({List<_i35.PageRouteInfo>? children})
    : super(MatchingHomeRoute.name, initialChildren: children);

  static const String name = 'MatchingHomeRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i16.MatchingHomePage();
    },
  );
}

/// generated route for
/// [_i17.MemberRegisterPage]
class MemberRegisterRoute extends _i35.PageRouteInfo<void> {
  const MemberRegisterRoute({List<_i35.PageRouteInfo>? children})
    : super(MemberRegisterRoute.name, initialChildren: children);

  static const String name = 'MemberRegisterRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i17.MemberRegisterPage();
    },
  );
}

/// generated route for
/// [_i18.MyProfilePage]
class MyProfileRoute extends _i35.PageRouteInfo<void> {
  const MyProfileRoute({List<_i35.PageRouteInfo>? children})
    : super(MyProfileRoute.name, initialChildren: children);

  static const String name = 'MyProfileRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i18.MyProfilePage();
    },
  );
}

/// generated route for
/// [_i19.NewsDetailPage]
class NewsDetailRoute extends _i35.PageRouteInfo<NewsDetailRouteArgs> {
  NewsDetailRoute({
    _i36.Key? key,
    _i38.News? news,
    List<_i35.PageRouteInfo>? children,
  }) : super(
         NewsDetailRoute.name,
         args: NewsDetailRouteArgs(key: key, news: news),
         initialChildren: children,
       );

  static const String name = 'NewsDetailRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NewsDetailRouteArgs>(
        orElse: () => const NewsDetailRouteArgs(),
      );
      return _i19.NewsDetailPage(key: args.key, news: args.news);
    },
  );
}

class NewsDetailRouteArgs {
  const NewsDetailRouteArgs({this.key, this.news});

  final _i36.Key? key;

  final _i38.News? news;

  @override
  String toString() {
    return 'NewsDetailRouteArgs{key: $key, news: $news}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! NewsDetailRouteArgs) return false;
    return key == other.key && news == other.news;
  }

  @override
  int get hashCode => key.hashCode ^ news.hashCode;
}

/// generated route for
/// [_i20.NotificationPage]
class NotificationRoute extends _i35.PageRouteInfo<void> {
  const NotificationRoute({List<_i35.PageRouteInfo>? children})
    : super(NotificationRoute.name, initialChildren: children);

  static const String name = 'NotificationRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i20.NotificationPage();
    },
  );
}

/// generated route for
/// [_i21.OtpPage]
class OtpRoute extends _i35.PageRouteInfo<OtpRouteArgs> {
  OtpRoute({
    _i36.Key? key,
    required String email,
    required String referenceCode,
    String? flowType,
    String? otpToken,
    List<_i35.PageRouteInfo>? children,
  }) : super(
         OtpRoute.name,
         args: OtpRouteArgs(
           key: key,
           email: email,
           referenceCode: referenceCode,
           flowType: flowType,
           otpToken: otpToken,
         ),
         initialChildren: children,
       );

  static const String name = 'OtpRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<OtpRouteArgs>();
      return _i21.OtpPage(
        key: args.key,
        email: args.email,
        referenceCode: args.referenceCode,
        flowType: args.flowType,
        otpToken: args.otpToken,
      );
    },
  );
}

class OtpRouteArgs {
  const OtpRouteArgs({
    this.key,
    required this.email,
    required this.referenceCode,
    this.flowType,
    this.otpToken,
  });

  final _i36.Key? key;

  final String email;

  final String referenceCode;

  final String? flowType;

  final String? otpToken;

  @override
  String toString() {
    return 'OtpRouteArgs{key: $key, email: $email, referenceCode: $referenceCode, flowType: $flowType, otpToken: $otpToken}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OtpRouteArgs) return false;
    return key == other.key &&
        email == other.email &&
        referenceCode == other.referenceCode &&
        flowType == other.flowType &&
        otpToken == other.otpToken;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      email.hashCode ^
      referenceCode.hashCode ^
      flowType.hashCode ^
      otpToken.hashCode;
}

/// generated route for
/// [_i22.PayFeePage]
class PayFeeRoute extends _i35.PageRouteInfo<void> {
  const PayFeeRoute({List<_i35.PageRouteInfo>? children})
    : super(PayFeeRoute.name, initialChildren: children);

  static const String name = 'PayFeeRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i22.PayFeePage();
    },
  );
}

/// generated route for
/// [_i23.PrivacyPolicyPage]
class PrivacyPolicyRoute extends _i35.PageRouteInfo<void> {
  const PrivacyPolicyRoute({List<_i35.PageRouteInfo>? children})
    : super(PrivacyPolicyRoute.name, initialChildren: children);

  static const String name = 'PrivacyPolicyRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i23.PrivacyPolicyPage();
    },
  );
}

/// generated route for
/// [_i24.ProjectDetailPage]
class ProjectDetailRoute extends _i35.PageRouteInfo<void> {
  const ProjectDetailRoute({List<_i35.PageRouteInfo>? children})
    : super(ProjectDetailRoute.name, initialChildren: children);

  static const String name = 'ProjectDetailRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i24.ProjectDetailPage();
    },
  );
}

/// generated route for
/// [_i25.QrPaymentPage]
class QrPaymentRoute extends _i35.PageRouteInfo<QrPaymentRouteArgs> {
  QrPaymentRoute({
    _i36.Key? key,
    required String paymentId,
    required String referenceCode1,
    required String referenceCode2,
    required String payerName,
    required String collectionDate,
    required String dueDate,
    required String amount,
    List<_i35.PageRouteInfo>? children,
  }) : super(
         QrPaymentRoute.name,
         args: QrPaymentRouteArgs(
           key: key,
           paymentId: paymentId,
           referenceCode1: referenceCode1,
           referenceCode2: referenceCode2,
           payerName: payerName,
           collectionDate: collectionDate,
           dueDate: dueDate,
           amount: amount,
         ),
         initialChildren: children,
       );

  static const String name = 'QrPaymentRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<QrPaymentRouteArgs>();
      return _i25.QrPaymentPage(
        key: args.key,
        paymentId: args.paymentId,
        referenceCode1: args.referenceCode1,
        referenceCode2: args.referenceCode2,
        payerName: args.payerName,
        collectionDate: args.collectionDate,
        dueDate: args.dueDate,
        amount: args.amount,
      );
    },
  );
}

class QrPaymentRouteArgs {
  const QrPaymentRouteArgs({
    this.key,
    required this.paymentId,
    required this.referenceCode1,
    required this.referenceCode2,
    required this.payerName,
    required this.collectionDate,
    required this.dueDate,
    required this.amount,
  });

  final _i36.Key? key;

  final String paymentId;

  final String referenceCode1;

  final String referenceCode2;

  final String payerName;

  final String collectionDate;

  final String dueDate;

  final String amount;

  @override
  String toString() {
    return 'QrPaymentRouteArgs{key: $key, paymentId: $paymentId, referenceCode1: $referenceCode1, referenceCode2: $referenceCode2, payerName: $payerName, collectionDate: $collectionDate, dueDate: $dueDate, amount: $amount}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! QrPaymentRouteArgs) return false;
    return key == other.key &&
        paymentId == other.paymentId &&
        referenceCode1 == other.referenceCode1 &&
        referenceCode2 == other.referenceCode2 &&
        payerName == other.payerName &&
        collectionDate == other.collectionDate &&
        dueDate == other.dueDate &&
        amount == other.amount;
  }

  @override
  int get hashCode =>
      key.hashCode ^
      paymentId.hashCode ^
      referenceCode1.hashCode ^
      referenceCode2.hashCode ^
      payerName.hashCode ^
      collectionDate.hashCode ^
      dueDate.hashCode ^
      amount.hashCode;
}

/// generated route for
/// [_i26.SearchHomePage]
class SearchHomeRoute extends _i35.PageRouteInfo<void> {
  const SearchHomeRoute({List<_i35.PageRouteInfo>? children})
    : super(SearchHomeRoute.name, initialChildren: children);

  static const String name = 'SearchHomeRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i26.SearchHomePage();
    },
  );
}

/// generated route for
/// [_i27.SearchResultConsultPage]
class SearchResultConsultRoute extends _i35.PageRouteInfo<void> {
  const SearchResultConsultRoute({List<_i35.PageRouteInfo>? children})
    : super(SearchResultConsultRoute.name, initialChildren: children);

  static const String name = 'SearchResultConsultRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i27.SearchResultConsultPage();
    },
  );
}

/// generated route for
/// [_i28.SearchResultProjectPage]
class SearchResultProjectRoute extends _i35.PageRouteInfo<void> {
  const SearchResultProjectRoute({List<_i35.PageRouteInfo>? children})
    : super(SearchResultProjectRoute.name, initialChildren: children);

  static const String name = 'SearchResultProjectRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i28.SearchResultProjectPage();
    },
  );
}

/// generated route for
/// [_i29.ServiceInfoPage]
class ServiceInfoRoute extends _i35.PageRouteInfo<void> {
  const ServiceInfoRoute({List<_i35.PageRouteInfo>? children})
    : super(ServiceInfoRoute.name, initialChildren: children);

  static const String name = 'ServiceInfoRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i29.ServiceInfoPage();
    },
  );
}

/// generated route for
/// [_i30.SuitableConsultantPage]
class SuitableConsultantRoute extends _i35.PageRouteInfo<void> {
  const SuitableConsultantRoute({List<_i35.PageRouteInfo>? children})
    : super(SuitableConsultantRoute.name, initialChildren: children);

  static const String name = 'SuitableConsultantRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i30.SuitableConsultantPage();
    },
  );
}

/// generated route for
/// [_i31.TrackingDetailPage]
class TrackingDetailRoute extends _i35.PageRouteInfo<TrackingDetailRouteArgs> {
  TrackingDetailRoute({
    _i36.Key? key,
    required _i39.TrackingStatusData trackingData,
    List<_i35.PageRouteInfo>? children,
  }) : super(
         TrackingDetailRoute.name,
         args: TrackingDetailRouteArgs(key: key, trackingData: trackingData),
         initialChildren: children,
       );

  static const String name = 'TrackingDetailRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TrackingDetailRouteArgs>();
      return _i31.TrackingDetailPage(
        key: args.key,
        trackingData: args.trackingData,
      );
    },
  );
}

class TrackingDetailRouteArgs {
  const TrackingDetailRouteArgs({this.key, required this.trackingData});

  final _i36.Key? key;

  final _i39.TrackingStatusData trackingData;

  @override
  String toString() {
    return 'TrackingDetailRouteArgs{key: $key, trackingData: $trackingData}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TrackingDetailRouteArgs) return false;
    return key == other.key && trackingData == other.trackingData;
  }

  @override
  int get hashCode => key.hashCode ^ trackingData.hashCode;
}

/// generated route for
/// [_i32.TrackingHomePage]
class TrackingHomeRoute extends _i35.PageRouteInfo<void> {
  const TrackingHomeRoute({List<_i35.PageRouteInfo>? children})
    : super(TrackingHomeRoute.name, initialChildren: children);

  static const String name = 'TrackingHomeRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i32.TrackingHomePage();
    },
  );
}

/// generated route for
/// [_i33.UserDataPolicyPage]
class UserDataPolicyRoute extends _i35.PageRouteInfo<void> {
  const UserDataPolicyRoute({List<_i35.PageRouteInfo>? children})
    : super(UserDataPolicyRoute.name, initialChildren: children);

  static const String name = 'UserDataPolicyRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i33.UserDataPolicyPage();
    },
  );
}

/// generated route for
/// [_i34.WelcomePage]
class WelcomeRoute extends _i35.PageRouteInfo<void> {
  const WelcomeRoute({List<_i35.PageRouteInfo>? children})
    : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static _i35.PageInfo page = _i35.PageInfo(
    name,
    builder: (data) {
      return const _i34.WelcomePage();
    },
  );
}
