# API Response Handling Refactoring

This document explains the refactored API response handling system that replaces the duplicate `Result<T>` and `ApiResponse<T>` classes with a unified, flexible approach.

## Overview

The previous system had duplication between `Result<T>` and `ApiResponse<T>`, which created unnecessary complexity and tight coupling to specific API response formats. The new system uses an enhanced `Result<T>` class with flexible parsing capabilities.

## Key Changes

### 1. Enhanced Result<T> Class

The `Result<T>` class now includes:
- Detailed error information (message, code, exception)
- Helper methods for checking state (`isSuccess`, `isError`, `isLoading`)
- Data access methods (`data`, `errorMessage`, `errorCode`)
- Functional methods (`map`, `onSuccess`, `onError`)

### 2. ApiResponseParser Utility

A new utility class that provides flexible parsing for different API response formats:
- `parseStandardResponse()` - For the standard format with status/data/error fields
- `parseDirectResponse()` - For direct data responses
- `parseListResponse()` - For array responses
- `parseWrappedListResponse()` - For wrapped array responses
- `parseFlexibleResponse()` - Automatically detects and parses different formats
- `parseCustomResponse()` - For completely custom parsing logic

### 3. Simplified Data Sources

Data sources now return `Result<T>` directly instead of `ApiResponse<T>`, eliminating the conversion layer.

## Usage Examples

### Basic Data Source Implementation

```dart
abstract class UserApiDataSource {
  Future<Result<LoginDataModel>> memberLogin({
    required String username,
    required String password,
  });
}

class UserApiDataSourceImpl implements UserApiDataSource {
  final ApiClient _apiClient;

  @override
  Future<Result<LoginDataModel>> memberLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberLogin,
      data: {'username': username, 'password': password},
    );

    return result.map<LoginDataModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        final parseResult = ApiResponseParser.parseFlexibleResponse(
          responseData,
          LoginDataModel.fromJson,
        );
        
        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }
}
```

### Repository Implementation

```dart
@override
Future<Either<Failure, LoginData>> memberLogin({
  required String username,
  required String password,
}) async {
  if (await _networkInfo.isConnected) {
    try {
      final result = await _userApiDataSource.memberLogin(
        username: username,
        password: password,
      );

      if (result.isSuccess) {
        return Right(result.data!.toEntity());
      } else {
        return Left(ServerFailure(
          message: result.errorMessage ?? 'Unknown server error',
        ));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message ?? e.toString()));
    } on Exception catch (e) {
      return Left(UnhandledFailure(message: e.toString()));
    }
  } else {
    return Left(NetworkFailure(message: 'No internet connection'));
  }
}
```

### Different Response Format Examples

#### Standard API Response Format
```json
{
  "status": true,
  "error_message": null,
  "error_code": null,
  "data": {
    "id": 1,
    "name": "John Doe"
  },
  "api_version": "v.0.0.1"
}
```

```dart
final result = ApiResponseParser.parseStandardResponse(
  responseData,
  UserModel.fromJson,
);
```

#### Direct Data Response
```json
{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

```dart
final result = ApiResponseParser.parseDirectResponse(
  responseData,
  UserModel.fromJson,
);
```

#### List Response
```json
[
  {"id": 1, "name": "John"},
  {"id": 2, "name": "Jane"}
]
```

```dart
final result = ApiResponseParser.parseListResponse(
  responseData,
  UserModel.fromJson,
);
```

#### Flexible Parsing (Recommended)
```dart
// Automatically detects the format and parses accordingly
final result = ApiResponseParser.parseFlexibleResponse(
  responseData,
  UserModel.fromJson,
);
```

## Benefits

1. **Flexibility**: Supports any API response format without code changes
2. **Reduced Duplication**: Single `Result<T>` class handles all scenarios
3. **Better Error Handling**: Rich error information with codes, messages, and exceptions
4. **Functional Programming**: Map and callback methods for cleaner code
5. **Type Safety**: Strong typing throughout the chain
6. **Maintainability**: Centralized parsing logic in `ApiResponseParser`

## Migration Guide

### From ApiResponse to Result

**Old approach:**
```dart
Future<ApiResponse<LoginDataModel>> memberLogin() async {
  var result = await _apiClient.post<dynamic>(endpoint, data: data);
  if (result is SuccessState) {
    return ApiResponse.fromJson(result.value, LoginDataModel.fromJson);
  } else if (result is ErrorState) {
    final errorResponse = ApiResponse.fromJson(result.value, (json) => json);
    throw ServerException(
      code: errorResponse.errorCode,
      message: errorResponse.errorMessage,
    );
  }
}
```

**New approach:**
```dart
Future<Result<LoginDataModel>> memberLogin() async {
  final result = await _apiClient.post<dynamic>(endpoint, data: data);
  return result.map<LoginDataModel>((responseData) {
    final parseResult = ApiResponseParser.parseFlexibleResponse(
      responseData,
      LoginDataModel.fromJson,
    );
    if (parseResult.isSuccess) {
      return parseResult.data!;
    } else {
      throw ServerException(
        code: parseResult.errorCode,
        message: parseResult.errorMessage,
      );
    }
  });
}
```

## Best Practices

1. Use `parseFlexibleResponse()` for most cases as it handles different formats automatically
2. Use specific parsers (`parseStandardResponse`, `parseDirectResponse`) when you know the exact format
3. Always handle both success and error cases in your data sources
4. Use the `map()` method for transforming successful results
5. Use `onSuccess()` and `onError()` for side effects without transformation
6. Leverage the rich error information for better debugging and user feedback
