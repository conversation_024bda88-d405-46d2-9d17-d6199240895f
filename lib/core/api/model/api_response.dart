// Generic base response class
//
// @deprecated This class is deprecated. Use Result<T> with ApiResponseParser instead.
// The new approach provides better flexibility and supports any API response format.
//
// Migration example:
// Old: ApiResponse.fromJson(json, MyModel.fromJson)
// New: ApiResponseParser.parseFlexibleResponse(json, MyModel.fromJson)
//
@Deprecated('Use Result<T> with ApiResponseParser for better flexibility')
class ApiResponse<T> {
  final bool status;
  final String? errorMessage;
  final String? errorCode;
  final T? data;
  final String apiVersion;

  ApiResponse({
    required this.status,
    this.errorMessage,
    this.errorCode,
    this.data,
    required this.apiVersion,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return ApiResponse<T>(
      status: json['status'] ?? false,
      errorMessage: json['error_message'],
      errorCode: json['error_code'],
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      apiVersion: json['api_version'] ?? '',
    );
  }

  // Helper method to check if response is successful and has data
  bool get isSuccess => status && data != null;

  // Helper method to get error info
  String get errorInfo => errorMessage ?? 'Unknown error occurred';
}
