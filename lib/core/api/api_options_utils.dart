import 'package:dio/dio.dart';

/// Utility class for creating commonly used API options
class ApiOptionsUtils {
  /// Creates options for downloading file content (Excel, PDF, etc.)
  ///
  /// [timeoutSeconds] - Optional timeout duration in seconds (defaults to 30)
  static Options createFileDownloadOptions({int timeoutSeconds = 30}) {
    return Options(
      responseType: ResponseType.bytes,
      followRedirects: false,
      receiveTimeout: Duration(seconds: timeoutSeconds),
      headers: {
        Headers.contentTypeHeader: 'application/json',
      },
    );
  }
}
