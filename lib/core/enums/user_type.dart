import 'package:json_annotation/json_annotation.dart';

/// Enum representing the type of user in the system
@JsonEnum(valueField: 'value')
enum UserType {
  /// Member user type
  member('member'),

  /// Consultant user type
  consultant('consultant');

  const UserType(this.value);

  /// The string value used in JSON serialization
  final String value;

  /// Creates a UserType from a string value
  /// Throws [ArgumentError] if the value is not valid
  static UserType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'member':
        return UserType.member;
      case 'consultant':
        return UserType.consultant;
      default:
        throw ArgumentError(
          'Invalid user type: $value. Must be either "member" or "consultant"',
        );
    }
  }

  /// Returns the string representation of the user type
  @override
  String toString() => value;

  /// Returns the display name for the user type
  String get displayName {
    switch (this) {
      case UserType.member:
        return 'Member';
      case UserType.consultant:
        return 'Consultant';
    }
  }

  /// Returns the localized display name key for the user type
  String get displayNameKey {
    switch (this) {
      case UserType.member:
        return 'member';
      case UserType.consultant:
        return 'consultant';
    }
  }

  /// Checks if the user type is member
  bool get isMember => this == UserType.member;

  /// Checks if the user type is consultant
  bool get isConsultant => this == UserType.consultant;

  /// JSON converter function for deserializing UserType from JSON
  /// Used in @JsonKey annotations for freezed models
  static UserType fromJson(String value) => fromString(value);

  /// JSON converter function for serializing UserType to JSON
  /// Used in @JsonKey annotations for freezed models
  static String toJson(UserType userType) => userType.value;
}
