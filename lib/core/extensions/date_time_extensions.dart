import 'package:intl/intl.dart';

extension DateTimeExtensions on DateTime {
  /// Format date to 'd MMM yyyy' with Thai month abbreviation and Buddhist year
  String toThaiDateString() {
    final DateFormat dayAndMonthFormat = DateFormat('d MMM', 'th');
    final int buddhistYear = year + 543;

    return '${dayAndMonthFormat.format(this)} $buddhistYear';
  }

  String toApiDateStringFormat() {
    return DateFormat('yyyy-MM-dd').format(this);
  }
}
