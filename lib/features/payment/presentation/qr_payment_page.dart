import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';

@RoutePage()
class QrPaymentPage extends StatefulWidget {
  final String paymentId;
  final String referenceCode1;
  final String referenceCode2;
  final String payerName;
  final String collectionDate;
  final String dueDate;
  final String amount;

  const QrPaymentPage({
    super.key,
    required this.paymentId,
    required this.referenceCode1,
    required this.referenceCode2,
    required this.payerName,
    required this.collectionDate,
    required this.dueDate,
    required this.amount,
  });

  @override
  State<QrPaymentPage> createState() => _QrPaymentPageState();
}

class _QrPaymentPageState extends State<QrPaymentPage> {
  bool _isPaymentSuccess = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child:
            _isPaymentSuccess
                ? _buildSuccessContent(l10n)
                : _buildQrContent(l10n),
      ),
    );
  }

  Widget _buildQrContent(AppLocalizations l10n) {
    return Column(
      children: [
        _buildAppBar(l10n.payFee),
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  SizedBox(height: 24.h),
                  Text(
                    l10n.payViaQrCode,
                    style: TextStyle(
                      fontFamily: 'NotoSansThai',
                      fontWeight: FontWeight.w600,
                      fontSize: 20.sp,
                      color: AppColors.textDefaultDark,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    l10n.qrCodeScanInstruction,
                    style: TextStyle(
                      fontFamily: 'NotoSansThai',
                      fontWeight: FontWeight.w400,
                      fontSize: 14.sp,
                      color: AppColors.textDefaultDark,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24.h),
                  Image.asset(
                    'assets/images/qr_code.png',
                    height: 240.h,
                    width: 240.w,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(height: 32.h),
                  _buildPaymentInfoCard(l10n),
                  SizedBox(height: 32.h),
                  _buildPaymentInstructions(l10n),
                ],
              ),
            ),
          ),
        ),
        _buildBottomButtons(l10n),
      ],
    );
  }

  Widget _buildSuccessContent(AppLocalizations l10n) {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/images/success_icon.svg',
                  height: 64.h,
                  width: 64.w,
                  colorFilter: const ColorFilter.mode(
                    Color(0xFF66C215),
                    BlendMode.srcIn,
                  ),
                ),
                SizedBox(height: 24.h),
                Text(
                  l10n.success,
                  style: TextStyle(
                    fontFamily: 'NotoSansThai',
                    fontWeight: FontWeight.w600,
                    fontSize: 20.sp,
                    color: AppColors.textDefaultDark,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8.h),
                Text(
                  l10n.paymentSuccessMessage,
                  style: TextStyle(
                    fontFamily: 'NotoSansThai',
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: AppColors.textDefaultDark,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.all(24.r),
          child: SecondaryButton(
            text: l10n.eReceiptLink,
            onPressed: () {
              // Handle e-receipt download
            },
            height: 40.h,
            borderRadius: 20.r,
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar(String title) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => context.router.pop(),
            child: Container(
              padding: EdgeInsets.all(4.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.arrow_back,
                color: AppColors.textDefaultDark,
                size: 24.sp,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'NotoSansThai',
              fontWeight: FontWeight.w600,
              fontSize: 20.sp,
              color: AppColors.textDefaultDark,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfoCard(AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color(0x141F2227),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            _buildInfoRow(l10n.paymentNotificationNumber, widget.paymentId),
            SizedBox(height: 16.h),
            _buildInfoRow(l10n.referenceCode1, widget.referenceCode1),
            SizedBox(height: 16.h),
            _buildInfoRow(l10n.referenceCode2, widget.referenceCode2),
            SizedBox(height: 16.h),
            _buildInfoRow(l10n.payerName, widget.payerName),
            SizedBox(height: 16.h),
            _buildInfoRow(l10n.collectionDate, widget.collectionDate),
            SizedBox(height: 16.h),
            _buildInfoRow(l10n.dueDate, widget.dueDate),
            SizedBox(height: 16.h),
            _buildInfoRow(
              l10n.totalPaymentAmount,
              '${widget.amount} ${l10n.baht}',
              valueStyle: TextStyle(
                fontFamily: 'NotoSansThai',
                fontWeight: FontWeight.w600,
                fontSize: 16.sp,
                color: AppColors.textDefaultDark,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {TextStyle? valueStyle}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'NotoSansThai',
            fontWeight: FontWeight.w400,
            fontSize: 14.sp,
            color: AppColors.textSubdude,
          ),
        ),
        Text(
          value,
          style:
              valueStyle ??
              TextStyle(
                fontFamily: 'NotoSansThai',
                fontWeight: FontWeight.w400,
                fontSize: 14.sp,
                color: AppColors.textDefaultDark,
              ),
        ),
      ],
    );
  }

  Widget _buildPaymentInstructions(AppLocalizations l10n) {
    return Column(
      children: [
        Text(
          l10n.mobileBankingPayment,
          style: TextStyle(
            fontFamily: 'NotoSansThai',
            fontWeight: FontWeight.w600,
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          l10n.paymentUpdateNotice,
          style: TextStyle(
            fontFamily: 'NotoSansThai',
            fontWeight: FontWeight.w400,
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16.h),
        Text(
          l10n.or,
          style: TextStyle(
            fontFamily: 'NotoSansThai',
            fontWeight: FontWeight.w600,
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16.h),
        Text(
          l10n.counterPayment,
          style: TextStyle(
            fontFamily: 'NotoSansThai',
            fontWeight: FontWeight.w600,
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16.h),
        GestureDetector(
          onTap: () {
            // Handle download pay-in slip
          },
          child: Text(
            l10n.downloadPayInSlip,
            style: TextStyle(
              fontFamily: 'NotoSansThai',
              fontWeight: FontWeight.w400,
              fontSize: 14.sp,
              color: const Color(0xFF0A4C9A),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButtons(AppLocalizations l10n) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          Expanded(
            child: SecondaryButton(
              text: l10n.backToPaymentList,
              onPressed: () => context.router.pop(),
              height: 40.h,
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: PrimaryButton(
              text: l10n.saveQrCode,
              onPressed: () {
                // For demo purposes, we'll simulate a successful payment
                setState(() {
                  _isPaymentSuccess = true;
                });
              },
              height: 40.h,
            ),
          ),
        ],
      ),
    );
  }
}
