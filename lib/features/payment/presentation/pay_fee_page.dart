import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/core/routes/router.gr.dart';

@RoutePage()
class PayFeePage extends StatefulWidget {
  const PayFeePage({super.key});

  @override
  State<PayFeePage> createState() => _PayFeePageState();
}

class _PayFeePageState extends State<PayFeePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'PayFeePage',
  );

  // This would come from your API in a real app
  final List<FeeItem> _feeItems = [
    FeeItem(
      id: '20171101112028222',
      referenceCode: '6201240000016730',
      collectionDate: '02 ม.ค. 2568 23.00.00',
      dueDate: '12 มี.ค. 2568 23.00.00',
      details: 'ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา',
      processDate: '-',
      amount: '5,000',
      status: PaymentStatus.pending,
    ),
    FeeItem(
      id: '20171101112028223',
      referenceCode: '6201240000016731',
      collectionDate: '02 ม.ค. 2568 23:00:00',
      dueDate: '12 มี.ค. 2568 23:00:00',
      details: 'ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา',
      processDate: '-',
      amount: '5,000',
      status: PaymentStatus.verifying,
    ),
    FeeItem(
      id: '20171101112028224',
      referenceCode: '6201240000016732',
      collectionDate: '02 ม.ค. 2568 23.00.00',
      dueDate: '12 มี.ค. 2568 23.00.00',
      details: 'ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา',
      processDate: '02 ม.ค. 2568 14:22:04',
      amount: '5,000',
      status: PaymentStatus.confirmed,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFF8F8F8),
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(l10n),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 24.h,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildFeeListHeader(l10n),
                      SizedBox(height: 16.h),
                      _buildReceiptDownloadSection(l10n),
                      SizedBox(height: 24.h),
                      ..._buildFeeItemList(l10n),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(AppLocalizations l10n) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => context.router.pop(),
            child: Container(
              padding: EdgeInsets.all(4.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.arrow_back,
                color: AppColors.textDefaultDark,
                size: 24.sp,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Text(
            l10n.payFee,
            style: TextStyle(
              fontFamily: 'NotoSansThai',
              fontWeight: FontWeight.w600,
              fontSize: 20.sp,
              color: AppColors.textDefaultDark,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeeListHeader(AppLocalizations l10n) {
    return Text(
      l10n.paymentFeeList,
      style: TextStyle(
        fontFamily: 'NotoSansThai',
        fontWeight: FontWeight.w600,
        fontSize: 20.sp,
        color: AppColors.textDefaultDark,
      ),
    );
  }

  Widget _buildReceiptDownloadSection(AppLocalizations l10n) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            l10n.downloadElectronicReceipt,
            style: TextStyle(
              fontFamily: 'NotoSansThai',
              fontWeight: FontWeight.w400,
              fontSize: 14.sp,
              color: AppColors.textDefaultDark,
            ),
          ),
        ),
        PrimaryButton(
          text: l10n.eReceiptLink,
          onPressed: () {
            // Handle e-receipt download
          },
          height: 40.h,
          borderRadius: 20.r,
        ),
      ],
    );
  }

  List<Widget> _buildFeeItemList(AppLocalizations l10n) {
    return _feeItems.map((item) => _buildFeeItem(item, l10n)).toList();
  }

  Widget _buildFeeItem(FeeItem item, AppLocalizations l10n) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color(0x141F2227),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.paymentNotificationNumber,
                  style: TextStyle(
                    fontFamily: 'NotoSansThai',
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: AppColors.textDefaultDark,
                  ),
                ),
                _buildStatusTag(item.status, l10n),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                const Icon(
                  Icons.content_copy_outlined,
                  color: AppColors.borderDefault,
                  size: 16,
                ),
                SizedBox(width: 8.w),
                Text(
                  item.id,
                  style: TextStyle(
                    fontFamily: 'NotoSansThai',
                    fontWeight: FontWeight.w600,
                    fontSize: 20.sp,
                    color: AppColors.textDefaultDark,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildInfoRow(l10n.referenceCode1, item.referenceCode),
            SizedBox(height: 8.h),
            _buildInfoRow(l10n.collectionDate, item.collectionDate),
            SizedBox(height: 8.h),
            _buildInfoRow(l10n.dueDate, item.dueDate),
            SizedBox(height: 8.h),
            _buildInfoRow(l10n.paymentDetails, item.details),
            SizedBox(height: 8.h),
            _buildInfoRow(l10n.processDate, item.processDate),
            SizedBox(height: 8.h),
            _buildInfoRow(
              l10n.totalPaymentAmount,
              '${item.amount} ${l10n.baht}',
              valueStyle: TextStyle(
                fontFamily: 'NotoSansThai',
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
                color: AppColors.textDefaultDark,
              ),
            ),
            if (item.status != PaymentStatus.confirmed) ...[
              Divider(
                height: 32.h,
                thickness: 1,
                color: AppColors.borderDefault,
              ),
              _buildActionButtons(item, l10n),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusTag(PaymentStatus status, AppLocalizations l10n) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case PaymentStatus.pending:
        backgroundColor = Colors.white;
        textColor = const Color(0xFFFFC115);
        text = l10n.paymentStatusPending;
        break;
      case PaymentStatus.verifying:
        backgroundColor = Colors.white;
        textColor = const Color(0xFFFFC115);
        text = l10n.paymentStatusVerifying;
        break;
      case PaymentStatus.confirmed:
        backgroundColor = Colors.white;
        textColor = const Color(0xFF66C215);
        text = l10n.paymentStatusConfirmed;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(50.r),
        border: Border.all(color: const Color(0xFFE5E6E6)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'NotoSansThai',
          fontWeight: FontWeight.w400,
          fontSize: 12.sp,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {TextStyle? valueStyle}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'NotoSansThai',
            fontWeight: FontWeight.w400,
            fontSize: 12.sp,
            color: AppColors.textSubdude,
          ),
        ),
        Text(
          value,
          style:
              valueStyle ??
              TextStyle(
                fontFamily: 'NotoSansThai',
                fontWeight: FontWeight.w400,
                fontSize: 12.sp,
                color: AppColors.textDefaultDark,
              ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(FeeItem item, AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.paymentEvidence,
            onPressed: () {
              // Handle evidence upload
            },
            height: 40.h,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: PrimaryButton(
            text: l10n.payFee,
            onPressed: () {
              context.router.push(
                QrPaymentRoute(
                  paymentId: item.id,
                  referenceCode1: item.referenceCode,
                  referenceCode2: '62022307',
                  payerName: 'วีวาสนาดี',
                  collectionDate: item.collectionDate,
                  dueDate: item.dueDate,
                  amount: item.amount,
                ),
              );
            },
            height: 40.h,
          ),
        ),
      ],
    );
  }
}

enum PaymentStatus { pending, verifying, confirmed }

class FeeItem {
  final String id;
  final String referenceCode;
  final String collectionDate;
  final String dueDate;
  final String details;
  final String processDate;
  final String amount;
  final PaymentStatus status;

  FeeItem({
    required this.id,
    required this.referenceCode,
    required this.collectionDate,
    required this.dueDate,
    required this.details,
    required this.processDate,
    required this.amount,
    required this.status,
  });
}
