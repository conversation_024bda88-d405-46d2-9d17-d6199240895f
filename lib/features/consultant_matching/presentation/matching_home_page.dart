import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/background/main_back_ground.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/features/consultant_matching/presentation/components/card_project.dart';
import 'package:mcdc/features/consultant_matching/presentation/components/matching_top.dart';
import 'package:mcdc/features/consultant_matching/presentation/components/bottom_sheet_project_filter.dart';

@RoutePage()
class MatchingHomePage extends StatefulWidget {
  const MatchingHomePage({super.key});

  @override
  State<MatchingHomePage> createState() => _MatchingHomePageState();
}

class _MatchingHomePageState extends State<MatchingHomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'MatchingHomePage',
  );

  // Mock data for 10 projects
  late final List<ProjectData> _mockProjects;

  // Filter state
  String? _appliedDataFilter;
  String? _appliedBranchData;

  @override
  void initState() {
    super.initState();
    _initializeMockData();
  }

  void _initializeMockData() {
    _mockProjects = [
      ProjectData(
        title:
            'สำรวจออกแบบเพื่อก่อสร้างเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8 ตำบลบ้านใหม่ อำเภอบ้านใหม่ จังหวัดกรุงเทพมหานคร',
        dateRange: 'วันที่ประกาศ : 29 ม.ค. 2568 - 30 มี.ค. 2568',
        stats: const ProjectStats(
          totalCount: '30',
          interestedCount: '8',
          viewCount: '100',
        ),
        isFavorite: false,
        onMorePressed: () => _handleMorePressed(0),
        onFavoritePressed: () => _handleFavoritePressed(0),
      ),
      ProjectData(
        title:
            'ออกแบบระบบบำบัดน้ำเสียชุมชนแบบธรรมชาติ สำหรับชุมชนบ้านสวนผึ้ง ตำบลสวนผึ้ง อำเภอศรีราชา จังหวัดชลบุรี',
        dateRange: 'วันที่ประกาศ : 15 ก.พ. 2568 - 15 เม.ย. 2568',
        stats: const ProjectStats(
          totalCount: '25',
          interestedCount: '12',
          viewCount: '85',
        ),
        isFavorite: true,
        onMorePressed: () => _handleMorePressed(1),
        onFavoritePressed: () => _handleFavoritePressed(1),
      ),
      ProjectData(
        title:
            'การศึกษาความเป็นไปได้ในการพัฒนาพื้นที่เขตเศรษฐกิจพิเศษ ชายแดนไทย-มาเลเซีย จังหวัดสงขลา',
        dateRange: 'วันที่ประกาศ : 10 ก.พ. 2568 - 10 พ.ค. 2568',
        stats: const ProjectStats(
          totalCount: '45',
          interestedCount: '15',
          viewCount: '200',
        ),
        isFavorite: false,
        onMorePressed: () => _handleMorePressed(2),
        onFavoritePressed: () => _handleFavoritePressed(2),
      ),
      ProjectData(
        title:
            'ออกแบบและก่อสร้างสะพานข้ามคลองบางกะปิ เพื่อเชื่อมต่อการคมนาคมในเขตกรุงเทพมหานคร',
        dateRange: 'วันที่ประกาศ : 5 มี.ค. 2568 - 5 มิ.ย. 2568',
        stats: const ProjectStats(
          totalCount: '60',
          interestedCount: '20',
          viewCount: '350',
        ),
        isFavorite: true,
        onMorePressed: () => _handleMorePressed(3),
        onFavoritePressed: () => _handleFavoritePressed(3),
      ),
      ProjectData(
        title:
            'การปรับปรุงระบบระบายน้ำในเขตพื้นที่เมืองพัทยา จังหวัดชลบุรี เพื่อป้องกันปัญหาน้ำท่วม',
        dateRange: 'วันที่ประกาศ : 20 มี.ค. 2568 - 20 มิ.ย. 2568',
        stats: const ProjectStats(
          totalCount: '35',
          interestedCount: '18',
          viewCount: '150',
        ),
        isFavorite: false,
        onMorePressed: () => _handleMorePressed(4),
        onFavoritePressed: () => _handleFavoritePressed(4),
      ),
      ProjectData(
        title:
            'ศึกษาและออกแบบระบบขนส่งมวลชนรางเบาสายสีเขียว ช่วงหมอชิต-คูคต จังหวัดนนทบุรี',
        dateRange: 'วันที่ประกาศ : 1 เม.ย. 2568 - 1 ก.ค. 2568',
        stats: const ProjectStats(
          totalCount: '80',
          interestedCount: '25',
          viewCount: '500',
        ),
        isFavorite: true,
        onMorePressed: () => _handleMorePressed(5),
        onFavoritePressed: () => _handleFavoritePressed(5),
      ),
      ProjectData(
        title:
            'การพัฒนาโครงสร้างพื้นฐานดิจิทัลสำหรับเมืองอัจฉริยะ ในเขตพื้นที่จังหวัดเชียงใหม่',
        dateRange: 'วันที่ประกาศ : 15 เม.ย. 2568 - 15 ก.ค. 2568',
        stats: const ProjectStats(
          totalCount: '40',
          interestedCount: '22',
          viewCount: '180',
        ),
        isFavorite: false,
        onMorePressed: () => _handleMorePressed(6),
        onFavoritePressed: () => _handleFavoritePressed(6),
      ),
      ProjectData(
        title:
            'ออกแบบและก่อสร้างท่าเรือน้ำลึกแห่งใหม่ ในพื้นที่จังหวัดระยอง เพื่อรองรับการขนส่งสินค้าระหว่างประเทศ',
        dateRange: 'วันที่ประกาศ : 10 พ.ค. 2568 - 10 ส.ค. 2568',
        stats: const ProjectStats(
          totalCount: '100',
          interestedCount: '35',
          viewCount: '750',
        ),
        isFavorite: true,
        onMorePressed: () => _handleMorePressed(7),
        onFavoritePressed: () => _handleFavoritePressed(7),
      ),
      ProjectData(
        title:
            'การศึกษาผลกระทบสิ่งแวดล้อมและการจัดการขยะในเขตอุตสาหกรรมมาบตาพุด จังหวัดระยอง',
        dateRange: 'วันที่ประกาศ : 25 พ.ค. 2568 - 25 ส.ค. 2568',
        stats: const ProjectStats(
          totalCount: '28',
          interestedCount: '14',
          viewCount: '95',
        ),
        isFavorite: false,
        onMorePressed: () => _handleMorePressed(8),
        onFavoritePressed: () => _handleFavoritePressed(8),
      ),
      ProjectData(
        title:
            'พัฒนาระบบการจัดการน้ำแบบบูรณาการสำหรับพื้นที่ลุ่มน้ำเจ้าพระยา เพื่อป้องกันอุทกภัยและภัยแล้ง',
        dateRange: 'วันที่ประกาศ : 5 มิ.ย. 2568 - 5 ก.ย. 2568',
        stats: const ProjectStats(
          totalCount: '55',
          interestedCount: '28',
          viewCount: '320',
        ),
        isFavorite: false,
        onMorePressed: () => _handleMorePressed(9),
        onFavoritePressed: () => _handleFavoritePressed(9),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.backgroundColorSecondary,
      body: MainBackground(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            const MatchingTop(),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    32.h.verticalSpace,
                    _buildProjectsHeader(l10n),
                    16.h.verticalSpace,
                    _buildProjectsList(),
                    24.h.verticalSpace,
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMorePressed(int index) {
    // Handle more button press for project at index
    debugPrint(
      'More pressed for project $index: ${_mockProjects[index].title}',
    );
    // Navigate to suitable consultant page
    context.pushRoute(const SuitableConsultantRoute());
  }

  void _handleFavoritePressed(int index) {
    // Handle favorite button press for project at index
    setState(() {
      final project = _mockProjects[index];
      _mockProjects[index] = ProjectData(
        title: project.title,
        dateRange: project.dateRange,
        stats: project.stats,
        isFavorite: !project.isFavorite,
        onMorePressed: project.onMorePressed,
        onFavoritePressed: project.onFavoritePressed,
      );
    });
  }

  void _showFilterBottomSheet() {
    showProjectFilterBottomSheet(
      context: context,
      onApplyFilter: (dataFilter, branchData) {
        setState(() {
          _appliedDataFilter = dataFilter;
          _appliedBranchData = branchData;
        });

        // Handle the applied filters
        _handleFilterApplied(dataFilter, branchData);
      },
      onClearFilter: () {
        setState(() {
          _appliedDataFilter = null;
          _appliedBranchData = null;
        });

        // Handle filter clearing
        _handleFilterCleared();
      },
    );
  }

  void _handleFilterApplied(String? dataFilter, String? branchData) {
    // TODO: Implement your filter logic here
    // This could be API calls, state management updates, etc.
    debugPrint('Filters applied - Data: $dataFilter, Branch: $branchData');

    // Example: Show a snackbar to confirm
    if (mounted) {
      final l10n = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            l10n.filterApplied,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              color: Colors.white,
            ),
          ),
          backgroundColor: AppColors.surfaceSuccess,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _handleFilterCleared() {
    // TODO: Implement your clear filter logic here
    debugPrint('Filters cleared');

    // Example: Show a snackbar to confirm
    if (mounted) {
      final l10n = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            l10n.filterCleared,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              color: Colors.white,
            ),
          ),
          backgroundColor: AppColors.textSubdude,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  bool _hasActiveFilters() {
    return _appliedDataFilter != null || _appliedBranchData != null;
  }

  Widget _buildProjectsList() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _mockProjects.length,
      separatorBuilder: (context, index) => 24.h.verticalSpace,
      itemBuilder: (context, index) {
        return ProjectCard(projectData: _mockProjects[index]);
      },
    );
  }

  Widget _buildProjectsHeader(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.projects,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        24.h.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              l10n.totalItemsCount(10),
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textDefaultDark,
                height: 1.5,
              ),
            ),
            GestureDetector(
              onTap: _showFilterBottomSheet,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    l10n.filter,
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color:
                          _hasActiveFilters()
                              ? AppColors.textPrimary
                              : AppColors.textSubdude,
                      height: 1.5,
                    ),
                  ),
                  if (_hasActiveFilters()) ...[
                    SizedBox(width: 4.w),
                    Container(
                      width: 6.w,
                      height: 6.w,
                      decoration: const BoxDecoration(
                        color: AppColors.textPrimary,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
