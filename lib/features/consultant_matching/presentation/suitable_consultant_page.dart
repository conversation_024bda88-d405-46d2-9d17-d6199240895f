import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/features/consultant_matching/presentation/components/card_suitable_consultant.dart';

@RoutePage()
class SuitableConsultantPage extends StatefulWidget {
  const SuitableConsultantPage({super.key});

  @override
  State<SuitableConsultantPage> createState() => _SuitableConsultantPageState();
}

class _SuitableConsultantPageState extends State<SuitableConsultantPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<ConsultantData> _suitableConsultants;
  late List<ConsultantData> _interestedPeople;
  late List<ConsultantData> _interestedList;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      if (_tabController.index != _currentTabIndex) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
    _initializeMockData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeMockData() {
    _suitableConsultants = [
      ConsultantData(
        companyName:
            'บริษัท ยูไนเต็ด แอนนาลิสต์ แอนด์ เอ็นจิเนียริ่งคอน ซัลแตนท์ จำกัด',
        isMember: true,
        matchingPercentage: 100,
        isFavorite: false,
        onFavoritePressed: () => _handleFavoritePressed(0, 0),
      ),
      ConsultantData(
        companyName: 'บริษัท เอส เอ็ม อี คอนซัลติ้ง เอ็นจิเนียริ่ง จำกัด',
        isMember: true,
        matchingPercentage: 95,
        isFavorite: true,
        onFavoritePressed: () => _handleFavoritePressed(0, 1),
      ),
    ];

    _interestedPeople = [
      ConsultantData(
        companyName: 'บริษัท ไทย เอ็นจิเนียริ่ง คอนซัลแตนท์ จำกัด',
        isMember: false,
        matchingPercentage: 85,
        isFavorite: false,
        onFavoritePressed: () => _handleFavoritePressed(1, 0),
      ),
    ];

    _interestedList = [
      ConsultantData(
        companyName: 'บริษัท แอดวานซ์ เอ็นจิเนียริ่ง คอนซัลแตนท์ จำกัด',
        isMember: true,
        matchingPercentage: 90,
        isFavorite: true,
        onFavoritePressed: () => _handleFavoritePressed(2, 0),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: AppBarCommon(title: l10n.suitableConsultant),
      body: Column(
        children: [
          _buildTabBar(l10n),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildConsultantList(
                  _suitableConsultants,
                  l10n.totalItemsCount(30),
                ),
                _buildConsultantList(
                  _interestedPeople,
                  l10n.totalItemsCount(5),
                ),
                _buildConsultantList(_interestedList, l10n.totalItemsCount(12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(AppLocalizations l10n) {
    return Container(
      color: AppColors.surfaceDefault,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      child: SizedBox(
        height: 48.h,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          itemCount: 3,
          separatorBuilder: (context, index) => 24.w.horizontalSpace,
          itemBuilder: (context, index) {
            final tabTitles = [
              l10n.suitableConsultant,
              l10n.interestedPeople,
              l10n.interestedList,
            ];
            final isSelected = _currentTabIndex == index;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _currentTabIndex = index;
                });
                _tabController.animateTo(index);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? AppColors.surfacePrimarySubdude
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(50.r),
                ),
                child: Center(
                  child: Text(
                    tabTitles[index],
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      height: 1.5,
                      color:
                          isSelected
                              ? AppColors.textPrimary
                              : AppColors.textSubdude,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildConsultantList(
    List<ConsultantData> consultants,
    String totalText,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            totalText,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.iconDefault,
              height: 1.5,
            ),
          ),
          24.h.verticalSpace,
          Expanded(
            child: ListView.separated(
              physics: const BouncingScrollPhysics(),
              itemCount: consultants.length,
              separatorBuilder: (context, index) => 24.h.verticalSpace,
              itemBuilder: (context, index) {
                return ConsultantCard(consultantData: consultants[index]);
              },
            ),
          ),
          24.h.verticalSpace,
        ],
      ),
    );
  }

  void _handleFavoritePressed(int tabIndex, int consultantIndex) {
    setState(() {
      List<ConsultantData> currentList;
      switch (tabIndex) {
        case 0:
          currentList = _suitableConsultants;
          break;
        case 1:
          currentList = _interestedPeople;
          break;
        case 2:
          currentList = _interestedList;
          break;
        default:
          return;
      }

      if (consultantIndex < currentList.length) {
        final consultant = currentList[consultantIndex];
        currentList[consultantIndex] = ConsultantData(
          companyName: consultant.companyName,
          isMember: consultant.isMember,
          matchingPercentage: consultant.matchingPercentage,
          isFavorite: !consultant.isFavorite,
          onMorePressed: consultant.onMorePressed,
          onFavoritePressed: consultant.onFavoritePressed,
        );
      }
    });
  }
}
