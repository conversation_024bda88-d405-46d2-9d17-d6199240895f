import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';

@RoutePage()
class ConsultantInfoPage extends StatefulWidget {
  const ConsultantInfoPage({super.key});

  @override
  State<ConsultantInfoPage> createState() => _ConsultantInfoPageState();
}

class _ConsultantInfoPageState extends State<ConsultantInfoPage> {
  bool _isFavorite = false;
  final List<ProjectData> _projects = [
    ProjectData(
      title: 'โครงการจัดทำแผนแม่บทป้องกันและบรรเทา\nภัยจากคลื่นสึนามิ',
      branch: 'TR',
      expertise: 'K203A',
      isExpanded: false,
    ),
    ProjectData(
      title: 'โครงการจัดทำแผนแม่บทป้องกันและบรรเทา\nภัยจากคลื่นสึนามิ',
      branch: 'TR',
      expertise: 'K203A',
      services: '4A, 7E, 4F,4G, 4H',
      duration: '11 พ.ย. 2535 - 11 ธ.ค. 2535',
      totalValue: '250,000.00 บาท',
      contractValue: '250,000.00 บาท',
      isExpanded: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: AppBarCommon(title: l10n.consultantInfo),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildConsultantInfoCard(l10n),
            24.h.verticalSpace,
            _buildExperienceTitle(l10n),
            24.h.verticalSpace,
            ..._buildProjectCards(context),
          ],
        ),
      ),
    );
  }

  Widget _buildConsultantInfoCard(AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCompanyHeader(),
          16.h.verticalSpace,
          _buildMemberBadge(l10n),
          24.h.verticalSpace,
          _buildRegistrationInfo(l10n),
          16.h.verticalSpace,
          _buildRegistrationDate(l10n),
          16.h.verticalSpace,
          _buildBranchInfo(l10n),
          16.h.verticalSpace,
          _buildPhoneInfo(),
          16.h.verticalSpace,
          _buildEmailButton(),
        ],
      ),
    );
  }

  Widget _buildCompanyHeader() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            'บริษัท ยูไนเต็ด แอนนาลิสต์ แอนด์ เอ็นจิเนียริ่งคอน ซัลแตนท์ จำกัด',
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.iconDefault,
              height: 1.5,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        16.w.horizontalSpace,
        GestureDetector(
          onTap: () {
            setState(() {
              _isFavorite = !_isFavorite;
            });
          },
          child: SvgPicture.asset(
            _isFavorite
                ? 'assets/icons/heart_filled.svg'
                : 'assets/icons/heart_outline.svg',
            width: 16.r,
            height: 16.r,
            colorFilter: ColorFilter.mode(
              _isFavorite ? AppColors.iconCritical : AppColors.iconDefault,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemberBadge(AppLocalizations l10n) {
    return Row(
      children: [
        Container(
          width: 10.r,
          height: 10.r,
          decoration: const BoxDecoration(
            color: Color(0xFF16A54D),
            shape: BoxShape.circle,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          l10n.member,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.iconDefault,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildRegistrationInfo(AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFF1F6AC3),
        borderRadius: BorderRadius.circular(20.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Text(
                  '37',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
                6.h.verticalSpace,
                Text(
                  l10n.registrationNumber,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          Container(width: 1.w, height: 40.h, color: const Color(0xFFD2D2D7)),
          Expanded(
            child: Column(
              children: [
                Text(
                  'ระดับ 1',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
                6.h.verticalSpace,
                Text(
                  l10n.type,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRegistrationDate(AppLocalizations l10n) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          l10n.firstRegistrationDate,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          '1 ม.ค. 2560',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.iconDefault,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildBranchInfo(AppLocalizations l10n) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          l10n.branch,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          'AG, BU,',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.iconDefault,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneInfo() {
    return Row(
      children: [
        SvgPicture.asset(
          'assets/icons/phone.svg',
          width: 16.r,
          height: 16.r,
          colorFilter: const ColorFilter.mode(
            AppColors.iconDefault,
            BlendMode.srcIn,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          '************',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.iconDefault,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildEmailButton() {
    return InkWell(
      onTap: () {
        // Handle email action
      },
      borderRadius: BorderRadius.circular(4.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/icons/envelope.svg',
              width: 16.r,
              height: 16.r,
              colorFilter: const ColorFilter.mode(
                AppColors.iconDefault,
                BlendMode.srcIn,
              ),
            ),
            8.w.horizontalSpace,
            Text(
              '<EMAIL>',
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.iconDefault,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperienceTitle(AppLocalizations l10n) {
    return Text(
      l10n.consultantExperience,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.iconDefault,
        height: 1.5,
      ),
    );
  }

  List<Widget> _buildProjectCards(BuildContext context) {
    return _projects.asMap().entries.map((entry) {
      final index = entry.key;
      final project = entry.value;

      return Column(
        children: [
          _buildProjectCard(context, project, index),
          if (index < _projects.length - 1) 24.h.verticalSpace,
        ],
      );
    }).toList();
  }

  Widget _buildProjectCard(
    BuildContext context,
    ProjectData project,
    int index,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          _buildProjectHeader(context, project, index),
          if (project.isExpanded) _buildProjectDetails(context, project),
        ],
      ),
    );
  }

  Widget _buildProjectHeader(
    BuildContext context,
    ProjectData project,
    int index,
  ) {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                project.title,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.iconDefault,
                  height: 1.5,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            16.w.horizontalSpace,
            GestureDetector(
              onTap: () {
                setState(() {
                  _projects[index] = ProjectData(
                    title: project.title,
                    branch: project.branch,
                    expertise: project.expertise,
                    services: project.services,
                    duration: project.duration,
                    totalValue: project.totalValue,
                    contractValue: project.contractValue,
                    isExpanded: !project.isExpanded,
                  );
                });
              },
              child: SvgPicture.asset(
                project.isExpanded
                    ? 'assets/icons/arrow_down.svg'
                    : 'assets/icons/arrow_right.svg',
                width: 16.r,
                height: 16.r,
                colorFilter: ColorFilter.mode(
                  project.isExpanded
                      ? AppColors.textSubdude
                      : AppColors.iconDefault,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
        16.h.verticalSpace,
        Divider(height: 1, thickness: 1, color: AppColors.borderDefault),
        16.h.verticalSpace,
        Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.branch,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textSubdude,
                    height: 1.5,
                  ),
                ),
                Text(
                  project.branch,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.iconDefault,
                    height: 1.5,
                  ),
                ),
              ],
            ),
            8.h.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.expertise,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textSubdude,
                    height: 1.5,
                  ),
                ),
                Text(
                  project.expertise,
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.iconDefault,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProjectDetails(BuildContext context, ProjectData project) {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      children: [
        if (project.services != null) ...[
          _buildDetailRow(l10n.services, project.services!),
          16.h.verticalSpace,
        ],
        if (project.duration != null) ...[
          _buildDetailRow(l10n.projectDuration, project.duration!),
          16.h.verticalSpace,
        ],
        if (project.totalValue != null) ...[
          _buildDetailRow(l10n.totalProjectValue, project.totalValue!),
          16.h.verticalSpace,
        ],
        if (project.contractValue != null)
          _buildDetailRow(l10n.consultantContractValue, project.contractValue!),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.iconDefault,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}

class ProjectData {
  final String title;
  final String branch;
  final String expertise;
  final String? services;
  final String? duration;
  final String? totalValue;
  final String? contractValue;
  final bool isExpanded;

  const ProjectData({
    required this.title,
    required this.branch,
    required this.expertise,
    this.services,
    this.duration,
    this.totalValue,
    this.contractValue,
    required this.isExpanded,
  });
}
