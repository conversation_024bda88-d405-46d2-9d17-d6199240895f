import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/constants/app_layout.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_dropdown_field.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class BottomSheetProjectFilter extends StatefulWidget {
  final Function(String? dataFilter, String? branchData)? onApplyFilter;
  final VoidCallback? onClearFilter;

  const BottomSheetProjectFilter({
    super.key,
    this.onApplyFilter,
    this.onClearFilter,
  });

  @override
  State<BottomSheetProjectFilter> createState() =>
      _BottomSheetProjectFilterState();
}

class _BottomSheetProjectFilterState extends State<BottomSheetProjectFilter> {
  String? selectedDataFilter;
  String? selectedBranchData;

  // Sample data for dropdowns - replace with actual data
  final List<String> dataFilterOptions = [
    'ผลการจับคู่จากมาก-น้อย',
    'โครงการล่าสุด',
  ];

  final List<String> branchDataOptions = [
    'ทั้งหมด',
    'สาขากรุงเทพ',
    'สาขาเชียงใหม่',
    'สาขาขอนแก่น',
    'สาขาสงขลา',
    'สาขาสมุทรปราการ',
    'สาขาอุตรดิตถ์',
    'สาขาชลบุรี',
    'สาขาชัยนาท',
    'สาขาชัยภูมิ',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header Section
          _buildHeader(),

          // Content Section
          _buildContent(),
          0.2.sh.verticalSpace,
          // Footer Section
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'ตัวกรอง',
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
              height: 1.5,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 24.w,
                color: AppColors.iconDefault,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // Data Filter Dropdown
          CustomDropdownField<String>(
            label: 'กรองข้อมูล',
            hintText: 'เลือกกรองข้อมูล',
            selectedValue: selectedDataFilter,
            items: dataFilterOptions,
            displayText: (item) => item,
            onChange: (value) {
              setState(() {
                selectedDataFilter = value;
              });
            },
          ),

          SizedBox(height: 24.h),

          // Branch Data Dropdown
          CustomDropdownField<String>(
            label: 'ข้อมูลสาขา',
            hintText: 'เลือกข้อมูลสาขา',
            selectedValue: selectedBranchData,
            items: branchDataOptions,
            displayText: (item) => item,
            onChange: (value) {
              setState(() {
                selectedBranchData = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(16.w, 24.h, 16.w, 58.h),
      child: Row(
        children: [
          // Clear Button
          Expanded(
            child: SecondaryButton(
              text: 'ล้างข้อมูล',
              height: AppLayout.defaultButtonHeight,
              onPressed: () {
                setState(() {
                  selectedDataFilter = null;
                  selectedBranchData = null;
                });
                widget.onClearFilter?.call();
              },
            ),
          ),

          16.w.horizontalSpace,

          // Search Button
          Expanded(
            child: PrimaryButton(
              text: 'ค้นหา',
              height: AppLayout.defaultButtonHeight,
              onPressed: () {
                widget.onApplyFilter?.call(
                  selectedDataFilter,
                  selectedBranchData,
                );
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to show the bottom sheet
void showProjectFilterBottomSheet({
  required BuildContext context,
  Function(String? dataFilter, String? branchData)? onApplyFilter,
  VoidCallback? onClearFilter,
}) {
  showMaterialModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isDismissible: true,
    enableDrag: true,
    builder:
        (context) => BottomSheetProjectFilter(
          onApplyFilter: onApplyFilter,
          onClearFilter: onClearFilter,
        ),
  );
}
