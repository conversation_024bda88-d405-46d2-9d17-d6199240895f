import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/core/routes/router.gr.dart';

/// Data model for consultant information
class ConsultantData {
  final String companyName;
  final bool isMember;
  final int matchingPercentage;
  final bool isFavorite;
  final VoidCallback? onMorePressed;
  final VoidCallback? onFavoritePressed;

  const ConsultantData({
    required this.companyName,
    required this.isMember,
    required this.matchingPercentage,
    this.isFavorite = false,
    this.onMorePressed,
    this.onFavoritePressed,
  });
}

class ConsultantCard extends StatelessWidget {
  final ConsultantData consultantData;

  const ConsultantCard({super.key, required this.consultantData});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildHeader(),
          16.h.verticalSpace,
          _buildMemberBadge(),
          24.h.verticalSpace,
          Divider(height: 1.h, thickness: 1, color: AppColors.borderDefault),
          24.h.verticalSpace,
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            consultantData.companyName,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.iconDefault,
              height: 1.5,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        12.w.horizontalSpace,
        GestureDetector(
          onTap: consultantData.onFavoritePressed,
          child: SvgPicture.asset(
            consultantData.isFavorite
                ? 'assets/icons/heart_filled.svg'
                : 'assets/icons/heart_outline.svg',
            width: 16.r,
            height: 16.r,
            colorFilter: ColorFilter.mode(
              consultantData.isFavorite
                  ? AppColors.iconCritical
                  : AppColors.iconDefault,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemberBadge() {
    if (!consultantData.isMember) return const SizedBox.shrink();

    return Row(
      children: [
        // Green dot indicator
        Container(
          width: 8.r,
          height: 8.r,
          decoration: const BoxDecoration(
            color: Color(0xFF16A54D),
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 8.w),
        // Member status text
        Text(
          'สมาชิก',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF36475A),
            height: 1.51,
          ),
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildMatchingInfo(),
        PrimaryButton(
          text: 'เพิ่มเติม',
          height: 40.h,
          borderRadius: 20.r,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          onPressed: () {
            context.router.push(const ConsultantInfoRoute());
          },
        ),
      ],
    );
  }

  Widget _buildMatchingInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              'assets/icons/puzzle.svg',
              height: 16.r,
              colorFilter: ColorFilter.mode(
                AppColors.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
            4.w.horizontalSpace,
            Text(
              '${consultantData.matchingPercentage} %',
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),
          ],
        ),
        4.h.verticalSpace,
        Text(
          'ผลจับคู่',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 10.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
