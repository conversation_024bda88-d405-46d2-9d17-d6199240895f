import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';

/// Data model for project information
class ProjectData {
  final String title;
  final String dateRange;
  final ProjectStats stats;
  final bool isFavorite;
  final VoidCallback? onMorePressed;
  final VoidCallback? onFavoritePressed;

  const ProjectData({
    required this.title,
    required this.dateRange,
    required this.stats,
    this.isFavorite = false,
    this.onMorePressed,
    this.onFavoritePressed,
  });
}

/// Data model for project statistics
class ProjectStats {
  final String totalCount;
  final String interestedCount;
  final String viewCount;

  const ProjectStats({
    required this.totalCount,
    required this.interestedCount,
    required this.viewCount,
  });
}

class ProjectCard extends StatelessWidget {
  final ProjectData projectData;

  const ProjectCard({super.key, required this.projectData});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildHeader(),
          36.h.verticalSpace,
          Divider(height: 1.h, thickness: 1, color: AppColors.borderDefault),
          24.h.verticalSpace,
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                projectData.title,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.iconDefault,
                  height: 1.5,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            12.w.horizontalSpace,
            GestureDetector(
              onTap: projectData.onFavoritePressed,
              child: SvgPicture.asset(
                projectData.isFavorite
                    ? 'assets/icons/heart_filled.svg'
                    : 'assets/icons/heart_outline.svg',
                width: 16.r,
                height: 16.r,
                colorFilter: ColorFilter.mode(
                  projectData.isFavorite
                      ? AppColors.iconCritical
                      : AppColors.iconDefault,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
        12.h.verticalSpace,
        Text(
          projectData.dateRange,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            ProjectInfoItem(
              icon: 'assets/icons/hand_shake.svg',
              count: projectData.stats.totalCount,
              label: 'ทั้งหมด',
            ),
            16.w.horizontalSpace,
            ProjectInfoItem(
              icon: 'assets/icons/inbox_in.svg',
              count: projectData.stats.interestedCount,
              label: 'ผู้สนใจ',
            ),
            16.w.horizontalSpace,
            ProjectInfoItem(
              icon: 'assets/icons/eye.svg',
              count: projectData.stats.viewCount,
              label: 'การดู',
            ),
          ],
        ),
        PrimaryButton(
          text: 'เพิ่มเติม',
          height: 40.h,
          borderRadius: 20.r,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          onPressed: projectData.onMorePressed ?? () {},
        ),
      ],
    );
  }
}

/// Reusable info item component for project statistics
///
/// Displays an icon, count, and label in a vertical layout.
/// Used within the ProjectCard to show statistics like total count,
/// interested count, and view count.
class ProjectInfoItem extends StatelessWidget {
  final String icon;
  final String count;
  final String label;

  const ProjectInfoItem({
    super.key,
    required this.icon,
    required this.count,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SvgPicture.asset(
              icon,
              height: 16.r,
              colorFilter: ColorFilter.mode(
                AppColors.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
            4.w.horizontalSpace,
            Text(
              count,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),
          ],
        ),
        4.h.verticalSpace,
        Text(
          label,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 10.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
