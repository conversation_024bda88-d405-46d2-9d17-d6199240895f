import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/features/search/presentation/components/card_project_result.dart';

@RoutePage()
class SearchResultProjectPage extends StatefulWidget {
  const SearchResultProjectPage({super.key});

  @override
  State<SearchResultProjectPage> createState() =>
      _SearchResultProjectPageState();
}

class _SearchResultProjectPageState extends State<SearchResultProjectPage> {
  late List<ProjectResultData> _searchResults;

  @override
  void initState() {
    super.initState();
    _initializeMockData();
  }

  void _initializeMockData() {
    // Mock data based on Figma design
    _searchResults = [
      ProjectResultData(
        title:
            'สำรวจออกแบบเพื่อก่อสร้างเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8 ตำบลบ้านใหม่ อำเภอพระนคร...',
        organizationName: 'บริษัท วีวาสนาดี จำกัด',
        announcementDate: '29 ม.ค. 2568 - 30 มี.ค. 2568',
        branch: 'BU',
        viewCount: 100,
        matchingPercentage: 100,
      ),
      ProjectResultData(
        title:
            'สำรวจออกแบบเพื่อก่อสร้างเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8 ตำบลบ้านใหม่ อำเภอพระนคร...',
        organizationName: 'บริษัท วีวาสนาดี จำกัด',
        announcementDate: '29 ม.ค. 2568 - 30 มี.ค. 2568',
        branch: 'BU',
        viewCount: 100,
        matchingPercentage: 95,
      ),
      ProjectResultData(
        title:
            'สำรวจออกแบบเพื่อก่อสร้างเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8 ตำบลบ้านใหม่ อำเภอพระนคร...',
        organizationName: 'บริษัท วีวาสนาดี จำกัด',
        announcementDate: '29 ม.ค. 2568 - 30 มี.ค. 2568',
        branch: 'BU',
        viewCount: 100,
        matchingPercentage: 90,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: AppBarCommon(title: l10n.allProjectsTitle),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            24.h.verticalSpace,
            _buildResultsHeader(),
            24.h.verticalSpace,
            Expanded(child: _buildProjectList()),
            24.h.verticalSpace,
          ],
        ),
      ),
    );
  }

  Widget _buildResultsHeader() {
    final l10n = AppLocalizations.of(context)!;
    return Text(
      l10n.totalItemsCount(100),
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.iconDefault,
        height: 1.51,
      ),
    );
  }

  Widget _buildProjectList() {
    return ListView.separated(
      physics: const BouncingScrollPhysics(),
      itemCount: _searchResults.length,
      separatorBuilder: (context, index) => 24.h.verticalSpace,
      itemBuilder: (context, index) {
        return ProjectResultCard(projectData: _searchResults[index]);
      },
    );
  }
}
