import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';

/// Data model for project detail information
class ProjectDetailData {
  final String title;
  final String organizationName;
  final String phone;
  final String email;
  final String organizationType;
  final String website;
  final String projectObjective;
  final String projectScope;
  final String projectDuration;
  final String announcementDate;
  final String branchInfo;
  final List<String> expertise;
  final String serviceBranch;
  final List<String> serviceDetails;
  final String keywords;
  final String minProjects;
  final String consultantExperience;
  final String contractValue;
  final String projectCount;
  final String projectType;
  final String consultantCount;
  final String consultantType;
  final String consultantLevel;
  final String registrationCertificate;
  final String matchingResult;
  final int viewCount;
  final int matchingPercentage;

  const ProjectDetailData({
    required this.title,
    required this.organizationName,
    required this.phone,
    required this.email,
    required this.organizationType,
    required this.website,
    required this.projectObjective,
    required this.projectScope,
    required this.projectDuration,
    required this.announcementDate,
    required this.branchInfo,
    required this.expertise,
    required this.serviceBranch,
    required this.serviceDetails,
    required this.keywords,
    required this.minProjects,
    required this.consultantExperience,
    required this.contractValue,
    required this.projectCount,
    required this.projectType,
    required this.consultantCount,
    required this.consultantType,
    required this.consultantLevel,
    required this.registrationCertificate,
    required this.matchingResult,
    required this.viewCount,
    required this.matchingPercentage,
  });
}

@RoutePage()
class ProjectDetailPage extends StatefulWidget {
  const ProjectDetailPage({super.key});

  @override
  State<ProjectDetailPage> createState() => _ProjectDetailPageState();
}

class _ProjectDetailPageState extends State<ProjectDetailPage> {
  late ProjectDetailData _projectData;

  @override
  void initState() {
    super.initState();
    _initializeMockData();
  }

  void _initializeMockData() {
    // Mock data based on Figma design
    _projectData = ProjectDetailData(
      title:
          'สำรวจออกแบบเพื่อก่อสร้างเขื่อนป้องกันตลิ่งพังที่บ้าน ใหม่ หมู่ที่ 3-8 ตำบลบ้านใหม่ อำเภอพระนครศรีอยุธยา',
      organizationName: 'บริษัท วีวาสนาดี',
      phone: '************',
      email: '<EMAIL>',
      organizationType: 'หน่วยงานภาคเอกชน',
      website: 'www.wewasanad.com',
      projectObjective:
          'เพื่อสนับสนุนการเชื่อมโยงทางน้ำระหว่างภาคใต้ตอนบนฝั่งตะวันออกและฝั่งตะวันตก อีกทั้งเพื่อลดต้นทุนการขนส่ง ประหยัดพลังงานและช่วยลดการจราจรแออัดและอุบัติเหตุ โดยการกระจายการขนส่งทางบกจากภาคกลาง ภาคตะวัน ออก ไปยังภาคตะวันตกเฉียงใต้',
      projectScope:
          'เพื่อสนับสนุนการเชื่อมโยงทางน้ำระหว่างภาคใต้ตอนบนฝั่งตะวันออกและฝั่งตะวันตก อีกทั้งเพื่อลดต้นทุนการขนส่ง ประหยัดพลังงานและช่วยลดการจราจรแออัด',
      projectDuration: '12 ก.ย. 2568 - 11 ก.ย. 2568',
      announcementDate: '30 ก.ย. 2568 - 30 ธ.ค. 2568',
      branchInfo: 'AG : การเกษตรและการพัฒนาชนบท',
      expertise: [
        'A102 : การวางแผนพัฒนาชนบท : Rural Development Planning',
        'A103 : การสำรวจและสถิติทางการเกษตร : Agricultural Censuses And Statistics',
        'A104 : การผลิตภาคเกษตรโดยทั่วไป : General Agricultural Production',
      ],
      serviceBranch: 'ED : การศึกษา',
      serviceDetails: [
        'C101 : เศรษฐศาสตร์การศึกษาและการเงินรวมถึงอัตราผลตอบแทนและการวิเคราะห์ต้นทุนผลประโยชน์และความคุ้มทุน : Education Economics And Financing (Including Rate or Return, Cost Benefit and Co',
        'C102 : การศึกษาการบริหารแบบมหภาค : Education Macro-Administration',
        'C103 : ส่ำมะโนประชากรการจัดการศึกษา : Demography/School Mapping',
      ],
      keywords: 'โครงการ บริหาร พัฒนา',
      minProjects: 'อย่างน้อย 1 โครงการ',
      consultantExperience: 'อย่างน้อย 2 ปี',
      contractValue: 'อย่างน้อย 2 ล้านบาท',
      projectCount: 'อย่างน้อย 1 โครงการ',
      projectType: 'ในประเทศ',
      consultantCount: 'อย่างน้อย 5 คน',
      consultantType: 'ที่ปรึกษาอิสระ',
      consultantLevel: 'ระดับ 1',
      registrationCertificate: 'ไม่ระบุสาขา',
      matchingResult: 'มากกว่าเท่ากับ 50 %',
      viewCount: 100,
      matchingPercentage: 100,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: AppBarCommon(
        title: AppLocalizations.of(context)!.projectDetailPageTitle,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            24.h.verticalSpace,
            _buildProjectHeaderCard(),
            24.h.verticalSpace,
            _buildOrganizationInfoCard(),
            24.h.verticalSpace,
            _buildProjectInfoCard(),
            24.h.verticalSpace,
            _buildBranchExpertiseCard(),
            24.h.verticalSpace,
            _buildRequiredQualificationsCard(),
            24.h.verticalSpace,
            _buildDownloadButton(),
            24.h.verticalSpace,
          ],
        ),
      ),
    );
  }

  Widget _buildProjectHeaderCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Project Title
          Text(
            _projectData.title,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.iconDefault,
              height: 1.51,
            ),
          ),
          24.h.verticalSpace,
          // Stats Row
          Row(
            children: [
              _buildStatItem(
                'assets/icons/eye.svg',
                _projectData.viewCount.toString(),
                AppLocalizations.of(context)!.viewsLabel,
              ),
              24.w.horizontalSpace,
              _buildStatItem(
                'assets/icons/puzzle.svg',
                '${_projectData.matchingPercentage}%',
                AppLocalizations.of(context)!.matchingResultLabel,
              ),
            ],
          ),
          24.h.verticalSpace,
          // Divider with padding
          Container(
            height: 1.h,
            color: AppColors.borderDefault,
            margin: EdgeInsets.symmetric(horizontal: 0.w),
          ),
          24.h.verticalSpace,
          // Contact Button
          Align(
            alignment: Alignment.centerRight,
            child: SecondaryButton(
              text: AppLocalizations.of(context)!.sendContactInfoButton,
              height: 40.h,
              borderRadius: 20.r,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              onPressed: _handleSendContactInfo,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String iconPath, String value, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              iconPath,
              height: 16.r,
              colorFilter: ColorFilter.mode(
                AppColors.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
            4.w.horizontalSpace,
            Text(
              value,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                height: 1.51,
              ),
            ),
          ],
        ),
        4.h.verticalSpace,
        Text(
          label,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 10.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.51,
          ),
        ),
      ],
    );
  }

  Widget _buildOrganizationInfoCard() {
    return _buildInfoCard(
      title: AppLocalizations.of(context)!.organizationInformationTitle,
      children: [
        _buildInfoRow(
          AppLocalizations.of(context)!.organizationName,
          _projectData.organizationName,
        ),
        _buildContactInfoRow('assets/icons/phone.svg', _projectData.phone),
        _buildEmailButton(),
        _buildInfoRow(
          AppLocalizations.of(context)!.organizationLabel,
          _projectData.organizationType,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.organizationWebsiteLabel,
          _projectData.website,
        ),
      ],
    );
  }

  Widget _buildContactInfoRow(String iconPath, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        children: [
          SvgPicture.asset(
            iconPath,
            height: 16.r,
            colorFilter: ColorFilter.mode(
              AppColors.iconDefault,
              BlendMode.srcIn,
            ),
          ),
          8.w.horizontalSpace,
          Text(
            value,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.iconDefault,
              height: 1.51,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailButton() {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: InkWell(
        onTap: _handleEmailPress,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 4.h),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/icons/envelope.svg',
                height: 16.r,
                colorFilter: ColorFilter.mode(
                  AppColors.iconDefault,
                  BlendMode.srcIn,
                ),
              ),
              8.w.horizontalSpace,
              Text(
                _projectData.email,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.iconDefault,
                  height: 1.51,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectInfoCard() {
    return _buildInfoCard(
      title: AppLocalizations.of(context)!.projectInformationTitle,
      children: [
        _buildInfoRow(
          AppLocalizations.of(context)!.projectNameLabel,
          _projectData.title,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.projectObjectiveLabel,
          _projectData.projectObjective,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.projectScopeLabel,
          _projectData.projectScope,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.projectDurationLabel,
          _projectData.projectDuration,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.projectAnnouncementDateLabel,
          _projectData.announcementDate,
        ),
      ],
    );
  }

  Widget _buildBranchExpertiseCard() {
    return _buildInfoCard(
      title: AppLocalizations.of(context)!.branchExpertiseInformationTitle,
      children: [
        _buildInfoRow(
          AppLocalizations.of(context)!.branchDataLabel,
          _projectData.branchInfo,
        ),
        _buildMultiLineInfoRow(
          AppLocalizations.of(context)!.expertise,
          _projectData.expertise,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.branchDataLabel,
          _projectData.serviceBranch,
        ),
        _buildMultiLineInfoRow(
          AppLocalizations.of(context)!.expertise,
          _projectData.serviceDetails,
        ),
      ],
    );
  }

  Widget _buildRequiredQualificationsCard() {
    return _buildInfoCard(
      title: AppLocalizations.of(context)!.requiredQualificationsTitle,
      children: [
        _buildServiceInfoRow(),
        _buildInfoRow(
          AppLocalizations.of(context)!.keywordsLabel,
          _projectData.keywords,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.matchingProjectsCountLabel,
          _projectData.minProjects,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.consultantExperienceLabel,
          _projectData.consultantExperience,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.consultantContractValueLabel,
          _projectData.contractValue,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.projectCountLabel,
          _projectData.projectCount,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.projectTypeLabel,
          _projectData.projectType,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.consultantPersonnelCountLabel,
          _projectData.consultantCount,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.consultantTypeLabel,
          _projectData.consultantType,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.consultantLevelLabel,
          _projectData.consultantLevel,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.registrationCertificateLabel,
          _projectData.registrationCertificate,
        ),
        _buildInfoRow(
          AppLocalizations.of(context)!.matchingResultLabel,
          _projectData.matchingResult,
        ),
      ],
    );
  }

  Widget _buildServiceInfoRow() {
    const serviceText =
        '1A : การสำรวจรูปถ่าย ภาพถ่ายจากดาวเทียม , \n1F : การสำรวจดิน , 1E : การสำรวจอุทกศาสตร์\n\n1B : การศึกษาความเหมาะสมของโครงการและความเป็นไปได้';

    return _buildInfoRow(
      AppLocalizations.of(context)!.serviceLabel,
      serviceText,
    );
  }

  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.iconDefault,
              height: 1.51,
            ),
          ),
          24.h.verticalSpace,
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textSubdude,
              height: 1.51,
            ),
          ),
          8.h.verticalSpace,
          Text(
            value,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.iconDefault,
              height: 1.51,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultiLineInfoRow(String label, List<String> values) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textSubdude,
              height: 1.51,
            ),
          ),
          8.h.verticalSpace,
          ...values.map(
            (value) => Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: Text(
                value,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.iconDefault,
                  height: 1.51,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadButton() {
    return PrimaryButton(
      text: AppLocalizations.of(context)!.downloadDocumentButton,
      height: 52.h,
      borderRadius: 50.r,
      width: double.infinity,
      onPressed: _handleDownloadDocuments,
    );
  }

  void _handleSendContactInfo() {
    // Handle send contact info
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          AppLocalizations.of(context)!.sendContactInfoMessage,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.surfacePrimarySoft,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleEmailPress() {
    // Handle email press
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          AppLocalizations.of(context)!.openEmailMessage(_projectData.email),
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.surfacePrimarySoft,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleDownloadDocuments() {
    // Handle download documents
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          AppLocalizations.of(context)!.downloadDocumentMessage,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.surfacePrimarySoft,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
