import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/features/search/presentation/components/card_consult_result.dart';

@RoutePage()
class SearchResultConsultPage extends StatefulWidget {
  const SearchResultConsultPage({super.key});

  @override
  State<SearchResultConsultPage> createState() =>
      _SearchResultConsultPageState();
}

class _SearchResultConsultPageState extends State<SearchResultConsultPage> {
  late List<ConsultantResultData> _searchResults;

  @override
  void initState() {
    super.initState();
    _initializeMockData();
  }

  void _initializeMockData() {
    // Mock data based on Figma design
    _searchResults = [
      const ConsultantResultData(
        companyName:
            'บริษัท ยูไนเต็ด แอนนาลิสต์ แอนด์ เอ็นจิเนียริ่งคอน ซัลแตนท์ จำกัด',
        registrationNumber: '37',
        branch: 'BU',
        isMember: true,
      ),
      const ConsultantResultData(
        companyName:
            'บริษัท ยูไนเต็ด แอนนาลิสต์ แอนด์ เอ็นจิเนียริ่งคอน ซัลแตนท์ จำกัด',
        registrationNumber: '102',
        branch: 'BU',
        isMember: true,
      ),
      const ConsultantResultData(
        companyName:
            'บริษัท ยูไนเต็ด แอนนาลิสต์ แอนด์ เอ็นจิเนียริ่งคอน ซัลแตนท์ จำกัด',
        registrationNumber: '102',
        branch: 'BU',
        isMember: true,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: AppBarCommon(title: l10n.allConsultantsTitle),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            24.h.verticalSpace,
            _buildResultsHeader(),
            24.h.verticalSpace,
            Expanded(child: _buildConsultantList()),
            24.h.verticalSpace,
          ],
        ),
      ),
    );
  }

  Widget _buildResultsHeader() {
    final l10n = AppLocalizations.of(context)!;
    return Text(
      l10n.totalItemsCount(100),
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.iconDefault,
        height: 1.51,
      ),
    );
  }

  Widget _buildConsultantList() {
    return ListView.separated(
      physics: const BouncingScrollPhysics(),
      itemCount: _searchResults.length,
      separatorBuilder: (context, index) => 24.h.verticalSpace,
      itemBuilder: (context, index) {
        return ConsultantResultCard(consultantData: _searchResults[index]);
      },
    );
  }
}
