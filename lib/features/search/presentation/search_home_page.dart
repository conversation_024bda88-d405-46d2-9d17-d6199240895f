import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/background/main_back_ground.dart';
import 'package:mcdc/features/search/presentation/components/search_top.dart';
import 'package:mcdc/features/search/presentation/components/search_form_project.dart';
import 'package:mcdc/features/search/presentation/components/search_form_consult.dart';

@RoutePage()
class SearchHomePage extends StatefulWidget {
  const SearchHomePage({super.key});

  @override
  State<SearchHomePage> createState() => _SearchHomePageState();
}

class _SearchHomePageState extends State<SearchHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (_tabController.index != _currentTabIndex) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      resizeToAvoidBottomInset: true,
      body: MainBackground(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            const SearchTop(),
            SliverFillRemaining(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  32.h.verticalSpace,
                  _buildTabBar(),
                  _buildTabContent(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      color: AppColors.surfaceDefault,
      padding: EdgeInsets.symmetric(horizontal: 17.w),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton(
              title: l10n.searchProjectAnnouncements,
              isSelected: _currentTabIndex == 0,
              onTap: () {
                setState(() {
                  _currentTabIndex = 0;
                });
                _tabController.animateTo(0);
              },
            ),
          ),
          24.w.horizontalSpace,
          Expanded(
            child: _buildTabButton(
              title: l10n.searchConsultants,
              isSelected: _currentTabIndex == 1,
              onTap: () {
                setState(() {
                  _currentTabIndex = 1;
                });
                _tabController.animateTo(1);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color:
              isSelected ? AppColors.surfacePrimarySubdude : Colors.transparent,
          borderRadius: BorderRadius.circular(50.r),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              height: 1.5,
              color: isSelected ? AppColors.textPrimary : AppColors.textSubdude,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    return Expanded(
      child: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          // Project Search Tab
          const SearchFormProject(),
          // Consultant Search Tab
          const SearchFormConsult(),
        ],
      ),
    );
  }
}
