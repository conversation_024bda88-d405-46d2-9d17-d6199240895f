import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_dropdown_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_date_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_radio_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/dual_list_selector.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';

class SearchFormConsult extends StatefulWidget {
  const SearchFormConsult({super.key});

  @override
  State<SearchFormConsult> createState() => _SearchFormConsultState();
}

class _SearchFormConsultState extends State<SearchFormConsult> {
  final TextEditingController _registrationNumberController =
      TextEditingController();
  final TextEditingController _legalEntityNumberController =
      TextEditingController();
  final TextEditingController _agencyNameController = TextEditingController();
  final TextEditingController _searchKeywordController =
      TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Dropdown selections
  String? _selectedConsultantType;
  DateTime? _registrationStartDate;
  DateTime? _registrationEndDate;
  String? _selectedRegistrationCapital;
  String? _selectedExperienceYears;
  String? _selectedConsultantLevel;
  String? _selectedContractValue;
  String? _selectedProjectCount;
  String? _selectedProjectType;

  // Radio button selection for search condition
  String _searchCondition = 'and'; // 'and' or 'or'

  // Expertise selection
  List<ExpertiseItem> _selectedExpertiseItems = [];

  // Mock data for dropdowns
  final List<String> _consultantTypes = [
    'ที่ปรึกษาอิสระ',
    'ที่ปรึกษานิติบุคคล',
  ];

  final List<String> _registrationCapitalOptions = [
    'น้อยกว่า 1 ล้านบาท',
    '1-5 ล้านบาท',
    '5-10 ล้านบาท',
    'มากกว่า 10 ล้านบาท',
  ];

  final List<String> _experienceYearsOptions = [
    'น้อยกว่า 1 ปี',
    '1-3 ปี',
    '3-5 ปี',
    '5-10 ปี',
    'มากกว่า 10 ปี',
  ];

  final List<String> _consultantLevelOptions = [
    'ระดับ 1',
    'ระดับ 2',
    'ระดับ 3',
  ];

  final List<String> _contractValueOptions = [
    'น้อยกว่า 1 ล้านบาท',
    '1-5 ล้านบาท',
    '5-10 ล้านบาท',
    'มากกว่า 10 ล้านบาท',
  ];

  final List<String> _projectCountOptions = [
    '1-5 โครงการ',
    '6-10 โครงการ',
    '11-20 โครงการ',
    'มากกว่า 20 โครงการ',
  ];

  final List<String> _projectTypeOptions = [
    'โครงการก่อสร้าง',
    'โครงการที่ปรึกษา',
    'โครงการวิจัย',
    'โครงการพัฒนา',
  ];

  // Mock expertise data
  final List<ExpertiseItem> _availableExpertiseItems = [
    ExpertiseItem(
      id: 'AG_A101',
      code: 'A101',
      title: 'การวางแผนพัฒนาเกษตร',
      description: 'Agricultural Development Planning',
      groupHeader: 'AG : การเกษตรและการพัฒนาชนบท',
    ),
    ExpertiseItem(
      id: 'AG_A102',
      code: 'A102',
      title: 'การวางแผนพัฒนาชนบท',
      description: 'Rural Development Planning',
      groupHeader: 'AG : การเกษตรและการพัฒนาชนบท',
    ),
    ExpertiseItem(
      id: 'AG_A103',
      code: 'A103',
      title: 'การสำรวจและสถิติทางการเกษตร',
      description: 'Agricultural Censuses And Statistics',
      groupHeader: 'AG : การเกษตรและการพัฒนาชนบท',
    ),
    ExpertiseItem(
      id: 'AG_A100',
      code: 'A100',
      title: 'เกษตรและพัฒนาชนบท',
      description: 'Agriculture And Rural Development',
      groupHeader: 'AG : การเกษตรและการพัฒนาชนบท',
    ),
    ExpertiseItem(
      id: 'IT_B101',
      code: 'B101',
      title: 'การพัฒนาระบบสารสนเทศ',
      description: 'Information System Development',
      groupHeader: 'IT : เทคโนโลยีสารสนเทศ',
    ),
    ExpertiseItem(
      id: 'IT_B102',
      code: 'B102',
      title: 'การจัดการฐานข้อมูล',
      description: 'Database Management',
      groupHeader: 'IT : เทคโนโลยีสารสนเทศ',
    ),
  ];

  @override
  void dispose() {
    _registrationNumberController.dispose();
    _legalEntityNumberController.dispose();
    _agencyNameController.dispose();
    _searchKeywordController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return SingleChildScrollView(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          32.h.verticalSpace,
          _buildFormFields(),
          24.h.verticalSpace,
          _buildActionButtons(),
          // Dynamic bottom padding based on keyboard visibility
          SizedBox(height: isKeyboardVisible ? 16.h : 24.h),
          // Add extra space when keyboard is visible to ensure buttons are above keyboard
          if (isKeyboardVisible) SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Registration Number Field
        CustomTextField(
          controller: _registrationNumberController,
          label: l10n.consultantRegistrationNumberLabel,
          hintText: l10n.consultantRegistrationNumberHint,
        ),
        16.h.verticalSpace,

        // Legal Entity Number Field
        CustomTextField(
          controller: _legalEntityNumberController,
          label: l10n.legalEntityNumberLabel,
          hintText: l10n.legalEntityNumberHint,
        ),
        16.h.verticalSpace,

        // Agency Name Field
        CustomTextField(
          controller: _agencyNameController,
          label: l10n.agencyNameLabel,
          hintText: l10n.agencyNameHint,
        ),
        16.h.verticalSpace,

        // Consultant Type Dropdown
        CustomDropdownField<String>(
          label: l10n.consultantTypeSearchLabel,
          hintText: l10n.consultantTypePlaceholder,
          items: _consultantTypes,
          selectedValue: _selectedConsultantType,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedConsultantType = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Registration Date Range
        CustomDateField(
          label: l10n.registrationDateLabel,
          hintText: l10n.selectDateHint,
          initialDate: _registrationStartDate,
          onDateSelected: (date) {
            setState(() {
              _registrationStartDate = date;
            });
          },
        ),
        16.h.verticalSpace,
        CustomDateField(
          label: l10n.toDateLabel,
          hintText: l10n.selectDateHint,
          initialDate: _registrationEndDate,
          onDateSelected: (date) {
            setState(() {
              _registrationEndDate = date;
            });
          },
        ),
        16.h.verticalSpace,

        // Registration Capital Dropdown
        CustomDropdownField<String>(
          label: l10n.registrationCapitalLabel,
          hintText: l10n.registrationCapitalPlaceholder,
          items: _registrationCapitalOptions,
          selectedValue: _selectedRegistrationCapital,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedRegistrationCapital = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Experience Years Dropdown
        CustomDropdownField<String>(
          label: l10n.consultantExperienceYearsLabel,
          hintText: l10n.consultantExperienceYearsPlaceholder,
          items: _experienceYearsOptions,
          selectedValue: _selectedExperienceYears,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedExperienceYears = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Consultant Level Dropdown
        CustomDropdownField<String>(
          label: l10n.consultantLevelSearchLabel,
          hintText: l10n.consultantLevelPlaceholder,
          items: _consultantLevelOptions,
          selectedValue: _selectedConsultantLevel,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedConsultantLevel = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Contract Value Dropdown
        CustomDropdownField<String>(
          label: l10n.consultantContractValueSearchLabel,
          hintText: l10n.consultantContractValuePlaceholder,
          items: _contractValueOptions,
          selectedValue: _selectedContractValue,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedContractValue = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Project Count Dropdown
        CustomDropdownField<String>(
          label: l10n.projectCountSearchLabel,
          hintText: l10n.projectCountPlaceholder,
          items: _projectCountOptions,
          selectedValue: _selectedProjectCount,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedProjectCount = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Project Type Dropdown
        CustomDropdownField<String>(
          label: l10n.projectTypeSearchLabel,
          hintText: l10n.projectTypePlaceholder,
          items: _projectTypeOptions,
          selectedValue: _selectedProjectType,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedProjectType = value;
            });
          },
        ),
        16.h.verticalSpace,

        // Expertise Selection
        DualListSelector(
          label: l10n.expertiseDataLabel,
          availableItems: _availableExpertiseItems,
          selectedItems: _selectedExpertiseItems,
          onSelectionChanged: (selectedItems) {
            setState(() {
              _selectedExpertiseItems = selectedItems;
            });
          },
        ),
        16.h.verticalSpace,

        // Search Condition Radio Buttons
        _buildSearchConditionSection(),
        16.h.verticalSpace,

        // Search Keyword Field
        CustomTextField(
          controller: _searchKeywordController,
          label: l10n.searchKeywordLabel,
          hintText: l10n.searchKeywordHint,
        ),
      ],
    );
  }

  Widget _buildSearchConditionSection() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.searchConditionLabel,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        8.h.verticalSpace,
        Row(
          children: [
            CustomRadioButton<String>(
              value: 'and',
              groupValue: _searchCondition,
              onChanged: (value) {
                setState(() {
                  _searchCondition = value ?? 'and';
                });
              },
              label: l10n.searchConditionAnd,
            ),
            24.w.horizontalSpace,
            CustomRadioButton<String>(
              value: 'or',
              groupValue: _searchCondition,
              onChanged: (value) {
                setState(() {
                  _searchCondition = value ?? 'and';
                });
              },
              label: l10n.searchConditionOr,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.clearDataButton,
            onPressed: _clearForm,
          ),
        ),
        16.w.horizontalSpace,
        Expanded(
          child: PrimaryButton(text: l10n.navSearch, onPressed: _performSearch),
        ),
      ],
    );
  }

  void _clearForm() {
    final l10n = AppLocalizations.of(context)!;
    setState(() {
      _registrationNumberController.clear();
      _legalEntityNumberController.clear();
      _agencyNameController.clear();
      _searchKeywordController.clear();
      _selectedConsultantType = null;
      _registrationStartDate = null;
      _registrationEndDate = null;
      _selectedRegistrationCapital = null;
      _selectedExperienceYears = null;
      _selectedConsultantLevel = null;
      _selectedContractValue = null;
      _selectedProjectCount = null;
      _selectedProjectType = null;
      _selectedExpertiseItems.clear();
      _searchCondition = 'and';
    });

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          l10n.dataClearedMessage,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.surfaceSuccess,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _performSearch() {
    // Log search parameters for debugging
    debugPrint('Consultant Search Parameters:');
    debugPrint('Registration Number: ${_registrationNumberController.text}');
    debugPrint('Legal Entity Number: ${_legalEntityNumberController.text}');
    debugPrint('Agency Name: ${_agencyNameController.text}');
    debugPrint('Consultant Type: $_selectedConsultantType');
    debugPrint('Registration Start Date: $_registrationStartDate');
    debugPrint('Registration End Date: $_registrationEndDate');
    debugPrint('Registration Capital: $_selectedRegistrationCapital');
    debugPrint('Experience Years: $_selectedExperienceYears');
    debugPrint('Consultant Level: $_selectedConsultantLevel');
    debugPrint('Contract Value: $_selectedContractValue');
    debugPrint('Project Count: $_selectedProjectCount');
    debugPrint('Project Type: $_selectedProjectType');
    debugPrint(
      'Selected Expertise: ${_selectedExpertiseItems.map((e) => e.code).join(', ')}',
    );
    debugPrint('Search Condition: $_searchCondition');
    debugPrint('Search Keyword: ${_searchKeywordController.text}');

    // Navigate to consultant search results page
    context.router.push(const SearchResultConsultRoute());
  }
}
