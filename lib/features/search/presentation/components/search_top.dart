import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/sliver_app_bar_home.dart';

class SearchTop extends StatelessWidget {
  const SearchTop({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return SliverAppBarHome(
      title: l10n.navSearch,
      fontFamily: AppFonts.notoSansThai,
    );
  }
}
