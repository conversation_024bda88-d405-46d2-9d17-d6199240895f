import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/core/routes/router.gr.dart';

/// Data model for project information
class ProjectResultData {
  final String title;
  final String organizationName;
  final String announcementDate;
  final String branch;
  final int viewCount;
  final int matchingPercentage;

  const ProjectResultData({
    required this.title,
    required this.organizationName,
    required this.announcementDate,
    required this.branch,
    required this.viewCount,
    required this.matchingPercentage,
  });
}

class ProjectResultCard extends StatelessWidget {
  final ProjectResultData projectData;

  const ProjectResultCard({super.key, required this.projectData});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProjectContent(context),
          8.h.verticalSpace,
          Divider(height: 1.h, thickness: 1, color: AppColors.borderDefault),
          24.h.verticalSpace,
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildProjectContent(BuildContext context) {
    return Column(
      children: [
        // Project Title
        _buildProjectTitle(),
        12.h.verticalSpace,
        // Project Details
        _buildProjectDetails(context),
      ],
    );
  }

  Widget _buildProjectTitle() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            projectData.title,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.iconDefault,
              height: 1.51,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildProjectDetails(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      children: [
        // Organization Name
        _buildDetailRow(
          l10n.organizationNameWithColon,
          projectData.organizationName,
        ),
        _buildDetailRow(
          l10n.announcementDateWithColon,
          projectData.announcementDate,
        ),
        _buildDetailRow(l10n.branchWithColon, projectData.branch),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 90.w,
            child: Text(
              label,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textSubdude,
                height: 1.51,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.iconDefault,
                height: 1.51,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildStatsRow(context),
        PrimaryButton(
          text: l10n.moreDetailsButton,
          height: 40.h,
          borderRadius: 20.r,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          onPressed: () {
            context.router.push(const ProjectDetailRoute());
          },
        ),
      ],
    );
  }

  Widget _buildStatsRow(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        // View Count
        _buildStatItem(
          'assets/icons/eye.svg',
          projectData.viewCount.toString(),
          l10n.viewsLabel,
        ),
        24.w.horizontalSpace,
        // Matching Percentage
        _buildStatItem(
          'assets/icons/puzzle.svg',
          '${projectData.matchingPercentage}%',
          l10n.matchingResultLabel,
        ),
      ],
    );
  }

  Widget _buildStatItem(String iconPath, String value, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              iconPath,
              height: 16.r,
              colorFilter: ColorFilter.mode(
                AppColors.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
            4.w.horizontalSpace,
            Text(
              value,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                height: 1.51,
              ),
            ),
          ],
        ),
        4.h.verticalSpace,
        Text(
          label,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 10.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.51,
          ),
        ),
      ],
    );
  }
}
