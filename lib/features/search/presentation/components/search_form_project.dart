import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_dropdown_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_date_field.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/core/routes/router.gr.dart';

class SearchFormProject extends StatefulWidget {
  const SearchFormProject({super.key});

  @override
  State<SearchFormProject> createState() => _SearchFormProjectState();
}

class _SearchFormProjectState extends State<SearchFormProject> {
  final TextEditingController _projectNameController = TextEditingController();
  final TextEditingController _organizationController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String? _selectedBranch;
  DateTime? _projectStartDate;
  DateTime? _projectEndDate;
  DateTime? _announcementStartDate;
  DateTime? _announcementEndDate;

  final List<String> _branchOptions = [
    'ED : การศึกษา',
    'IT : เทคโนโลยีสารสนเทศ',
    'ENG : วิศวกรรม',
    'MED : การแพทย์',
    'AGR : เกษตรกรรม',
    'ENV : สิ่งแวดล้อม',
  ];

  @override
  void initState() {
    super.initState();
    // Set default selected branch based on Figma design
    _selectedBranch = 'ED : การศึกษา';
  }

  @override
  void dispose() {
    _projectNameController.dispose();
    _organizationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return SingleChildScrollView(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          32.h.verticalSpace,
          _buildFormFields(),
          24.h.verticalSpace,
          _buildActionButtons(),
          // Dynamic bottom padding based on keyboard visibility
          SizedBox(height: isKeyboardVisible ? 16.h : 24.h),
          // Add extra space when keyboard is visible to ensure buttons are above keyboard
          if (isKeyboardVisible) SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Project Name Field
        CustomTextField(
          controller: _projectNameController,
          label: l10n.projectNameLabel,
          hintText: l10n.projectNameHint,
        ),
        16.h.verticalSpace,

        // Organization Name Field
        CustomTextField(
          controller: _organizationController,
          label: l10n.organizationName,
          hintText: l10n.organizationNameHint,
        ),
        16.h.verticalSpace,

        // Branch Data Dropdown
        CustomDropdownField<String>(
          label: l10n.branchDataLabel,
          hintText: l10n.selectBranchHint,
          items: _branchOptions,
          selectedValue: _selectedBranch,
          displayText: (value) => value,
          onChange: (value) {
            setState(() {
              _selectedBranch = value;
            });
          },
        ),
        16.h.verticalSpace,
        CustomDateField(
          label: l10n.projectStartDateLabel,
          hintText: l10n.selectDateHint,
          initialDate: _projectStartDate,
          onDateSelected: (date) {
            setState(() {
              _projectStartDate = date;
            });
          },
        ),
        16.h.verticalSpace,
        CustomDateField(
          label: l10n.toDateLabel,
          hintText: l10n.selectDateHint,
          initialDate: _projectEndDate,
          onDateSelected: (date) {
            setState(() {
              _projectEndDate = date;
            });
          },
        ),
        16.h.verticalSpace,

        // Announcement Date Range Section
        CustomDateField(
          label: l10n.announcementDateLabel,
          hintText: l10n.selectDateHint,
          initialDate: _announcementStartDate,
          onDateSelected: (date) {
            setState(() {
              _announcementStartDate = date;
            });
          },
        ),
        16.h.verticalSpace,

        CustomDateField(
          label: l10n.toDateLabel,
          hintText: l10n.selectDateHint,
          initialDate: _announcementEndDate,
          onDateSelected: (date) {
            setState(() {
              _announcementEndDate = date;
            });
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.clearDataButton,
            onPressed: _clearForm,
          ),
        ),
        16.w.horizontalSpace,
        Expanded(
          child: PrimaryButton(text: l10n.navSearch, onPressed: _performSearch),
        ),
      ],
    );
  }

  void _clearForm() {
    final l10n = AppLocalizations.of(context)!;
    setState(() {
      _projectNameController.clear();
      _organizationController.clear();
      _selectedBranch = null;
      _projectStartDate = null;
      _projectEndDate = null;
      _announcementStartDate = null;
      _announcementEndDate = null;
    });

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          l10n.dataClearedMessage,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.surfaceSuccess,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _performSearch() {
    // Log search parameters for debugging
    debugPrint('Search Parameters:');
    debugPrint('Project Name: ${_projectNameController.text}');
    debugPrint('Organization: ${_organizationController.text}');
    debugPrint('Branch: $_selectedBranch');
    debugPrint('Project Start Date: $_projectStartDate');
    debugPrint('Project End Date: $_projectEndDate');
    debugPrint('Announcement Start Date: $_announcementStartDate');
    debugPrint('Announcement End Date: $_announcementEndDate');

    // Navigate to search results page
    context.router.push(const SearchResultProjectRoute());
  }
}
