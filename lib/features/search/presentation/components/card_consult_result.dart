import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/core/routes/router.gr.dart';

/// Data model for consultant information
class ConsultantResultData {
  final String companyName;
  final String registrationNumber;
  final String branch;
  final bool isMember;

  const ConsultantResultData({
    required this.companyName,
    required this.registrationNumber,
    required this.branch,
    this.isMember = true,
  });
}

class ConsultantResultCard extends StatelessWidget {
  final ConsultantResultData consultantData;

  const ConsultantResultCard({super.key, required this.consultantData});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(31, 34, 39, 0.08),
            blurRadius: 4.r,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConsultantContent(context),
          8.h.verticalSpace,
          Divider(height: 1.h, thickness: 1, color: AppColors.borderDefault),
          16.h.verticalSpace,
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildConsultantContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Company Name
        _buildCompanyName(),
        16.h.verticalSpace,
        // Member Badge
        _buildMemberBadge(context),
        16.h.verticalSpace,
        // Consultant Details
        _buildConsultantDetails(context),
      ],
    );
  }

  Widget _buildCompanyName() {
    return Text(
      consultantData.companyName,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.iconDefault,
        height: 1.51,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildMemberBadge(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (!consultantData.isMember) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Container(
          width: 10.r,
          height: 10.r,
          decoration: const BoxDecoration(
            color: Color(0xFF16A54D),
            shape: BoxShape.circle,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          l10n.member,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.iconDefault,
            height: 1.51,
          ),
        ),
      ],
    );
  }

  Widget _buildConsultantDetails(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      children: [
        // Registration Number
        _buildDetailRow(
          '${l10n.registrationNumber} :',
          consultantData.registrationNumber,
        ),
        // Branch
        _buildDetailRow('${l10n.branch} :', consultantData.branch),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 90.w,
            child: Text(
              label,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textSubdude,
                height: 1.51,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: AppFonts.notoSansThai,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.iconDefault,
                height: 1.51,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        PrimaryButton(
          text: l10n.moreDetailsButton,
          height: 40.h,
          borderRadius: 20.r,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          onPressed: () {
            context.router.push(const ConsultantInfoRoute());
          },
        ),
      ],
    );
  }
}
