import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/error/exceptions.dart';
import 'package:mcdc/core/api/network_info.dart';
import '../../domain/entities/send_otp_response.dart';
import '../../domain/entities/verify_otp_response.dart';
import '../../domain/repositories/otp_repository.dart';
import '../datasources/otp_api_data_source.dart';
import '../models/send_otp_response_model.dart';
import '../models/verify_otp_response_model.dart';

class OtpRepositoryImpl implements OtpRepository {
  final OtpApiDataSource apiDataSource;
  final NetworkInfo networkInfo;

  const OtpRepositoryImpl({
    required this.apiDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, SendOtpResponse>> sendOtp({
    required String email,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiDataSource.sendOtp(email: email);

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(message: result.errorMessage ?? 'Failed to send OTP'),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, VerifyOtpResponse>> verifyOtp({
    required String token,
    required String otp,
    required String refCode,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiDataSource.verifyOtp(
          token: token,
          otp: otp,
          refCode: refCode,
        );

        if (result.isSuccess) {
          return Right(result.data!.toEntity());
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Failed to verify OTP',
            ),
          );
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message ?? e.toString()));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
