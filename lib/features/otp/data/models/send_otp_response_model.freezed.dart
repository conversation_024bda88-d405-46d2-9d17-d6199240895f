// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_otp_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SendOtpResponseModel {

 String get token;@JsonKey(name: 'ref_code') String get refCode;@JsonKey(name: 'expires_at') String get expiresAt; String get message;
/// Create a copy of SendOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SendOtpResponseModelCopyWith<SendOtpResponseModel> get copyWith => _$SendOtpResponseModelCopyWithImpl<SendOtpResponseModel>(this as SendOtpResponseModel, _$identity);

  /// Serializes this SendOtpResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SendOtpResponseModel&&(identical(other.token, token) || other.token == token)&&(identical(other.refCode, refCode) || other.refCode == refCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,token,refCode,expiresAt,message);

@override
String toString() {
  return 'SendOtpResponseModel(token: $token, refCode: $refCode, expiresAt: $expiresAt, message: $message)';
}


}

/// @nodoc
abstract mixin class $SendOtpResponseModelCopyWith<$Res>  {
  factory $SendOtpResponseModelCopyWith(SendOtpResponseModel value, $Res Function(SendOtpResponseModel) _then) = _$SendOtpResponseModelCopyWithImpl;
@useResult
$Res call({
 String token,@JsonKey(name: 'ref_code') String refCode,@JsonKey(name: 'expires_at') String expiresAt, String message
});




}
/// @nodoc
class _$SendOtpResponseModelCopyWithImpl<$Res>
    implements $SendOtpResponseModelCopyWith<$Res> {
  _$SendOtpResponseModelCopyWithImpl(this._self, this._then);

  final SendOtpResponseModel _self;
  final $Res Function(SendOtpResponseModel) _then;

/// Create a copy of SendOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? token = null,Object? refCode = null,Object? expiresAt = null,Object? message = null,}) {
  return _then(_self.copyWith(
token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refCode: null == refCode ? _self.refCode : refCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SendOtpResponseModel implements SendOtpResponseModel {
  const _SendOtpResponseModel({required this.token, @JsonKey(name: 'ref_code') required this.refCode, @JsonKey(name: 'expires_at') required this.expiresAt, required this.message});
  factory _SendOtpResponseModel.fromJson(Map<String, dynamic> json) => _$SendOtpResponseModelFromJson(json);

@override final  String token;
@override@JsonKey(name: 'ref_code') final  String refCode;
@override@JsonKey(name: 'expires_at') final  String expiresAt;
@override final  String message;

/// Create a copy of SendOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SendOtpResponseModelCopyWith<_SendOtpResponseModel> get copyWith => __$SendOtpResponseModelCopyWithImpl<_SendOtpResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SendOtpResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SendOtpResponseModel&&(identical(other.token, token) || other.token == token)&&(identical(other.refCode, refCode) || other.refCode == refCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,token,refCode,expiresAt,message);

@override
String toString() {
  return 'SendOtpResponseModel(token: $token, refCode: $refCode, expiresAt: $expiresAt, message: $message)';
}


}

/// @nodoc
abstract mixin class _$SendOtpResponseModelCopyWith<$Res> implements $SendOtpResponseModelCopyWith<$Res> {
  factory _$SendOtpResponseModelCopyWith(_SendOtpResponseModel value, $Res Function(_SendOtpResponseModel) _then) = __$SendOtpResponseModelCopyWithImpl;
@override @useResult
$Res call({
 String token,@JsonKey(name: 'ref_code') String refCode,@JsonKey(name: 'expires_at') String expiresAt, String message
});




}
/// @nodoc
class __$SendOtpResponseModelCopyWithImpl<$Res>
    implements _$SendOtpResponseModelCopyWith<$Res> {
  __$SendOtpResponseModelCopyWithImpl(this._self, this._then);

  final _SendOtpResponseModel _self;
  final $Res Function(_SendOtpResponseModel) _then;

/// Create a copy of SendOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? token = null,Object? refCode = null,Object? expiresAt = null,Object? message = null,}) {
  return _then(_SendOtpResponseModel(
token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refCode: null == refCode ? _self.refCode : refCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
