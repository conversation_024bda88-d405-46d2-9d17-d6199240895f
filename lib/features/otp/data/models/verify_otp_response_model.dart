import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/verify_otp_response.dart';

part 'verify_otp_response_model.freezed.dart';
part 'verify_otp_response_model.g.dart';

@freezed
abstract class VerifyOtpResponseModel with _$VerifyOtpResponseModel {
  const factory VerifyOtpResponseModel({
    required String token,
    @JsonKey(name: 'verified_at') required String verifiedAt,
    required String message,
  }) = _VerifyOtpResponseModel;

  factory VerifyOtpResponseModel.fromJson(Map<String, dynamic> json) =>
      _$VerifyOtpResponseModelFromJson(json);
}

extension VerifyOtpResponseModelX on VerifyOtpResponseModel {
  VerifyOtpResponse toEntity() => VerifyOtpResponse(
    token: token,
    verifiedAt: DateTime.parse(verifiedAt),
    message: message,
  );
}
