import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/send_otp_response.dart';

part 'send_otp_response_model.freezed.dart';
part 'send_otp_response_model.g.dart';

@freezed
abstract class SendOtpResponseModel with _$SendOtpResponseModel {
  const factory SendOtpResponseModel({
    required String token,
    @JsonKey(name: 'ref_code') required String refCode,
    @JsonKey(name: 'expires_at') required String expiresAt,
    required String message,
  }) = _SendOtpResponseModel;

  factory SendOtpResponseModel.fromJson(Map<String, dynamic> json) =>
      _$SendOtpResponseModelFromJson(json);
}

extension SendOtpResponseModelX on SendOtpResponseModel {
  SendOtpResponse toEntity() => SendOtpResponse(
    token: token,
    refCode: refCode,
    expiresAt: DateTime.parse(expiresAt),
    message: message,
  );
}
