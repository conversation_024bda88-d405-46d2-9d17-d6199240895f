// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$OtpState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'OtpState()';
}


}

/// @nodoc
class $OtpStateCopyWith<$Res>  {
$OtpStateCopyWith(OtpState _, $Res Function(OtpState) __);
}


/// @nodoc


class OtpInitial implements OtpState {
  const OtpInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'OtpState.initial()';
}


}




/// @nodoc


class OtpGenerating implements OtpState {
  const OtpGenerating();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpGenerating);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'OtpState.generating()';
}


}




/// @nodoc


class OtpGenerated implements OtpState {
  const OtpGenerated({required this.email, required this.referenceCode});
  

 final  String email;
 final  String referenceCode;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OtpGeneratedCopyWith<OtpGenerated> get copyWith => _$OtpGeneratedCopyWithImpl<OtpGenerated>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpGenerated&&(identical(other.email, email) || other.email == email)&&(identical(other.referenceCode, referenceCode) || other.referenceCode == referenceCode));
}


@override
int get hashCode => Object.hash(runtimeType,email,referenceCode);

@override
String toString() {
  return 'OtpState.generated(email: $email, referenceCode: $referenceCode)';
}


}

/// @nodoc
abstract mixin class $OtpGeneratedCopyWith<$Res> implements $OtpStateCopyWith<$Res> {
  factory $OtpGeneratedCopyWith(OtpGenerated value, $Res Function(OtpGenerated) _then) = _$OtpGeneratedCopyWithImpl;
@useResult
$Res call({
 String email, String referenceCode
});




}
/// @nodoc
class _$OtpGeneratedCopyWithImpl<$Res>
    implements $OtpGeneratedCopyWith<$Res> {
  _$OtpGeneratedCopyWithImpl(this._self, this._then);

  final OtpGenerated _self;
  final $Res Function(OtpGenerated) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? email = null,Object? referenceCode = null,}) {
  return _then(OtpGenerated(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,referenceCode: null == referenceCode ? _self.referenceCode : referenceCode // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class OtpVerifying implements OtpState {
  const OtpVerifying();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpVerifying);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'OtpState.verifying()';
}


}




/// @nodoc


class OtpVerified implements OtpState {
  const OtpVerified({required this.verifiedToken});
  

 final  String verifiedToken;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OtpVerifiedCopyWith<OtpVerified> get copyWith => _$OtpVerifiedCopyWithImpl<OtpVerified>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpVerified&&(identical(other.verifiedToken, verifiedToken) || other.verifiedToken == verifiedToken));
}


@override
int get hashCode => Object.hash(runtimeType,verifiedToken);

@override
String toString() {
  return 'OtpState.verified(verifiedToken: $verifiedToken)';
}


}

/// @nodoc
abstract mixin class $OtpVerifiedCopyWith<$Res> implements $OtpStateCopyWith<$Res> {
  factory $OtpVerifiedCopyWith(OtpVerified value, $Res Function(OtpVerified) _then) = _$OtpVerifiedCopyWithImpl;
@useResult
$Res call({
 String verifiedToken
});




}
/// @nodoc
class _$OtpVerifiedCopyWithImpl<$Res>
    implements $OtpVerifiedCopyWith<$Res> {
  _$OtpVerifiedCopyWithImpl(this._self, this._then);

  final OtpVerified _self;
  final $Res Function(OtpVerified) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? verifiedToken = null,}) {
  return _then(OtpVerified(
verifiedToken: null == verifiedToken ? _self.verifiedToken : verifiedToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class OtpResending implements OtpState {
  const OtpResending();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpResending);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'OtpState.resending()';
}


}




/// @nodoc


class OtpResent implements OtpState {
  const OtpResent({required this.referenceCode});
  

 final  String referenceCode;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OtpResentCopyWith<OtpResent> get copyWith => _$OtpResentCopyWithImpl<OtpResent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpResent&&(identical(other.referenceCode, referenceCode) || other.referenceCode == referenceCode));
}


@override
int get hashCode => Object.hash(runtimeType,referenceCode);

@override
String toString() {
  return 'OtpState.resent(referenceCode: $referenceCode)';
}


}

/// @nodoc
abstract mixin class $OtpResentCopyWith<$Res> implements $OtpStateCopyWith<$Res> {
  factory $OtpResentCopyWith(OtpResent value, $Res Function(OtpResent) _then) = _$OtpResentCopyWithImpl;
@useResult
$Res call({
 String referenceCode
});




}
/// @nodoc
class _$OtpResentCopyWithImpl<$Res>
    implements $OtpResentCopyWith<$Res> {
  _$OtpResentCopyWithImpl(this._self, this._then);

  final OtpResent _self;
  final $Res Function(OtpResent) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? referenceCode = null,}) {
  return _then(OtpResent(
referenceCode: null == referenceCode ? _self.referenceCode : referenceCode // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class OtpTimerUpdated implements OtpState {
  const OtpTimerUpdated({required this.remainingSeconds, required this.canResend});
  

 final  int remainingSeconds;
 final  bool canResend;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OtpTimerUpdatedCopyWith<OtpTimerUpdated> get copyWith => _$OtpTimerUpdatedCopyWithImpl<OtpTimerUpdated>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpTimerUpdated&&(identical(other.remainingSeconds, remainingSeconds) || other.remainingSeconds == remainingSeconds)&&(identical(other.canResend, canResend) || other.canResend == canResend));
}


@override
int get hashCode => Object.hash(runtimeType,remainingSeconds,canResend);

@override
String toString() {
  return 'OtpState.timerUpdated(remainingSeconds: $remainingSeconds, canResend: $canResend)';
}


}

/// @nodoc
abstract mixin class $OtpTimerUpdatedCopyWith<$Res> implements $OtpStateCopyWith<$Res> {
  factory $OtpTimerUpdatedCopyWith(OtpTimerUpdated value, $Res Function(OtpTimerUpdated) _then) = _$OtpTimerUpdatedCopyWithImpl;
@useResult
$Res call({
 int remainingSeconds, bool canResend
});




}
/// @nodoc
class _$OtpTimerUpdatedCopyWithImpl<$Res>
    implements $OtpTimerUpdatedCopyWith<$Res> {
  _$OtpTimerUpdatedCopyWithImpl(this._self, this._then);

  final OtpTimerUpdated _self;
  final $Res Function(OtpTimerUpdated) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? remainingSeconds = null,Object? canResend = null,}) {
  return _then(OtpTimerUpdated(
remainingSeconds: null == remainingSeconds ? _self.remainingSeconds : remainingSeconds // ignore: cast_nullable_to_non_nullable
as int,canResend: null == canResend ? _self.canResend : canResend // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class OtpError implements OtpState {
  const OtpError({required this.message});
  

 final  String message;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OtpErrorCopyWith<OtpError> get copyWith => _$OtpErrorCopyWithImpl<OtpError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'OtpState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $OtpErrorCopyWith<$Res> implements $OtpStateCopyWith<$Res> {
  factory $OtpErrorCopyWith(OtpError value, $Res Function(OtpError) _then) = _$OtpErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$OtpErrorCopyWithImpl<$Res>
    implements $OtpErrorCopyWith<$Res> {
  _$OtpErrorCopyWithImpl(this._self, this._then);

  final OtpError _self;
  final $Res Function(OtpError) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(OtpError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
