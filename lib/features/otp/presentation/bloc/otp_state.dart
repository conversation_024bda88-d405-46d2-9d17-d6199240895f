import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp_state.freezed.dart';

@freezed
sealed class OtpState with _$OtpState {
  /// Initial state when OTP flow starts
  const factory OtpState.initial() = OtpInitial;

  /// State when generating/sending OTP
  const factory OtpState.generating() = OtpGenerating;

  /// State when OTP has been successfully generated and sent
  const factory OtpState.generated({
    required String email,
    required String referenceCode,
  }) = OtpGenerated;

  /// State when verifying OTP
  const factory OtpState.verifying() = OtpVerifying;

  /// State when OTP has been successfully verified
  const factory OtpState.verified({required String verifiedToken}) =
      OtpVerified;

  /// State when resending OTP
  const factory OtpState.resending() = OtpResending;

  /// State when OTP has been successfully resent
  const factory OtpState.resent({required String referenceCode}) = OtpResent;

  /// State for timer updates (to prevent unnecessary UI rebuilds)
  const factory OtpState.timerUpdated({
    required int remainingSeconds,
    required bool canResend,
  }) = OtpTimerUpdated;

  /// State when an error occurs
  const factory OtpState.error({required String message}) = OtpError;
}
