import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

class OtpInputField extends StatefulWidget {
  final int length;
  final Function(String) onCompleted;
  final Function(String) onChanged;
  final String? initialValue;

  const OtpInputField({
    super.key,
    this.length = 6,
    required this.onCompleted,
    required this.onChanged,
    this.initialValue,
  });

  @override
  State<OtpInputField> createState() => _OtpInputFieldState();
}

class _OtpInputFieldState extends State<OtpInputField> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  String _otpValue = '';

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.length,
      (index) => TextEditingController(),
    );
    _focusNodes = List.generate(widget.length, (index) => FocusNode());

    // Add listeners to focus nodes for better UX
    for (int i = 0; i < widget.length; i++) {
      _focusNodes[i].addListener(() => _onFocusChange(i));
    }

    // Set initial value if provided
    if (widget.initialValue != null && widget.initialValue!.isNotEmpty) {
      final chars = widget.initialValue!.split('');
      for (int i = 0; i < chars.length && i < widget.length; i++) {
        _controllers[i].text = chars[i];
      }
      _updateOtpValue();
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _updateOtpValue() {
    _otpValue = _controllers.map((controller) => controller.text).join();
    widget.onChanged(_otpValue);

    if (_otpValue.length == widget.length) {
      widget.onCompleted(_otpValue);
    }
  }

  void _onFocusChange(int index) {
    if (_focusNodes[index].hasFocus && _controllers[index].text.isNotEmpty) {
      // Select all text when field gains focus and has content
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_controllers[index].text.isNotEmpty) {
          _controllers[index].selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controllers[index].text.length,
          );
        }
      });
    }
  }

  void _onTextChanged(String value, int index) {
    // If user enters more than 1 character (paste), take only the first character
    if (value.length > 1) {
      value = value[0];
      _controllers[index].text = value;
      _controllers[index].selection = TextSelection.fromPosition(
        TextPosition(offset: value.length),
      );
    }

    if (value.isNotEmpty) {
      // Move to next field if not the last one
      if (index < widget.length - 1) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Remove focus from last field when completed
        _focusNodes[index].unfocus();
      }
    }
    _updateOtpValue();
  }

  bool _onKeyEvent(KeyEvent event, int index) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.backspace) {
        if (_controllers[index].text.isEmpty && index > 0) {
          // Move to previous field and clear it
          _focusNodes[index - 1].requestFocus();
          _controllers[index - 1].clear();
          _updateOtpValue();
          return true;
        } else if (_controllers[index].text.isNotEmpty) {
          // Clear current field
          _controllers[index].clear();
          _updateOtpValue();
          return true;
        }
      }
    }
    return false;
  }

  void _clearAllFields() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
    _updateOtpValue();
  }

  void _setOtpValue(String value) {
    final chars = value.split('');
    for (int i = 0; i < widget.length; i++) {
      if (i < chars.length) {
        _controllers[i].text = chars[i];
      } else {
        _controllers[i].clear();
      }
    }
    _updateOtpValue();

    // Focus on the next empty field or the last field
    int focusIndex =
        chars.length < widget.length ? chars.length : widget.length - 1;
    if (focusIndex >= 0 && focusIndex < widget.length) {
      _focusNodes[focusIndex].requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.length,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          child: SizedBox(
            width: 48.w,
            height: 74.h,
            child: Focus(
              onKeyEvent: (node, event) {
                return _onKeyEvent(event, index)
                    ? KeyEventResult.handled
                    : KeyEventResult.ignored;
              },
              child: TextFormField(
                controller: _controllers[index],
                focusNode: _focusNodes[index],
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                maxLength: 1,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: InputDecoration(
                  counterText: '',
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 8.w,
                    vertical: 16.h,
                  ),
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(
                      color:
                          _controllers[index].text.isNotEmpty
                              ? AppColors.borderPrimary
                              : AppColors.borderDefault,
                      width: 1,
                    ),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(
                      color:
                          _controllers[index].text.isNotEmpty
                              ? AppColors.borderPrimary
                              : AppColors.borderDefault,
                      width: 1,
                    ),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(
                      color: AppColors.borderPrimary,
                      width: 1,
                    ),
                  ),
                  errorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.critical, width: 1),
                  ),
                  focusedErrorBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.critical, width: 1),
                  ),
                  filled: false,
                ),
                onChanged: (value) => _onTextChanged(value, index),
                onTap: () {
                  // Select all text when field is tapped
                  if (_controllers[index].text.isNotEmpty) {
                    _controllers[index].selection = TextSelection(
                      baseOffset: 0,
                      extentOffset: _controllers[index].text.length,
                    );
                  }
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
