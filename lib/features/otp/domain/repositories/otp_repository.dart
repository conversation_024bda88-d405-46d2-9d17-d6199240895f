import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import '../entities/send_otp_response.dart';
import '../entities/verify_otp_response.dart';

abstract class OtpRepository {
  Future<Either<Failure, SendOtpResponse>> sendOtp({required String email});

  Future<Either<Failure, VerifyOtpResponse>> verifyOtp({
    required String token,
    required String otp,
    required String refCode,
  });
}
