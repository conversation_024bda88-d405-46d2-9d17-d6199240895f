import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/verify_otp_response.dart';
import '../repositories/otp_repository.dart';

class VerifyOtpUseCase implements UseCase<VerifyOtpResponse, VerifyOtpParams> {
  final OtpRepository repository;

  VerifyOtpUseCase(this.repository);

  @override
  Future<Either<Failure, VerifyOtpResponse>> call(VerifyOtpParams params) async {
    return await repository.verifyOtp(
      token: params.token,
      otp: params.otp,
      refCode: params.refCode,
    );
  }
}

class VerifyOtpParams {
  final String token;
  final String otp;
  final String refCode;

  VerifyOtpParams({
    required this.token,
    required this.otp,
    required this.refCode,
  });
}
