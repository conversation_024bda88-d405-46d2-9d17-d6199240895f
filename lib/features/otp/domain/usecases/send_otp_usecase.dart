import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/send_otp_response.dart';
import '../repositories/otp_repository.dart';

class SendOtpUseCase implements UseCase<SendOtpResponse, SendOtpParams> {
  final OtpRepository repository;

  SendOtpUseCase(this.repository);

  @override
  Future<Either<Failure, SendOtpResponse>> call(SendOtpParams params) async {
    return await repository.sendOtp(email: params.email);
  }
}

class SendOtpParams {
  final String email;

  SendOtpParams({required this.email});
}
