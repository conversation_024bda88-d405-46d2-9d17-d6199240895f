import 'package:equatable/equatable.dart';

class Ministry extends Equatable {
  final int id;
  final int appMasGovernmentSectorId;
  final String nameTh;
  final String nameEn;

  const Ministry({
    required this.id,
    required this.appMasGovernmentSectorId,
    required this.nameTh,
    required this.nameEn,
  });

  @override
  List<Object?> get props => [id, appMasGovernmentSectorId, nameTh, nameEn];

  @override
  String toString() {
    return 'Ministry(id: $id, appMasGovernmentSectorId: $appMasGovernmentSectorId, nameTh: $nameTh, nameEn: $nameEn)';
  }
}
