import 'package:equatable/equatable.dart';

/// Member type entity for government/private sector selection
class MemberType extends Equatable {
  final int id;
  final String nameTh;
  final String nameEn;

  const MemberType({
    required this.id,
    required this.nameTh,
    required this.nameEn,
  });

  @override
  List<Object?> get props => [id, nameTh, nameEn];

  MemberType copyWith({
    int? id,
    String? nameTh,
    String? nameEn,
  }) {
    return MemberType(
      id: id ?? this.id,
      nameTh: nameTh ?? this.nameTh,
      nameEn: nameEn ?? this.nameEn,
    );
  }

  /// Get display name based on current locale
  /// For now, we'll use Thai as default
  String get displayName => nameTh;
}
