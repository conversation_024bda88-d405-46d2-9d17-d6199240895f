import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/master_data/domain/entities/ministry.dart';

import '../repositories/master_data_repository.dart';

class GetMinistriesByGovernmentSector implements UseCase<List<Ministry>, int> {
  final MasterDataRepository repository;

  GetMinistriesByGovernmentSector(this.repository);

  @override
  Future<Either<Failure, List<Ministry>>> call(int governmentSectorId) async {
    final result = await repository.getMinistriesByGovernmentSector(
      governmentSectorId,
    );
    return result.fold((failure) => Left(failure), (ministries) {
      // Add "Other" option as the last item
      final ministriesWithOther = [
        ...ministries,
        const Ministry(
          id: 0,
          appMasGovernmentSectorId: 0,
          nameTh: 'อื่นๆ',
          nameEn: 'Other',
        ),
      ];
      return Right(ministriesWithOther);
    });
  }
}
