import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/government_sector.dart';
import '../repositories/master_data_repository.dart';

class GetGovernmentSectors
    implements UseCase<List<GovernmentSector>, NoParams> {
  final MasterDataRepository repository;

  GetGovernmentSectors(this.repository);

  @override
  Future<Either<Failure, List<GovernmentSector>>> call(NoParams params) async {
    final result = await repository.getGovernmentSectors();

    return result.fold((failure) => Left(failure), (sectors) {
      // Add "Other" option as the last item
      final sectorsWithOther = [
        ...sectors,
        const GovernmentSector(id: 0, nameTh: 'อื่นๆ', nameEn: 'Other'),
      ];
      return Right(sectorsWithOther);
    });
  }
}
