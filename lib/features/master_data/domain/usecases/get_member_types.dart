import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/member_type.dart';
import '../repositories/master_data_repository.dart';

class GetMemberTypes implements UseCase<List<MemberType>, NoParams> {
  final MasterDataRepository repository;

  GetMemberTypes(this.repository);

  @override
  Future<Either<Failure, List<MemberType>>> call(NoParams params) async {
    return await repository.getMemberTypes();
  }
}
