// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ministry_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MinistryModel {

 int get id; int get appMasGovernmentSectorId; String get nameTh; String get nameEn;
/// Create a copy of MinistryModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MinistryModelCopyWith<MinistryModel> get copyWith => _$MinistryModelCopyWithImpl<MinistryModel>(this as MinistryModel, _$identity);

  /// Serializes this MinistryModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MinistryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.nameTh, nameTh) || other.nameTh == nameTh)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,appMasGovernmentSectorId,nameTh,nameEn);

@override
String toString() {
  return 'MinistryModel(id: $id, appMasGovernmentSectorId: $appMasGovernmentSectorId, nameTh: $nameTh, nameEn: $nameEn)';
}


}

/// @nodoc
abstract mixin class $MinistryModelCopyWith<$Res>  {
  factory $MinistryModelCopyWith(MinistryModel value, $Res Function(MinistryModel) _then) = _$MinistryModelCopyWithImpl;
@useResult
$Res call({
 int id, int appMasGovernmentSectorId, String nameTh, String nameEn
});




}
/// @nodoc
class _$MinistryModelCopyWithImpl<$Res>
    implements $MinistryModelCopyWith<$Res> {
  _$MinistryModelCopyWithImpl(this._self, this._then);

  final MinistryModel _self;
  final $Res Function(MinistryModel) _then;

/// Create a copy of MinistryModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? appMasGovernmentSectorId = null,Object? nameTh = null,Object? nameEn = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,appMasGovernmentSectorId: null == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int,nameTh: null == nameTh ? _self.nameTh : nameTh // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _MinistryModel implements MinistryModel {
  const _MinistryModel({required this.id, required this.appMasGovernmentSectorId, required this.nameTh, required this.nameEn});
  factory _MinistryModel.fromJson(Map<String, dynamic> json) => _$MinistryModelFromJson(json);

@override final  int id;
@override final  int appMasGovernmentSectorId;
@override final  String nameTh;
@override final  String nameEn;

/// Create a copy of MinistryModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MinistryModelCopyWith<_MinistryModel> get copyWith => __$MinistryModelCopyWithImpl<_MinistryModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MinistryModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MinistryModel&&(identical(other.id, id) || other.id == id)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.nameTh, nameTh) || other.nameTh == nameTh)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,appMasGovernmentSectorId,nameTh,nameEn);

@override
String toString() {
  return 'MinistryModel(id: $id, appMasGovernmentSectorId: $appMasGovernmentSectorId, nameTh: $nameTh, nameEn: $nameEn)';
}


}

/// @nodoc
abstract mixin class _$MinistryModelCopyWith<$Res> implements $MinistryModelCopyWith<$Res> {
  factory _$MinistryModelCopyWith(_MinistryModel value, $Res Function(_MinistryModel) _then) = __$MinistryModelCopyWithImpl;
@override @useResult
$Res call({
 int id, int appMasGovernmentSectorId, String nameTh, String nameEn
});




}
/// @nodoc
class __$MinistryModelCopyWithImpl<$Res>
    implements _$MinistryModelCopyWith<$Res> {
  __$MinistryModelCopyWithImpl(this._self, this._then);

  final _MinistryModel _self;
  final $Res Function(_MinistryModel) _then;

/// Create a copy of MinistryModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? appMasGovernmentSectorId = null,Object? nameTh = null,Object? nameEn = null,}) {
  return _then(_MinistryModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,appMasGovernmentSectorId: null == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int,nameTh: null == nameTh ? _self.nameTh : nameTh // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
