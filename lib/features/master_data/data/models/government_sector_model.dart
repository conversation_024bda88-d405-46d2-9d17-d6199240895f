import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/government_sector.dart';

part 'government_sector_model.freezed.dart';
part 'government_sector_model.g.dart';

@freezed
abstract class GovernmentSectorModel with _$GovernmentSectorModel {
  const factory GovernmentSectorModel({
    required int id,
    required String nameTh,
    required String nameEn,
  }) = _GovernmentSectorModel;

  factory GovernmentSectorModel.fromJson(Map<String, dynamic> json) =>
      _$GovernmentSectorModelFromJson(json);

  // Custom fromJson to handle API field names
  factory GovernmentSectorModel.fromApiJson(Map<String, dynamic> json) {
    return GovernmentSectorModel(
      id: json['id'] as int,
      nameTh: json['name_th'] as String,
      nameEn: json['name_en'] as String,
    );
  }
}

extension GovernmentSectorModelX on GovernmentSectorModel {
  /// Convert model to domain entity
  GovernmentSector toEntity() {
    return GovernmentSector(id: id, nameTh: nameTh, nameEn: nameEn);
  }
}
