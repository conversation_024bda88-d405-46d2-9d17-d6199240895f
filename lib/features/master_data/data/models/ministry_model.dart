import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/master_data/domain/entities/ministry.dart';
import '../../../user/domain/entities/organization.dart';

part 'ministry_model.freezed.dart';
part 'ministry_model.g.dart';

@freezed
abstract class MinistryModel with _$MinistryModel {
  const factory MinistryModel({
    required int id,
    required int appMasGovernmentSectorId,
    required String nameTh,
    required String nameEn,
  }) = _MinistryModel;

  factory MinistryModel.fromJson(Map<String, dynamic> json) =>
      _$MinistryModelFromJson(json);

  // Custom fromJson to handle API field names
  factory MinistryModel.fromApiJson(Map<String, dynamic> json) {
    return MinistryModel(
      id: json['id'] as int,
      appMasGovernmentSectorId: json['app_mas_government_sector_id'] as int,
      nameTh: json['name_th'] as String,
      nameEn: json['name_en'] as String,
    );
  }
}

extension MinistryModelExtension on MinistryModel {
  /// Convert MinistryModel to Ministry entity
  Ministry toEntity() {
    return Ministry(
      id: id,
      appMasGovernmentSectorId: appMasGovernmentSectorId,
      nameTh: nameTh,
      nameEn: nameEn,
    );
  }
}
