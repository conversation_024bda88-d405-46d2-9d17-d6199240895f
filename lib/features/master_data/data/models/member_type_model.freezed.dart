// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'member_type_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MemberTypeModel {

 int get id; String get nameTh; String get nameEn;
/// Create a copy of MemberTypeModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemberTypeModelCopyWith<MemberTypeModel> get copyWith => _$MemberTypeModelCopyWithImpl<MemberTypeModel>(this as MemberTypeModel, _$identity);

  /// Serializes this MemberTypeModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberTypeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.nameTh, nameTh) || other.nameTh == nameTh)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameTh,nameEn);

@override
String toString() {
  return 'MemberTypeModel(id: $id, nameTh: $nameTh, nameEn: $nameEn)';
}


}

/// @nodoc
abstract mixin class $MemberTypeModelCopyWith<$Res>  {
  factory $MemberTypeModelCopyWith(MemberTypeModel value, $Res Function(MemberTypeModel) _then) = _$MemberTypeModelCopyWithImpl;
@useResult
$Res call({
 int id, String nameTh, String nameEn
});




}
/// @nodoc
class _$MemberTypeModelCopyWithImpl<$Res>
    implements $MemberTypeModelCopyWith<$Res> {
  _$MemberTypeModelCopyWithImpl(this._self, this._then);

  final MemberTypeModel _self;
  final $Res Function(MemberTypeModel) _then;

/// Create a copy of MemberTypeModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameTh = null,Object? nameEn = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameTh: null == nameTh ? _self.nameTh : nameTh // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _MemberTypeModel implements MemberTypeModel {
  const _MemberTypeModel({required this.id, required this.nameTh, required this.nameEn});
  factory _MemberTypeModel.fromJson(Map<String, dynamic> json) => _$MemberTypeModelFromJson(json);

@override final  int id;
@override final  String nameTh;
@override final  String nameEn;

/// Create a copy of MemberTypeModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemberTypeModelCopyWith<_MemberTypeModel> get copyWith => __$MemberTypeModelCopyWithImpl<_MemberTypeModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MemberTypeModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemberTypeModel&&(identical(other.id, id) || other.id == id)&&(identical(other.nameTh, nameTh) || other.nameTh == nameTh)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameTh,nameEn);

@override
String toString() {
  return 'MemberTypeModel(id: $id, nameTh: $nameTh, nameEn: $nameEn)';
}


}

/// @nodoc
abstract mixin class _$MemberTypeModelCopyWith<$Res> implements $MemberTypeModelCopyWith<$Res> {
  factory _$MemberTypeModelCopyWith(_MemberTypeModel value, $Res Function(_MemberTypeModel) _then) = __$MemberTypeModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameTh, String nameEn
});




}
/// @nodoc
class __$MemberTypeModelCopyWithImpl<$Res>
    implements _$MemberTypeModelCopyWith<$Res> {
  __$MemberTypeModelCopyWithImpl(this._self, this._then);

  final _MemberTypeModel _self;
  final $Res Function(_MemberTypeModel) _then;

/// Create a copy of MemberTypeModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameTh = null,Object? nameEn = null,}) {
  return _then(_MemberTypeModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameTh: null == nameTh ? _self.nameTh : nameTh // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
