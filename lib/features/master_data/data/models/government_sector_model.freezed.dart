// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'government_sector_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GovernmentSectorModel {

 int get id; String get nameTh; String get nameEn;
/// Create a copy of GovernmentSectorModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GovernmentSectorModelCopyWith<GovernmentSectorModel> get copyWith => _$GovernmentSectorModelCopyWithImpl<GovernmentSectorModel>(this as GovernmentSectorModel, _$identity);

  /// Serializes this GovernmentSectorModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GovernmentSectorModel&&(identical(other.id, id) || other.id == id)&&(identical(other.nameTh, nameTh) || other.nameTh == nameTh)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameTh,nameEn);

@override
String toString() {
  return 'GovernmentSectorModel(id: $id, nameTh: $nameTh, nameEn: $nameEn)';
}


}

/// @nodoc
abstract mixin class $GovernmentSectorModelCopyWith<$Res>  {
  factory $GovernmentSectorModelCopyWith(GovernmentSectorModel value, $Res Function(GovernmentSectorModel) _then) = _$GovernmentSectorModelCopyWithImpl;
@useResult
$Res call({
 int id, String nameTh, String nameEn
});




}
/// @nodoc
class _$GovernmentSectorModelCopyWithImpl<$Res>
    implements $GovernmentSectorModelCopyWith<$Res> {
  _$GovernmentSectorModelCopyWithImpl(this._self, this._then);

  final GovernmentSectorModel _self;
  final $Res Function(GovernmentSectorModel) _then;

/// Create a copy of GovernmentSectorModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameTh = null,Object? nameEn = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameTh: null == nameTh ? _self.nameTh : nameTh // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _GovernmentSectorModel implements GovernmentSectorModel {
  const _GovernmentSectorModel({required this.id, required this.nameTh, required this.nameEn});
  factory _GovernmentSectorModel.fromJson(Map<String, dynamic> json) => _$GovernmentSectorModelFromJson(json);

@override final  int id;
@override final  String nameTh;
@override final  String nameEn;

/// Create a copy of GovernmentSectorModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GovernmentSectorModelCopyWith<_GovernmentSectorModel> get copyWith => __$GovernmentSectorModelCopyWithImpl<_GovernmentSectorModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GovernmentSectorModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GovernmentSectorModel&&(identical(other.id, id) || other.id == id)&&(identical(other.nameTh, nameTh) || other.nameTh == nameTh)&&(identical(other.nameEn, nameEn) || other.nameEn == nameEn));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameTh,nameEn);

@override
String toString() {
  return 'GovernmentSectorModel(id: $id, nameTh: $nameTh, nameEn: $nameEn)';
}


}

/// @nodoc
abstract mixin class _$GovernmentSectorModelCopyWith<$Res> implements $GovernmentSectorModelCopyWith<$Res> {
  factory _$GovernmentSectorModelCopyWith(_GovernmentSectorModel value, $Res Function(_GovernmentSectorModel) _then) = __$GovernmentSectorModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameTh, String nameEn
});




}
/// @nodoc
class __$GovernmentSectorModelCopyWithImpl<$Res>
    implements _$GovernmentSectorModelCopyWith<$Res> {
  __$GovernmentSectorModelCopyWithImpl(this._self, this._then);

  final _GovernmentSectorModel _self;
  final $Res Function(_GovernmentSectorModel) _then;

/// Create a copy of GovernmentSectorModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameTh = null,Object? nameEn = null,}) {
  return _then(_GovernmentSectorModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameTh: null == nameTh ? _self.nameTh : nameTh // ignore: cast_nullable_to_non_nullable
as String,nameEn: null == nameEn ? _self.nameEn : nameEn // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
