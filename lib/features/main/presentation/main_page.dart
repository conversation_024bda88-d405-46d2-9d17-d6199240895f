import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/features/consultant_matching/presentation/matching_home_page.dart';
import 'package:mcdc/features/dashboard/presentation/dashboard_home_page.dart';
import 'package:mcdc/features/user/presentation/profile_main_page.dart';
import 'package:mcdc/features/search/presentation/search_home_page.dart';
import 'package:mcdc/features/tracking/presentation/tracking_home_page.dart';

@RoutePage()
class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'MainPage',
  );
  int currentIndex = 0;
  final pageController = PageController(initialPage: 0);
  List<StatefulWidget> contents = [
    const DashboardHomePage(),
    const TrackingHomePage(),
    const MatchingHomePage(),
    const SearchHomePage(),
    const ProfileMainPage(),
  ];

  @override
  void initState() {
    super.initState();
  }

  void _onPageChanged(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  void _changePage(int index) {
    setState(() {
      pageController.jumpToPage(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: pageController,
        onPageChanged: _onPageChanged,
        children: contents,
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          bottomNavigationBarTheme: BottomNavigationBarThemeData(
            selectedLabelStyle: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              height: kTextHeightNone,
            ),
            unselectedLabelStyle: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              height: kTextHeightNone,
            ),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: currentIndex,
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: AppColors.textPrimary,
          unselectedItemColor: AppColors.textSubdude,
          items: [
            _createBottomNavItem(
              context,
              "assets/icons/nav_home.svg",
              AppLocalizations.of(context)!.navHome,
              currentIndex == 0,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_tracking.svg",
              AppLocalizations.of(context)!.navTracking,
              currentIndex == 1,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_match.svg",
              AppLocalizations.of(context)!.navMatching,
              currentIndex == 2,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_search.svg",
              AppLocalizations.of(context)!.navSearch,
              currentIndex == 3,
            ),
            _createBottomNavItem(
              context,
              "assets/icons/nav_profile.svg",
              AppLocalizations.of(context)!.navProfile,
              currentIndex == 4,
            ),
          ],
          onTap: _changePage,
        ),
      ),
    );
  }

  BottomNavigationBarItem _createBottomNavItem(
    BuildContext context,
    String svgScr,
    String title,
    bool isActive,
  ) {
    return BottomNavigationBarItem(
      icon: Padding(
        padding: const EdgeInsets.only(bottom: 4.0),
        child: SvgPicture.asset(
          svgScr,
          height: 25.h,
          colorFilter: ColorFilter.mode(
            isActive ? AppColors.textPrimary : AppColors.textSubdude,
            BlendMode.srcIn,
          ),
        ),
      ),
      label: title,
      backgroundColor: Colors.transparent,
    );
  }
}
