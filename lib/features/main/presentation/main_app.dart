import 'package:flutter/material.dart' hide Router;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/core/routes/router.dart';
import 'package:mcdc/core/constants/app_screen_size.dart';
import 'package:mcdc/core/constants/app_theme.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_bloc.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_state.dart';

class MainApp extends StatelessWidget {
  // make sure you don't initiate your router
  // inside of the build function.
  final _appRouter = AppRouter();

  MainApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    //Set the fit size (Find your UI design, look at the dimensions of the device screen and fill it in,unit in dp)
    return BlocProvider<LanguageBloc>(
      create: (context) => AppInjector.get<LanguageBloc>(),
      child: ScreenUtilInit(
        designSize: const Size(
          AppScreenSize.designWidth,
          AppScreenSize.designHeight,
        ),
        minTextAdapt: true,
        splitScreenMode: true,
        // Use builder only if you need to use library outside ScreenUtilInit context
        builder: (_, child) {
          return BlocBuilder<LanguageBloc, LanguageState>(
            builder: (context, languageState) {
              return MaterialApp.router(
                debugShowCheckedModeBanner: false,
                title: 'MCDC',
                theme: AppTheme.appTheme(),
                localizationsDelegates: [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                locale: languageState.locale,
                supportedLocales: AppLocalizations.supportedLocales,
                routerConfig: _appRouter.config(),
                // routerDelegate: _appRouter.delegate(),
                // routeInformationParser: _appRouter.defaultRouteParser(),
                builder: (ctx, child) {
                  return MediaQuery(
                    data: MediaQuery.of(
                      ctx,
                    ).copyWith(textScaler: const TextScaler.linear(1.0)),
                    child: child!, // this is the child widget
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
