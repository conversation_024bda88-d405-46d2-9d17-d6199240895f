import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import '../domain/entities/news.dart';

@RoutePage()
class NewsDetailPage extends StatelessWidget {
  final News? news;

  const NewsDetailPage({super.key, this.news});

  @override
  Widget build(BuildContext context) {
    // Use provided news or create mock data for demonstration
    final newsData = news ?? _getMockNews();

    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: const AppBarCommon(title: 'ข่าวประกาศ'),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNewsHeader(newsData),
                  24.h.verticalSpace,
                  _buildNewsContent(newsData),
                  24.h.verticalSpace,
                  if (newsData.downloadUrl != null) ...[
                    _buildDivider(),
                    24.h.verticalSpace,
                    _buildDownloadSection(),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewsHeader(News newsData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          newsData.title,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        16.h.verticalSpace,
        _buildMetaInfo(newsData),
      ],
    );
  }

  Widget _buildMetaInfo(News newsData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCategoryTag(newsData.category),
        8.h.verticalSpace,
        _buildDateTimeInfo(newsData.publishedDate),
      ],
    );
  }

  Widget _buildCategoryTag(String category) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          'assets/icons/tag.svg',
          width: 16.w,
          height: 16.h,
          colorFilter: const ColorFilter.mode(
            AppColors.textSubdude,
            BlendMode.srcIn,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          'ข่าวประกาศ/ข่าวประชาสัมพันธ์',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimeInfo(DateTime publishedDate) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          'assets/icons/calendar.svg',
          width: 16.w,
          height: 16.h,
          colorFilter: const ColorFilter.mode(
            AppColors.textSubdude,
            BlendMode.srcIn,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          _formatDate(publishedDate),
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          _formatTime(publishedDate),
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildNewsContent(News newsData) {
    return Text(
      newsData.content,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildDivider() {
    return Container(height: 1.h, color: AppColors.borderDefault);
  }

  Widget _buildDownloadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ดาวน์โหลดเอกสาร',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        16.h.verticalSpace,
        _buildDownloadButton(),
      ],
    );
  }

  Widget _buildDownloadButton() {
    return InkWell(
      onTap: _handleDownload,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: AppColors.surfaceCriticalSoft,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/icons/file_invoice.svg',
              width: 16.w,
              height: 16.h,
              colorFilter: const ColorFilter.mode(
                AppColors.iconWhite,
                BlendMode.srcIn,
              ),
            ),
            8.w.horizontalSpace,
            Text(
              'ดาวน์โหลด PDF',
              style: TextStyle(
                fontFamily: AppFonts.kanit,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textDefaultWhite,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day} ${_getThaiMonth(date.month)} ${date.year + 543}';
  }

  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _getThaiMonth(int month) {
    const thaiMonths = [
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ];

    if (month < 1 || month > 12) {
      return 'ม.ค.';
    }

    return thaiMonths[month - 1];
  }

  News _getMockNews() {
    return News(
      id: '1',
      title:
          'ข้อมูลเชิงสถิติการให้บริการของศูนย์ข้อมูลที่ปรึกษา ไตรมาสที่ 1 ปีงบประมาณ พ.ศ. 2568',
      content:
          '''ศูนย์ข้อมูลและเทคโนโลยีสารสนเทศ (ศทส.) ขอรายงานผลการให้บริการของศูนย์ข้อมูลที่ปรึกษา (ศท.)

ณ วันที่ 31 ธันวาคม 2567 ดังนี้

1. ภาพรวมสถานะการขึ้นทะเบียนที่ปรึกษาณ วันที่ 31 ธันวาคม 2567 มีจำนวนที่ปรึกษา
ที่ขึ้นทะเบียนกับ ศท. รวมทั้งสิ้น 3,195 ราย ประกอบด้วยที่ปรึกษาอิสระ 602 ราย และที่ปรึกษา
นิติบุคคล 2,593 ราย โดยมีรายได้ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา รวมทั้งสิ้น 29,660,000 บาท

2. การให้บริการของ ศท. ประจำไตรมาสที่ 1 (วันที่ 1 ตุลาคม 2567 จนถึงวันที่ 31 ธันวาคม 2567) สรุปได้ดังนี้

   2.1 จำนวนที่ปรึกษาที่ขอรับบริการ รวมทั้งสิ้น 235 รายแบ่งเป็นที่ปรึกษาที่ขึ้นทะเบียนที่ปรึกษา
จำนวน 70 ราย ที่ปรึกษาที่เพิ่มเติมผลงานที่ปรึกษาจำนวน 50 ราย ที่ปรึกษาที่เปลี่ยนแปลงข้อมูลที่ปรึกษา จำนวน 33 ราย
และที่ปรึกษาที่รายงานข้อมูลสถานะการขึ้นทะเบียนที่ปรึกษา จำนวน 82 ราย

   2.2 จำนวนการขอรายชื่อที่ปรึกษาจากหน่วยงานของรัฐ รวมทั้งสิ้น 5 ราย และจำนวน การดาวน์โหลด
รายงานการค้นหารายชื่อที่ปรึกษาจากเว็บไซต์ของ ศท. (www.consultant.pdmo.go.th) รวมทั้งสิ้น 1,661 ครั้ง

   2.3 รายได้ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา รวมทั้งสิ้น 615,000 บาท''',
      category: 'ข่าวประกาศ',
      publishedDate: DateTime(2567, 12, 9, 13, 1),
      downloadUrl: 'https://example.com/news-document.pdf',
      author: 'ศูนย์ข้อมูลและเทคโนโลยีสารสนเทศ',
    );
  }

  void _handleDownload() {
    // TODO: Implement download functionality
    debugPrint('Download PDF requested');
  }
}
