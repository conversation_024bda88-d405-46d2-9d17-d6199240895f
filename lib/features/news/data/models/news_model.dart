import '../../domain/entities/news.dart';

/// News data model for data layer operations
class NewsModel extends News {
  const NewsModel({
    required super.id,
    required super.title,
    required super.content,
    required super.category,
    required super.publishedDate,
    super.downloadUrl,
    super.imageUrl,
    super.author,
  });

  /// Creates a [NewsModel] from JSON
  factory NewsModel.fromJson(Map<String, dynamic> json) {
    return NewsModel(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      category: json['category'] as String,
      publishedDate: DateTime.parse(json['publishedDate'] as String),
      downloadUrl: json['downloadUrl'] as String?,
      imageUrl: json['imageUrl'] as String?,
      author: json['author'] as String? ?? '',
    );
  }

  /// Converts [NewsModel] to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'publishedDate': publishedDate.toIso8601String(),
      'downloadUrl': downloadUrl,
      'imageUrl': imageUrl,
      'author': author,
    };
  }

  /// Creates a [NewsModel] from [News] entity
  factory NewsModel.fromEntity(News news) {
    return NewsModel(
      id: news.id,
      title: news.title,
      content: news.content,
      category: news.category,
      publishedDate: news.publishedDate,
      downloadUrl: news.downloadUrl,
      imageUrl: news.imageUrl,
      author: news.author,
    );
  }

  /// Converts [NewsModel] to [News] entity
  News toEntity() {
    return News(
      id: id,
      title: title,
      content: content,
      category: category,
      publishedDate: publishedDate,
      downloadUrl: downloadUrl,
      imageUrl: imageUrl,
      author: author,
    );
  }

  @override
  NewsModel copyWith({
    String? id,
    String? title,
    String? content,
    String? category,
    DateTime? publishedDate,
    String? downloadUrl,
    String? imageUrl,
    String? author,
  }) {
    return NewsModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      publishedDate: publishedDate ?? this.publishedDate,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      author: author ?? this.author,
    );
  }
}
