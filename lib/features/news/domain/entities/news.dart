import 'package:equatable/equatable.dart';

/// News entity representing a news article
class News extends Equatable {
  final String id;
  final String title;
  final String content;
  final String category;
  final DateTime publishedDate;
  final String? downloadUrl;
  final String? imageUrl;
  final String author;

  const News({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.publishedDate,
    this.downloadUrl,
    this.imageUrl,
    this.author = '',
  });

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        category,
        publishedDate,
        downloadUrl,
        imageUrl,
        author,
      ];

  News copyWith({
    String? id,
    String? title,
    String? content,
    String? category,
    DateTime? publishedDate,
    String? downloadUrl,
    String? imageUrl,
    String? author,
  }) {
    return News(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      publishedDate: publishedDate ?? this.publishedDate,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      author: author ?? this.author,
    );
  }
}
