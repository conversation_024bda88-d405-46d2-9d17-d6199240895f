import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';

@RoutePage()
class ApplicationPolicyPage extends StatelessWidget {
  const ApplicationPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.appPolicy),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPolicyTitle(l10n),
            SizedBox(height: 24.h),
            _buildObjectiveSection(l10n),
            SizedBox(height: 24.h),
            _buildTermsSection(l10n),
            SizedBox(height: 24.h),
            _buildRightsSection(l10n),
            SizedBox(height: 24.h),
            _buildLinksSection(l10n),
            SizedBox(height: 24.h),
            _buildDisclaimerSection(l10n),
            SizedBox(height: 24.h),
            _buildOwnershipSection(l10n),
            SizedBox(height: 24.h),
            _buildLawSection(l10n),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildPolicyTitle(AppLocalizations l10n) {
    return Text(
      l10n.appPolicyTitle,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildObjectiveSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyObjectiveTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyObjectiveContent),
      ],
    );
  }

  Widget _buildTermsSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyTermsTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyTermsContent),
      ],
    );
  }

  Widget _buildRightsSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyRightsTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyRightsContent),
      ],
    );
  }

  Widget _buildLinksSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyLinksTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyLinksContent),
      ],
    );
  }

  Widget _buildDisclaimerSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyDisclaimerTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyDisclaimerContent),
      ],
    );
  }

  Widget _buildOwnershipSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyOwnershipTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyOwnershipContent),
      ],
    );
  }

  Widget _buildLawSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.appPolicyLawTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.appPolicyLawContent),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildSectionContent(String content) {
    return Text(
      content,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }
}
