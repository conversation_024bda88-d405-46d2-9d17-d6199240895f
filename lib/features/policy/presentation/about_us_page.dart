import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';

@RoutePage()
class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.aboutUsPageTitle),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAppTitle(l10n),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildDescription(l10n),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildDetailedContent(l10n),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildAppTitle(AppLocalizations l10n) {
    return Text(
      l10n.aboutUsAppTitle,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildDescription(AppLocalizations l10n) {
    return Text(
      l10n.aboutUsDescription,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildDetailedContent(AppLocalizations l10n) {
    return Text(
      l10n.aboutUsDetailedContent,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }
}
