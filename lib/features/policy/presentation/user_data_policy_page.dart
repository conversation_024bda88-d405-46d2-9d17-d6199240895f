import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';

@RoutePage()
class UserDataPolicyPage extends StatelessWidget {
  const UserDataPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.privacyPolicy),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPolicyDate(l10n),
            SizedBox(height: 24.h),
            _buildPolicyIntroduction(l10n),
            SizedBox(height: 24.h),
            _buildPersonalDataCollectionSection(l10n),
            SizedBox(height: 24.h),
            _buildPersonalDataUsageSection(l10n),
            SizedBox(height: 24.h),
            _buildPolicyUpdateSection(l10n),
            SizedBox(height: 24.h),
            _buildContactInfoSection(l10n),
            SizedBox(height: 24.h),
            _buildContactDetailsSection(l10n),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildPolicyDate(AppLocalizations l10n) {
    return Text(
      l10n.privacyPolicyDate,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildPolicyIntroduction(AppLocalizations l10n) {
    return Text(
      l10n.privacyPolicyDescription,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildPersonalDataCollectionSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.personalDataCollectionTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.personalDataCollectionContent),
      ],
    );
  }

  Widget _buildPersonalDataUsageSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.personalDataUsageTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.personalDataUsageContent),
      ],
    );
  }

  Widget _buildPolicyUpdateSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(l10n.privacyPolicyUpdateTitle),
        SizedBox(height: 18.h),
        _buildSectionContent(l10n.privacyPolicyUpdateContent),
      ],
    );
  }

  Widget _buildContactInfoSection(AppLocalizations l10n) {
    return _buildSectionContent(l10n.contactInfoContent);
  }

  Widget _buildContactDetailsSection(AppLocalizations l10n) {
    return _buildSectionContent(l10n.contactDetails);
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildSectionContent(String content) {
    return Text(
      content,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }
}
