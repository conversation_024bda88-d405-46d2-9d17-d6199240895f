import 'dart:io';

import 'package:mcdc/features/chat/presentation/models/message_type.dart';

class ChatMessage {
  final String id;
  final String message;
  final MessageSender sender;
  final DateTime timestamp;
  final MessageType type;
  final File? imageFile;
  final String? filePath;
  final String? fileName;

  ChatMessage({
    required this.id,
    required this.message,
    required this.sender,
    required this.timestamp,
    required this.type,
    this.imageFile,
    this.filePath,
    this.fileName,
  });

  String get formattedTime {
    final hour = timestamp.hour.toString().padLeft(2, '0');
    final minute = timestamp.minute.toString().padLeft(2, '0');
    return '$hour:$minute น.';
  }
}
