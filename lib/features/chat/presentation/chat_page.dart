import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/shared/presentation/widgets/background/main_back_ground.dart';
import 'package:mcdc/features/chat/presentation/components/chat_app_bar.dart';
import 'package:mcdc/features/chat/presentation/components/chat_bottom_bar.dart';
import 'package:mcdc/features/chat/presentation/components/chat_bubble.dart';
import 'package:mcdc/features/chat/presentation/components/chat_date_divider.dart';
import 'package:mcdc/features/chat/presentation/components/chat_image_preview.dart';
import 'package:mcdc/features/chat/presentation/components/pre_chat_questions.dart';
import 'package:mcdc/features/chat/presentation/models/chat_message.dart';
import 'package:mcdc/features/chat/presentation/models/message_type.dart';

@RoutePage()
class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'ChatPage',
  );

  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _picker = ImagePicker();

  bool _showPreChatQuestions = true;
  File? _selectedImage;
  String? _selectedFilePath;
  String? _selectedFileName;

  // Mock data
  final List<ChatMessage> _messages = [
    ChatMessage(
      id: '1',
      message: 'พบที่ปรึกษาที่ใช่ อย่างไร',
      sender: MessageSender.user,
      timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
      type: MessageType.text,
    ),
    ChatMessage(
      id: '2',
      message:
          'กรอกคุณสมบัติของที่ปรึกษาที่ท่านต้องการในหน้า ค้นหา -> ที่ปรึกษา จากนั้นกดปุ่ม "ค้นหา"',
      sender: MessageSender.system,
      timestamp: DateTime.now().subtract(const Duration(minutes: 25)),
      type: MessageType.text,
    ),
  ];

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _sendMessage({
    String? text,
    File? image,
    String? filePath,
    String? fileName,
  }) {
    if ((text == null || text.trim().isEmpty) &&
        image == null &&
        filePath == null) {
      return;
    }

    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      message: text?.trim() ?? '',
      sender: MessageSender.user,
      timestamp: DateTime.now(),
      type:
          image != null
              ? MessageType.image
              : filePath != null
              ? MessageType.file
              : MessageType.text,
      imageFile: image,
      filePath: filePath,
      fileName: fileName,
    );

    setState(() {
      _messages.add(message);
      _messageController.clear();
      _selectedImage = null;
      _selectedFilePath = null;
      _selectedFileName = null;
    });

    // Scroll to bottom after message is sent
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _selectImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _selectFile() async {
    // In a real app, you would implement file picking functionality
    // For this mock example, we'll just simulate a PDF file being selected
    setState(() {
      _selectedFilePath = "file_path";
      _selectedFileName = "มีหลักฐานที่เป็นข้อเท็จจริงยืนยันมานานแล้ว";
    });
  }

  void _onQuestionSelected(String question) {
    _messageController.text = question;
    setState(() {
      _showPreChatQuestions = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.backgroundColorSecondary,
      body: MainBackground(
        child: Column(
          children: [
            // App Bar
            ChatAppBar(title: l10n.chat),

            // Chat Messages
            Expanded(
              child:
                  _showPreChatQuestions
                      ? PreChatQuestions(
                        onQuestionSelected: _onQuestionSelected,
                      )
                      : _buildChatMessages(),
            ),

            // Image Preview
            if (_selectedImage != null)
              ChatImagePreview(
                image: _selectedImage!,
                onRemove: () => setState(() => _selectedImage = null),
              ),

            // File Preview
            if (_selectedFilePath != null && _selectedFileName != null)
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: AppColors.borderDefault),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40.w,
                      height: 40.w,
                      decoration: BoxDecoration(
                        color: AppColors.textPrimary,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Icon(
                        Icons.insert_drive_file_outlined,
                        color: Colors.white,
                        size: 20.w,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedFileName!,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textDefaultDark,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'PDF',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textSubdude,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, size: 20.w),
                      onPressed:
                          () => setState(() {
                            _selectedFilePath = null;
                            _selectedFileName = null;
                          }),
                    ),
                  ],
                ),
              ),

            // Bottom Bar
            ChatBottomBar(
              controller: _messageController,
              onSendPressed: () => _sendMessage(text: _messageController.text),
              onAttachmentPressed: _selectFile,
              onCameraPressed: _selectImage,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatMessages() {
    // Group messages by date
    final Map<String, List<ChatMessage>> messagesByDate = {};

    for (final message in _messages) {
      final date = _formatDateForGrouping(message.timestamp);
      if (!messagesByDate.containsKey(date)) {
        messagesByDate[date] = [];
      }
      messagesByDate[date]!.add(message);
    }

    final sortedDates = messagesByDate.keys.toList()..sort();

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      physics: const BouncingScrollPhysics(),
      itemCount: sortedDates.length,
      itemBuilder: (context, dateIndex) {
        final date = sortedDates[dateIndex];
        final messagesForDate = messagesByDate[date]!;

        return Column(
          children: [
            ChatDateDivider(date: _formatDateForDisplay(date)),
            ...messagesForDate.map((message) {
              return Padding(
                padding: EdgeInsets.only(bottom: 16.h),
                child: ChatBubble(message: message),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _formatDateForGrouping(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }

  String _formatDateForDisplay(String groupDate) {
    final parts = groupDate.split('-');
    final year = int.parse(parts[0]);
    final month = int.parse(parts[1]);
    final day = int.parse(parts[2]);

    final date = DateTime(year, month, day);

    // Check if the date is today
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    if (date == today) {
      return 'วันนี้';
    }

    // Format as Thai date: d month year (Buddhist era)
    final thaiYear = year + 543; // Convert to Buddhist era
    final monthNames = [
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ];

    return '$day ${monthNames[month - 1]} $thaiYear';
  }
}
