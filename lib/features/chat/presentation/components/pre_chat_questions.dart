import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class PreChatQuestions extends StatelessWidget {
  final Function(String) onQuestionSelected;

  const PreChatQuestions({super.key, required this.onQuestionSelected});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildQuestionTag(
            'สามารถดูวิธีการคำนวณระบบจับคู่ที่ปรึกษาได้อย่างไร',
            isFullWidth: true,
          ),
          SizedBox(height: 16.h),
          _buildQuestionTag('พบที่ปรึกษาที่ใช่ อย่างไร'),
          SizedBox(height: 16.h),
          _buildQuestionTag('สามารถสร้างโครงการ ได้อย่างไร ?'),
          SizedBox(height: 16.h),
          _buildQuestionTag('การสมัครสมาชิก ทำอย่างไร?'),
        ],
      ),
    );
  }

  Widget _buildQuestionTag(String text, {bool isFullWidth = false}) {
    return GestureDetector(
      onTap: () => onQuestionSelected(text),
      child: Container(
        width: isFullWidth ? double.infinity : null,
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: AppColors.backgroundColorSecondary,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: AppColors.borderDefault),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
