import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class ChatBottomBar extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onSendPressed;
  final VoidCallback onAttachmentPressed;
  final VoidCallback onCameraPressed;

  const ChatBottomBar({
    super.key,
    required this.controller,
    required this.onSendPressed,
    required this.onAttachmentPressed,
    required this.onCameraPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            offset: const Offset(0, -9),
            blurRadius: 9,
          ),
        ],
      ),
      child: Row(
        children: [
          // Attachment button
          _buildIconButton(
            onPressed: onAttachmentPressed,
            icon: Icons.attach_file,
            showBorder: true,
          ),
          SizedBox(width: 8.w),

          // Text field
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.borderDefault),
              ),
              child: TextField(
                controller: controller,
                decoration: InputDecoration.collapsed(
                  hintText: 'Type a message...',
                  hintStyle: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSubdude,
                  ),
                ),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.textDefaultDark,
                ),
                minLines: 1,
                maxLines: 4,
              ),
            ),
          ),

          SizedBox(width: 8.w),

          // Camera button
          _buildIconButton(
            onPressed: onCameraPressed,
            icon: Icons.camera_alt_outlined,
            showBorder: false,
            backgroundColor: AppColors.backgroundColorSecondary,
          ),

          SizedBox(width: 8.w),

          // Send button
          _buildIconButton(
            onPressed: onSendPressed,
            icon: Icons.send,
            showBorder: false,
            backgroundColor: AppColors.textPrimary,
            iconColor: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildIconButton({
    required VoidCallback onPressed,
    required IconData icon,
    bool showBorder = false,
    Color? backgroundColor,
    Color? iconColor,
  }) {
    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(10.r),
        border: showBorder ? Border.all(color: AppColors.borderDefault) : null,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: 16.w,
          color: iconColor ?? AppColors.textDefaultDark,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
}
