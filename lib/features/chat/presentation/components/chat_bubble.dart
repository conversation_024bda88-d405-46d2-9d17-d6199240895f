import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/features/chat/presentation/models/chat_message.dart';
import 'package:mcdc/features/chat/presentation/models/message_type.dart';

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    final isUserMessage = message.sender == MessageSender.user;

    return Row(
      mainAxisAlignment:
          isUserMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (!isUserMessage) _buildAvatar(),

        Column(
          crossAxisAlignment:
              isUserMessage ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            _buildMessageContent(context, isUserMessage),
            <PERSON><PERSON><PERSON><PERSON>(height: 4.h),
            _buildTimestamp(isUserMessage),
          ],
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40.w,
      height: 40.w,
      margin: EdgeInsets.only(right: 16.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColorSecondary,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: Image.asset(
          'assets/images/bot_avatar.png',
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              Icons.smart_toy,
              size: 24.w,
              color: AppColors.textPrimary,
            );
          },
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, bool isUserMessage) {
    final maxWidth = MediaQuery.of(context).size.width * 0.7;

    switch (message.type) {
      case MessageType.text:
        return Container(
          constraints: BoxConstraints(maxWidth: maxWidth),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color:
                isUserMessage
                    ? AppColors.textPrimary
                    : AppColors.backgroundColorSecondary,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
              bottomLeft: Radius.circular(isUserMessage ? 16.r : 8.r),
              bottomRight: Radius.circular(isUserMessage ? 8.r : 16.r),
            ),
          ),
          child: Text(
            message.message,
            style: TextStyle(
              fontSize: 16.sp,
              color: isUserMessage ? Colors.white : AppColors.textDefaultDark,
            ),
          ),
        );

      case MessageType.image:
        return Container(
          constraints: BoxConstraints(maxWidth: maxWidth),
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.r)),
          child:
              message.imageFile != null
                  ? Image.file(
                    message.imageFile!,
                    width: maxWidth,
                    fit: BoxFit.cover,
                  )
                  : Container(
                    width: 200.w,
                    height: 150.h,
                    color: AppColors.backgroundColorSecondary,
                    child: Icon(
                      Icons.image_not_supported,
                      size: 40.w,
                      color: AppColors.textSubdude,
                    ),
                  ),
        );

      case MessageType.file:
        return Container(
          constraints: BoxConstraints(maxWidth: maxWidth),
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(color: AppColors.borderDefault),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: AppColors.textPrimary,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(
                  Icons.insert_drive_file_outlined,
                  color: Colors.white,
                  size: 20.w,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.fileName ?? 'File',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textDefaultDark,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      'PDF',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textSubdude,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
    }
  }

  Widget _buildTimestamp(bool isUserMessage) {
    return Padding(
      padding:
          isUserMessage
              ? EdgeInsets.only(right: 8.w)
              : EdgeInsets.only(left: 8.w),
      child: Text(
        message.formattedTime,
        style: TextStyle(fontSize: 12.sp, color: AppColors.textSubdude),
      ),
    );
  }
}
