import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class ChatDateDivider extends StatelessWidget {
  final String date;

  const ChatDateDivider({super.key, required this.date});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: AppColors.backgroundColorSecondary,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        date,
        style: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textSubdude,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
