import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import '../../domain/entities/notification.dart' as notification_entity;

/// A widget that displays a single notification item
class NotificationItem extends StatelessWidget {
  final notification_entity.Notification notification;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelectionMode;
  final bool isSelected;
  final ValueChanged<bool>? onSelectionChanged;

  const NotificationItem({
    super.key,
    required this.notification,
    this.onTap,
    this.onLongPress,
    this.isSelectionMode = false,
    this.isSelected = false,
    this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: isSelectionMode ? _handleSelectionTap : onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(8.r),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Row(
              children: [
                if (isSelectionMode) ...[
                  _buildCheckbox(),
                  16.w.horizontalSpace,
                ],
                Expanded(
                  child: Column(
                    children: [
                      _buildNotificationHeader(),
                      8.h.verticalSpace,
                      _buildNotificationContent(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        _buildDivider(),
      ],
    );
  }

  void _handleSelectionTap() {
    onSelectionChanged?.call(!isSelected);
  }

  Widget _buildCheckbox() {
    return GestureDetector(
      onTap: _handleSelectionTap,
      child: Container(
        width: 20.w,
        height: 20.h,
        decoration: BoxDecoration(
          color:
              isSelected ? AppColors.buttonPrimary : AppColors.surfaceDefault,
          border: Border.all(
            color:
                isSelected ? AppColors.buttonPrimary : AppColors.borderDefault,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child:
            isSelected
                ? Icon(
                  Icons.check,
                  size: 12.w,
                  color: AppColors.textDefaultWhite,
                )
                : null,
      ),
    );
  }

  Widget _buildNotificationHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [_buildCategoryBadge(), _buildTimeText()],
    );
  }

  Widget _buildCategoryBadge() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        notification.category,
        style: TextStyle(
          fontFamily: AppFonts.notoSansThai,
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.textDefaultDark,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildTimeText() {
    return Text(
      notification.time,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textSubdude,
        height: 1.5,
      ),
    );
  }

  Widget _buildNotificationContent() {
    return Row(
      children: [
        Expanded(
          child: Text(
            notification.title,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textDefaultDark,
              height: 1.5,
            ),
          ),
        ),
        8.w.horizontalSpace,
        _buildArrowIcon(),
      ],
    );
  }

  Widget _buildArrowIcon() {
    return SvgPicture.asset(
      'assets/icons/arrow_right.svg',
      width: 16.w,
      height: 16.h,
      colorFilter: const ColorFilter.mode(
        AppColors.textDefaultDark,
        BlendMode.srcIn,
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      color: AppColors.borderDefault,
    );
  }
}
