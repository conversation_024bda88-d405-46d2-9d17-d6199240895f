import '../../domain/entities/notification.dart';

/// Notification data model for data layer operations
class NotificationModel extends Notification {
  const NotificationModel({
    required super.id,
    required super.title,
    required super.category,
    required super.time,
    required super.date,
    super.isRead,
  });

  /// Creates a [NotificationModel] from JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      category: json['category'] as String,
      time: json['time'] as String,
      date: DateTime.parse(json['date'] as String),
      isRead: json['isRead'] as bool? ?? false,
    );
  }

  /// Converts [NotificationModel] to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'category': category,
      'time': time,
      'date': date.toIso8601String(),
      'isRead': isRead,
    };
  }

  /// Creates a [NotificationModel] from [Notification] entity
  factory NotificationModel.fromEntity(Notification notification) {
    return NotificationModel(
      id: notification.id,
      title: notification.title,
      category: notification.category,
      time: notification.time,
      date: notification.date,
      isRead: notification.isRead,
    );
  }

  /// Converts [NotificationModel] to [Notification] entity
  Notification toEntity() {
    return Notification(
      id: id,
      title: title,
      category: category,
      time: time,
      date: date,
      isRead: isRead,
    );
  }

  @override
  NotificationModel copyWith({
    String? id,
    String? title,
    String? category,
    String? time,
    DateTime? date,
    bool? isRead,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      category: category ?? this.category,
      time: time ?? this.time,
      date: date ?? this.date,
      isRead: isRead ?? this.isRead,
    );
  }
}
