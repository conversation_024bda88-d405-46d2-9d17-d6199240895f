import 'package:equatable/equatable.dart';

/// Notification entity representing a notification item
class Notification extends Equatable {
  final String id;
  final String title;
  final String category;
  final String time;
  final DateTime date;
  final bool isRead;

  const Notification({
    required this.id,
    required this.title,
    required this.category,
    required this.time,
    required this.date,
    this.isRead = false,
  });

  @override
  List<Object?> get props => [id, title, category, time, date, isRead];

  Notification copyWith({
    String? id,
    String? title,
    String? category,
    String? time,
    DateTime? date,
    bool? isRead,
  }) {
    return Notification(
      id: id ?? this.id,
      title: title ?? this.title,
      category: category ?? this.category,
      time: time ?? this.time,
      date: date ?? this.date,
      isRead: isRead ?? this.isRead,
    );
  }
}
