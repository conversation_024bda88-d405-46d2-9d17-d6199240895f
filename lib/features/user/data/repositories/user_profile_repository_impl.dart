import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import '../../domain/entities/user_profile.dart';
import '../../domain/repositories/user_profile_repository.dart';
import '../datasources/user_profile_local_datasource.dart';

/// Implementation of [UserProfileRepository]
class UserProfileRepositoryImpl implements UserProfileRepository {
  final UserProfileLocalDataSource localDataSource;

  const UserProfileRepositoryImpl({required this.localDataSource});

  @override
  Future<Either<Failure, UserProfile>> getUserProfile() async {
    try {
      final profile = await localDataSource.getUserProfile();
      return Right(profile.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserProfile>> updateUserProfile({
    required String firstName,
    required String lastName,
    required String idCardNumber,
    required String email,
    required String phoneNumber,
    required String sectorType,
    required String organizationName,
    String? websiteName,
    required String currentPassword,
  }) async {
    try {
      final updatedProfile = await localDataSource.updateUserProfile(
        firstName: firstName,
        lastName: lastName,
        idCardNumber: idCardNumber,
        email: email,
        phoneNumber: phoneNumber,
        sectorType: sectorType,
        organizationName: organizationName,
        websiteName: websiteName,
        currentPassword: currentPassword,
      );
      return Right(updatedProfile.toEntity());
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      await localDataSource.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount({
    required String password,
  }) async {
    try {
      await localDataSource.deleteAccount(password: password);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
