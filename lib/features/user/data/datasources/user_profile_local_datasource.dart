import '../models/user_profile_model.dart';

/// Local data source for user profile operations
abstract class UserProfileLocalDataSource {
  /// Get user profile from local storage
  Future<UserProfileModel> getUserProfile();

  /// Update user profile in local storage
  Future<UserProfileModel> updateUserProfile({
    required String firstName,
    required String lastName,
    required String idCardNumber,
    required String email,
    required String phoneNumber,
    required String sectorType,
    required String organizationName,
    String? websiteName,
    required String currentPassword,
  });

  /// Change user password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Delete user account
  Future<void> deleteAccount({required String password});
}

/// Implementation of [UserProfileLocalDataSource] with mock data
class UserProfileLocalDataSourceImpl implements UserProfileLocalDataSource {
  @override
  Future<UserProfileModel> getUserProfile() async {
    // Mock data - in real app this would come from API or local storage
    await Future.delayed(const Duration(milliseconds: 500));

    return const UserProfileModel(
      username: '<PERSON><PERSON><PERSON>',
      firstName: 'Pattamon',
      lastName: 'Wongwarang',
      idCardNumber: '*************',
      email: '<EMAIL>',
      phoneNumber: '**********',
      sectorType: 'ภาคเอกชน',
      organizationName: 'วีวาสนาดี',
      websiteName: null,
    );
  }

  @override
  Future<UserProfileModel> updateUserProfile({
    required String firstName,
    required String lastName,
    required String idCardNumber,
    required String email,
    required String phoneNumber,
    required String sectorType,
    required String organizationName,
    String? websiteName,
    required String currentPassword,
  }) async {
    // Mock update - in real app this would send to API
    await Future.delayed(const Duration(milliseconds: 1000));

    // Simulate password validation
    if (currentPassword.isEmpty) {
      throw Exception('Current password is required');
    }

    // Return updated profile
    return UserProfileModel(
      username: 'Pattamon', // Username cannot be changed
      firstName: firstName,
      lastName: lastName,
      idCardNumber: idCardNumber,
      email: email,
      phoneNumber: phoneNumber,
      sectorType: sectorType,
      organizationName: organizationName,
      websiteName: websiteName,
    );
  }

  @override
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    // Mock password change - in real app this would send to API
    await Future.delayed(const Duration(milliseconds: 1000));

    // Simulate password validation
    if (currentPassword.isEmpty) {
      throw Exception('Current password is required');
    }

    if (newPassword.isEmpty) {
      throw Exception('New password is required');
    }

    if (newPassword.length < 6) {
      throw Exception('New password must be at least 6 characters');
    }

    // Simulate current password verification
    // In real app, this would verify against stored password
    if (currentPassword != 'password123') {
      throw Exception('Current password is incorrect');
    }

    // Password change successful - in real app would update in backend
  }

  @override
  Future<void> deleteAccount({required String password}) async {
    // Mock account deletion - in real app this would send to API
    await Future.delayed(const Duration(milliseconds: 1000));

    // Simulate password validation
    if (password.isEmpty) {
      throw Exception('Password is required');
    }

    // Simulate password verification
    // In real app, this would verify against stored password
    if (password != 'password123') {
      throw Exception('Password is incorrect');
    }

    // Account deletion successful - in real app would delete from backend
    // and clear local storage
  }
}
