import 'dart:convert';
import 'package:mcdc/features/user/domain/entities/login_data.dart';
import 'package:mcdc/features/user/domain/entities/user.dart';
import 'package:mcdc/features/user/domain/entities/token.dart';
import 'package:mcdc/features/user/domain/entities/session_info.dart';
import 'package:mcdc/core/enums/user_type.dart';
import '../../../../core/storage/local_storage.dart';

/// Data source for managing authentication-related storage operations
abstract class AuthStorageDataSource {
  /// Save complete login data
  Future<void> saveLoginData(LoginData loginData);

  /// Get stored access token
  Future<String?> getAccessToken();

  /// Get stored refresh token
  Future<String?> getRefreshToken();

  /// Get stored user data
  Future<User?> getUser();

  /// Get stored session info
  Future<SessionInfo?> getSessionInfo();

  /// Get complete login data
  Future<LoginData?> getLoginData();

  /// Check if user is logged in (has valid tokens)
  Future<bool> isLoggedIn();

  /// Clear all authentication data (logout)
  Future<void> clearAuthData();

  /// Update access token (for token refresh)
  Future<void> updateAccessToken(String accessToken);

  /// Update refresh token
  Future<void> updateRefreshToken(String refreshToken);
}

/// Implementation of [AuthStorageDataSource]
class AuthStorageDataSourceImpl implements AuthStorageDataSource {
  final LocalStorage _secureStorage;

  // Storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';
  static const String _sessionInfoKey = 'session_info';

  const AuthStorageDataSourceImpl(this._secureStorage);

  @override
  Future<void> saveLoginData(LoginData loginData) async {
    // Save tokens
    await _secureStorage.store(_accessTokenKey, loginData.tokens.accessToken);
    await _secureStorage.store(_refreshTokenKey, loginData.tokens.refreshToken);

    // Save user data as JSON
    final userJson = jsonEncode(_userToMap(loginData.user));
    await _secureStorage.store(_userDataKey, userJson);

    // Save session info as JSON
    if (loginData.sessionInfo != null) {
      final sessionJson = jsonEncode(_sessionInfoToMap(loginData.sessionInfo!));
      await _secureStorage.store(_sessionInfoKey, sessionJson);
    }
  }

  @override
  Future<String?> getAccessToken() async {
    return await _secureStorage.retrieve(_accessTokenKey);
  }

  @override
  Future<String?> getRefreshToken() async {
    return await _secureStorage.retrieve(_refreshTokenKey);
  }

  @override
  Future<User?> getUser() async {
    final userJson = await _secureStorage.retrieve(_userDataKey);
    if (userJson == null) return null;

    try {
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return _userFromMap(userMap);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<SessionInfo?> getSessionInfo() async {
    final sessionJson = await _secureStorage.retrieve(_sessionInfoKey);
    if (sessionJson == null) return null;

    try {
      final sessionMap = jsonDecode(sessionJson) as Map<String, dynamic>;
      return _sessionInfoFromMap(sessionMap);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<LoginData?> getLoginData() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    final user = await getUser();
    final sessionInfo = await getSessionInfo();

    if (accessToken == null || refreshToken == null || user == null) {
      return null;
    }

    return LoginData(
      user: user,
      tokens: Token(accessToken: accessToken, refreshToken: refreshToken),
      sessionInfo: sessionInfo,
    );
  }

  @override
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    return accessToken != null && accessToken.isNotEmpty;
  }

  @override
  Future<void> clearAuthData() async {
    await Future.wait([
      _secureStorage.delete(_accessTokenKey),
      _secureStorage.delete(_refreshTokenKey),
      _secureStorage.delete(_userDataKey),
      _secureStorage.delete(_sessionInfoKey),
    ]);
  }

  @override
  Future<void> updateAccessToken(String accessToken) async {
    await _secureStorage.store(_accessTokenKey, accessToken);
  }

  @override
  Future<void> updateRefreshToken(String refreshToken) async {
    await _secureStorage.store(_refreshTokenKey, refreshToken);
  }

  // Helper methods for User serialization
  Map<String, dynamic> _userToMap(User user) {
    return {
      'id': user.id,
      'name': user.name,
      'firstName': user.firstName,
      'lastName': user.lastName,
      'email': user.email,
      'emailSecond': user.emailSecond,
      'phone': user.phone,
      'phoneSecond': user.phoneSecond,
      'identityCardNo': user.identityCardNo,
      'username': user.username,
      'status': user.status,
      'statusDisplay': user.statusDisplay,
      // Member-specific fields
      'appMasMemberTypeId': user.appMasMemberTypeId,
      'appMasMemberTypeDisplay': user.appMasMemberTypeDisplay,
      'appMasGovernmentSectorId': user.appMasGovernmentSectorId,
      'appMasGovernmentSectorDisplay': user.appMasGovernmentSectorDisplay,
      'appMasMinistryId': user.appMasMinistryId,
      'appMasMinistryDisplay': user.appMasMinistryDisplay,
      'appMasDepartmentId': user.appMasDepartmentId,
      'appMasDepartmentDisplay': user.appMasDepartmentDisplay,
      'website': user.website,
      'createDate': user.createDate?.toIso8601String(),
      // Consultant-specific fields
      'consultType': user.consultType,
      'consultTypeDisplay': user.consultTypeDisplay,
      'corporateTypeId': user.corporateTypeId,
      'corporateTypeDisplay': user.corporateTypeDisplay,
      'makerName': user.makerName,
      'makerPhone': user.makerPhone,
      'makerEmail': user.makerEmail,
      'verify': user.verify,
      'verifyDisplay': user.verifyDisplay,
      'score': user.score,
      'isActiveMatching': user.isActiveMatching,
      // Common fields
      'isNotification': user.isNotification,
      'lang': user.lang,
      'userType': user.userType.value,
    };
  }

  User _userFromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] as int,
      name: map['name'] as String?,
      firstName: map['firstName'] as String?,
      lastName: map['lastName'] as String?,
      email: map['email'] as String,
      emailSecond: map['emailSecond'] as String?,
      phone: map['phone'] as String,
      phoneSecond: map['phoneSecond'] as String?,
      identityCardNo: map['identityCardNo'] as String?,
      username: map['username'] as String,
      // Member-specific fields
      appMasMemberTypeId: map['appMasMemberTypeId'] as int?,
      appMasMemberTypeDisplay: map['appMasMemberTypeDisplay'] as String?,
      appMasGovernmentSectorId: map['appMasGovernmentSectorId'] as int?,
      appMasGovernmentSectorDisplay:
          map['appMasGovernmentSectorDisplay'] as String?,
      appMasMinistryId: map['appMasMinistryId'] as int?,
      appMasMinistryDisplay: map['appMasMinistryDisplay'] as String?,
      appMasDepartmentId: map['appMasDepartmentId'] as int?,
      appMasDepartmentDisplay: map['appMasDepartmentDisplay'] as String?,
      website: map['website'] as String?,
      createDate:
          map['createDate'] != null
              ? DateTime.parse(map['createDate'] as String)
              : null,
      status: map['status'] as String?,
      statusDisplay: map['statusDisplay'] as String?,
      // Consultant-specific fields
      consultType: map['consultType'] as int?,
      consultTypeDisplay: map['consultTypeDisplay'] as String?,
      corporateTypeId: map['corporateTypeId'] as int?,
      corporateTypeDisplay: map['corporateTypeDisplay'] as String?,
      makerName: map['makerName'] as String?,
      makerPhone: map['makerPhone'] as String?,
      makerEmail: map['makerEmail'] as String?,
      verify: map['verify'] as String?,
      verifyDisplay: map['verifyDisplay'] as String?,
      score: map['score'] as double?,
      isActiveMatching: map['isActiveMatching'] as bool?,
      // Common fields
      isNotification: map['isNotification'] as String,
      lang: map['lang'] as String,
      userType: UserType.fromString(map['userType'] as String),
    );
  }

  // Helper methods for SessionInfo serialization
  Map<String, dynamic> _sessionInfoToMap(SessionInfo sessionInfo) {
    return {
      'loginTime': sessionInfo.loginTime.toIso8601String(),
      'userType': sessionInfo.userType.value,
    };
  }

  SessionInfo _sessionInfoFromMap(Map<String, dynamic> map) {
    return SessionInfo(
      loginTime: DateTime.parse(map['loginTime'] as String),
      userType: UserType.fromString(map['userType'] as String),
    );
  }
}
