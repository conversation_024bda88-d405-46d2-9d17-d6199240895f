// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'forgot_password_verify_otp_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ForgotPasswordVerifyOtpResponseModel {

 bool get verified;@JsonKey(name: 'verified_token') String get verifiedToken; String get message;
/// Create a copy of ForgotPasswordVerifyOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ForgotPasswordVerifyOtpResponseModelCopyWith<ForgotPasswordVerifyOtpResponseModel> get copyWith => _$ForgotPasswordVerifyOtpResponseModelCopyWithImpl<ForgotPasswordVerifyOtpResponseModel>(this as ForgotPasswordVerifyOtpResponseModel, _$identity);

  /// Serializes this ForgotPasswordVerifyOtpResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ForgotPasswordVerifyOtpResponseModel&&(identical(other.verified, verified) || other.verified == verified)&&(identical(other.verifiedToken, verifiedToken) || other.verifiedToken == verifiedToken)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,verified,verifiedToken,message);

@override
String toString() {
  return 'ForgotPasswordVerifyOtpResponseModel(verified: $verified, verifiedToken: $verifiedToken, message: $message)';
}


}

/// @nodoc
abstract mixin class $ForgotPasswordVerifyOtpResponseModelCopyWith<$Res>  {
  factory $ForgotPasswordVerifyOtpResponseModelCopyWith(ForgotPasswordVerifyOtpResponseModel value, $Res Function(ForgotPasswordVerifyOtpResponseModel) _then) = _$ForgotPasswordVerifyOtpResponseModelCopyWithImpl;
@useResult
$Res call({
 bool verified,@JsonKey(name: 'verified_token') String verifiedToken, String message
});




}
/// @nodoc
class _$ForgotPasswordVerifyOtpResponseModelCopyWithImpl<$Res>
    implements $ForgotPasswordVerifyOtpResponseModelCopyWith<$Res> {
  _$ForgotPasswordVerifyOtpResponseModelCopyWithImpl(this._self, this._then);

  final ForgotPasswordVerifyOtpResponseModel _self;
  final $Res Function(ForgotPasswordVerifyOtpResponseModel) _then;

/// Create a copy of ForgotPasswordVerifyOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? verified = null,Object? verifiedToken = null,Object? message = null,}) {
  return _then(_self.copyWith(
verified: null == verified ? _self.verified : verified // ignore: cast_nullable_to_non_nullable
as bool,verifiedToken: null == verifiedToken ? _self.verifiedToken : verifiedToken // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ForgotPasswordVerifyOtpResponseModel implements ForgotPasswordVerifyOtpResponseModel {
  const _ForgotPasswordVerifyOtpResponseModel({required this.verified, @JsonKey(name: 'verified_token') required this.verifiedToken, required this.message});
  factory _ForgotPasswordVerifyOtpResponseModel.fromJson(Map<String, dynamic> json) => _$ForgotPasswordVerifyOtpResponseModelFromJson(json);

@override final  bool verified;
@override@JsonKey(name: 'verified_token') final  String verifiedToken;
@override final  String message;

/// Create a copy of ForgotPasswordVerifyOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ForgotPasswordVerifyOtpResponseModelCopyWith<_ForgotPasswordVerifyOtpResponseModel> get copyWith => __$ForgotPasswordVerifyOtpResponseModelCopyWithImpl<_ForgotPasswordVerifyOtpResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ForgotPasswordVerifyOtpResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ForgotPasswordVerifyOtpResponseModel&&(identical(other.verified, verified) || other.verified == verified)&&(identical(other.verifiedToken, verifiedToken) || other.verifiedToken == verifiedToken)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,verified,verifiedToken,message);

@override
String toString() {
  return 'ForgotPasswordVerifyOtpResponseModel(verified: $verified, verifiedToken: $verifiedToken, message: $message)';
}


}

/// @nodoc
abstract mixin class _$ForgotPasswordVerifyOtpResponseModelCopyWith<$Res> implements $ForgotPasswordVerifyOtpResponseModelCopyWith<$Res> {
  factory _$ForgotPasswordVerifyOtpResponseModelCopyWith(_ForgotPasswordVerifyOtpResponseModel value, $Res Function(_ForgotPasswordVerifyOtpResponseModel) _then) = __$ForgotPasswordVerifyOtpResponseModelCopyWithImpl;
@override @useResult
$Res call({
 bool verified,@JsonKey(name: 'verified_token') String verifiedToken, String message
});




}
/// @nodoc
class __$ForgotPasswordVerifyOtpResponseModelCopyWithImpl<$Res>
    implements _$ForgotPasswordVerifyOtpResponseModelCopyWith<$Res> {
  __$ForgotPasswordVerifyOtpResponseModelCopyWithImpl(this._self, this._then);

  final _ForgotPasswordVerifyOtpResponseModel _self;
  final $Res Function(_ForgotPasswordVerifyOtpResponseModel) _then;

/// Create a copy of ForgotPasswordVerifyOtpResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? verified = null,Object? verifiedToken = null,Object? message = null,}) {
  return _then(_ForgotPasswordVerifyOtpResponseModel(
verified: null == verified ? _self.verified : verified // ignore: cast_nullable_to_non_nullable
as bool,verifiedToken: null == verifiedToken ? _self.verifiedToken : verifiedToken // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
