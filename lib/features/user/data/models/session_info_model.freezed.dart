// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SessionInfoModel {

@JsonKey(name: 'login_time') DateTime get loginTime;@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType get userType;
/// Create a copy of SessionInfoModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SessionInfoModelCopyWith<SessionInfoModel> get copyWith => _$SessionInfoModelCopyWithImpl<SessionInfoModel>(this as SessionInfoModel, _$identity);

  /// Serializes this SessionInfoModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SessionInfoModel&&(identical(other.loginTime, loginTime) || other.loginTime == loginTime)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,loginTime,userType);

@override
String toString() {
  return 'SessionInfoModel(loginTime: $loginTime, userType: $userType)';
}


}

/// @nodoc
abstract mixin class $SessionInfoModelCopyWith<$Res>  {
  factory $SessionInfoModelCopyWith(SessionInfoModel value, $Res Function(SessionInfoModel) _then) = _$SessionInfoModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'login_time') DateTime loginTime,@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType userType
});




}
/// @nodoc
class _$SessionInfoModelCopyWithImpl<$Res>
    implements $SessionInfoModelCopyWith<$Res> {
  _$SessionInfoModelCopyWithImpl(this._self, this._then);

  final SessionInfoModel _self;
  final $Res Function(SessionInfoModel) _then;

/// Create a copy of SessionInfoModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? loginTime = null,Object? userType = null,}) {
  return _then(_self.copyWith(
loginTime: null == loginTime ? _self.loginTime : loginTime // ignore: cast_nullable_to_non_nullable
as DateTime,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as UserType,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SessionInfoModel implements SessionInfoModel {
  const _SessionInfoModel({@JsonKey(name: 'login_time') required this.loginTime, @JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) required this.userType});
  factory _SessionInfoModel.fromJson(Map<String, dynamic> json) => _$SessionInfoModelFromJson(json);

@override@JsonKey(name: 'login_time') final  DateTime loginTime;
@override@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) final  UserType userType;

/// Create a copy of SessionInfoModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SessionInfoModelCopyWith<_SessionInfoModel> get copyWith => __$SessionInfoModelCopyWithImpl<_SessionInfoModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SessionInfoModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SessionInfoModel&&(identical(other.loginTime, loginTime) || other.loginTime == loginTime)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,loginTime,userType);

@override
String toString() {
  return 'SessionInfoModel(loginTime: $loginTime, userType: $userType)';
}


}

/// @nodoc
abstract mixin class _$SessionInfoModelCopyWith<$Res> implements $SessionInfoModelCopyWith<$Res> {
  factory _$SessionInfoModelCopyWith(_SessionInfoModel value, $Res Function(_SessionInfoModel) _then) = __$SessionInfoModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'login_time') DateTime loginTime,@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType userType
});




}
/// @nodoc
class __$SessionInfoModelCopyWithImpl<$Res>
    implements _$SessionInfoModelCopyWith<$Res> {
  __$SessionInfoModelCopyWithImpl(this._self, this._then);

  final _SessionInfoModel _self;
  final $Res Function(_SessionInfoModel) _then;

/// Create a copy of SessionInfoModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? loginTime = null,Object? userType = null,}) {
  return _then(_SessionInfoModel(
loginTime: null == loginTime ? _self.loginTime : loginTime // ignore: cast_nullable_to_non_nullable
as DateTime,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as UserType,
  ));
}


}

// dart format on
