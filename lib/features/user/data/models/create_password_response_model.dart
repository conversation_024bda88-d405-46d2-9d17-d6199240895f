import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/create_password_response.dart';

part 'create_password_response_model.freezed.dart';
part 'create_password_response_model.g.dart';

@freezed
abstract class CreatePasswordResponseModel with _$CreatePasswordResponseModel {
  const factory CreatePasswordResponseModel({
    String? message,
    @JsonKey(name: 'user_type') String? userType,
  }) = _CreatePasswordResponseModel;

  factory CreatePasswordResponseModel.fromJson(Map<String, dynamic> json) =>
      _$CreatePasswordResponseModelFromJson(json);
}

extension CreatePasswordResponseModelX on CreatePasswordResponseModel {
  CreatePasswordResponse toEntity() {
    return CreatePasswordResponse(message: message, userType: userType);
  }
}
