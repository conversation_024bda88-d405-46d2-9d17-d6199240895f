// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'member_register_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MemberRegisterResponseModel {

 bool get status;@JsonKey(name: 'error_message') String? get errorMessage;@JsonKey(name: 'error_code') String? get errorCode;@JsonKey(name: 'api_version') String get apiVersion;@JsonKey(name: 'validation_errors') Map<String, List<String>>? get validationErrors; LoginDataModel? get loginData;// Contains user data and tokens on success
 String? get message;
/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemberRegisterResponseModelCopyWith<MemberRegisterResponseModel> get copyWith => _$MemberRegisterResponseModelCopyWithImpl<MemberRegisterResponseModel>(this as MemberRegisterResponseModel, _$identity);

  /// Serializes this MemberRegisterResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterResponseModel&&(identical(other.status, status) || other.status == status)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&(identical(other.apiVersion, apiVersion) || other.apiVersion == apiVersion)&&const DeepCollectionEquality().equals(other.validationErrors, validationErrors)&&(identical(other.loginData, loginData) || other.loginData == loginData)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,errorMessage,errorCode,apiVersion,const DeepCollectionEquality().hash(validationErrors),loginData,message);

@override
String toString() {
  return 'MemberRegisterResponseModel(status: $status, errorMessage: $errorMessage, errorCode: $errorCode, apiVersion: $apiVersion, validationErrors: $validationErrors, loginData: $loginData, message: $message)';
}


}

/// @nodoc
abstract mixin class $MemberRegisterResponseModelCopyWith<$Res>  {
  factory $MemberRegisterResponseModelCopyWith(MemberRegisterResponseModel value, $Res Function(MemberRegisterResponseModel) _then) = _$MemberRegisterResponseModelCopyWithImpl;
@useResult
$Res call({
 bool status,@JsonKey(name: 'error_message') String? errorMessage,@JsonKey(name: 'error_code') String? errorCode,@JsonKey(name: 'api_version') String apiVersion,@JsonKey(name: 'validation_errors') Map<String, List<String>>? validationErrors, LoginDataModel? loginData, String? message
});


$LoginDataModelCopyWith<$Res>? get loginData;

}
/// @nodoc
class _$MemberRegisterResponseModelCopyWithImpl<$Res>
    implements $MemberRegisterResponseModelCopyWith<$Res> {
  _$MemberRegisterResponseModelCopyWithImpl(this._self, this._then);

  final MemberRegisterResponseModel _self;
  final $Res Function(MemberRegisterResponseModel) _then;

/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? errorMessage = freezed,Object? errorCode = freezed,Object? apiVersion = null,Object? validationErrors = freezed,Object? loginData = freezed,Object? message = freezed,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,apiVersion: null == apiVersion ? _self.apiVersion : apiVersion // ignore: cast_nullable_to_non_nullable
as String,validationErrors: freezed == validationErrors ? _self.validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>?,loginData: freezed == loginData ? _self.loginData : loginData // ignore: cast_nullable_to_non_nullable
as LoginDataModel?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LoginDataModelCopyWith<$Res>? get loginData {
    if (_self.loginData == null) {
    return null;
  }

  return $LoginDataModelCopyWith<$Res>(_self.loginData!, (value) {
    return _then(_self.copyWith(loginData: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _MemberRegisterResponseModel implements MemberRegisterResponseModel {
  const _MemberRegisterResponseModel({required this.status, @JsonKey(name: 'error_message') this.errorMessage, @JsonKey(name: 'error_code') this.errorCode, @JsonKey(name: 'api_version') required this.apiVersion, @JsonKey(name: 'validation_errors') final  Map<String, List<String>>? validationErrors, this.loginData, this.message}): _validationErrors = validationErrors;
  factory _MemberRegisterResponseModel.fromJson(Map<String, dynamic> json) => _$MemberRegisterResponseModelFromJson(json);

@override final  bool status;
@override@JsonKey(name: 'error_message') final  String? errorMessage;
@override@JsonKey(name: 'error_code') final  String? errorCode;
@override@JsonKey(name: 'api_version') final  String apiVersion;
 final  Map<String, List<String>>? _validationErrors;
@override@JsonKey(name: 'validation_errors') Map<String, List<String>>? get validationErrors {
  final value = _validationErrors;
  if (value == null) return null;
  if (_validationErrors is EqualUnmodifiableMapView) return _validationErrors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  LoginDataModel? loginData;
// Contains user data and tokens on success
@override final  String? message;

/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemberRegisterResponseModelCopyWith<_MemberRegisterResponseModel> get copyWith => __$MemberRegisterResponseModelCopyWithImpl<_MemberRegisterResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MemberRegisterResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemberRegisterResponseModel&&(identical(other.status, status) || other.status == status)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&(identical(other.apiVersion, apiVersion) || other.apiVersion == apiVersion)&&const DeepCollectionEquality().equals(other._validationErrors, _validationErrors)&&(identical(other.loginData, loginData) || other.loginData == loginData)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,errorMessage,errorCode,apiVersion,const DeepCollectionEquality().hash(_validationErrors),loginData,message);

@override
String toString() {
  return 'MemberRegisterResponseModel(status: $status, errorMessage: $errorMessage, errorCode: $errorCode, apiVersion: $apiVersion, validationErrors: $validationErrors, loginData: $loginData, message: $message)';
}


}

/// @nodoc
abstract mixin class _$MemberRegisterResponseModelCopyWith<$Res> implements $MemberRegisterResponseModelCopyWith<$Res> {
  factory _$MemberRegisterResponseModelCopyWith(_MemberRegisterResponseModel value, $Res Function(_MemberRegisterResponseModel) _then) = __$MemberRegisterResponseModelCopyWithImpl;
@override @useResult
$Res call({
 bool status,@JsonKey(name: 'error_message') String? errorMessage,@JsonKey(name: 'error_code') String? errorCode,@JsonKey(name: 'api_version') String apiVersion,@JsonKey(name: 'validation_errors') Map<String, List<String>>? validationErrors, LoginDataModel? loginData, String? message
});


@override $LoginDataModelCopyWith<$Res>? get loginData;

}
/// @nodoc
class __$MemberRegisterResponseModelCopyWithImpl<$Res>
    implements _$MemberRegisterResponseModelCopyWith<$Res> {
  __$MemberRegisterResponseModelCopyWithImpl(this._self, this._then);

  final _MemberRegisterResponseModel _self;
  final $Res Function(_MemberRegisterResponseModel) _then;

/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? errorMessage = freezed,Object? errorCode = freezed,Object? apiVersion = null,Object? validationErrors = freezed,Object? loginData = freezed,Object? message = freezed,}) {
  return _then(_MemberRegisterResponseModel(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,apiVersion: null == apiVersion ? _self.apiVersion : apiVersion // ignore: cast_nullable_to_non_nullable
as String,validationErrors: freezed == validationErrors ? _self._validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>?,loginData: freezed == loginData ? _self.loginData : loginData // ignore: cast_nullable_to_non_nullable
as LoginDataModel?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of MemberRegisterResponseModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LoginDataModelCopyWith<$Res>? get loginData {
    if (_self.loginData == null) {
    return null;
  }

  return $LoginDataModelCopyWith<$Res>(_self.loginData!, (value) {
    return _then(_self.copyWith(loginData: value));
  });
}
}

// dart format on
