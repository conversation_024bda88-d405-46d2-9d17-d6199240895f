// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_id_card_result_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CheckIdCardResultModel {

@JsonKey(name: 'is_duplicate') bool get isDuplicate;@JsonKey(name: 'existing_member_id') String? get existingMemberId;
/// Create a copy of CheckIdCardResultModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckIdCardResultModelCopyWith<CheckIdCardResultModel> get copyWith => _$CheckIdCardResultModelCopyWithImpl<CheckIdCardResultModel>(this as CheckIdCardResultModel, _$identity);

  /// Serializes this CheckIdCardResultModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckIdCardResultModel&&(identical(other.isDuplicate, isDuplicate) || other.isDuplicate == isDuplicate)&&(identical(other.existingMemberId, existingMemberId) || other.existingMemberId == existingMemberId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isDuplicate,existingMemberId);

@override
String toString() {
  return 'CheckIdCardResultModel(isDuplicate: $isDuplicate, existingMemberId: $existingMemberId)';
}


}

/// @nodoc
abstract mixin class $CheckIdCardResultModelCopyWith<$Res>  {
  factory $CheckIdCardResultModelCopyWith(CheckIdCardResultModel value, $Res Function(CheckIdCardResultModel) _then) = _$CheckIdCardResultModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'is_duplicate') bool isDuplicate,@JsonKey(name: 'existing_member_id') String? existingMemberId
});




}
/// @nodoc
class _$CheckIdCardResultModelCopyWithImpl<$Res>
    implements $CheckIdCardResultModelCopyWith<$Res> {
  _$CheckIdCardResultModelCopyWithImpl(this._self, this._then);

  final CheckIdCardResultModel _self;
  final $Res Function(CheckIdCardResultModel) _then;

/// Create a copy of CheckIdCardResultModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isDuplicate = null,Object? existingMemberId = freezed,}) {
  return _then(_self.copyWith(
isDuplicate: null == isDuplicate ? _self.isDuplicate : isDuplicate // ignore: cast_nullable_to_non_nullable
as bool,existingMemberId: freezed == existingMemberId ? _self.existingMemberId : existingMemberId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CheckIdCardResultModel implements CheckIdCardResultModel {
  const _CheckIdCardResultModel({@JsonKey(name: 'is_duplicate') required this.isDuplicate, @JsonKey(name: 'existing_member_id') this.existingMemberId});
  factory _CheckIdCardResultModel.fromJson(Map<String, dynamic> json) => _$CheckIdCardResultModelFromJson(json);

@override@JsonKey(name: 'is_duplicate') final  bool isDuplicate;
@override@JsonKey(name: 'existing_member_id') final  String? existingMemberId;

/// Create a copy of CheckIdCardResultModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CheckIdCardResultModelCopyWith<_CheckIdCardResultModel> get copyWith => __$CheckIdCardResultModelCopyWithImpl<_CheckIdCardResultModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CheckIdCardResultModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CheckIdCardResultModel&&(identical(other.isDuplicate, isDuplicate) || other.isDuplicate == isDuplicate)&&(identical(other.existingMemberId, existingMemberId) || other.existingMemberId == existingMemberId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isDuplicate,existingMemberId);

@override
String toString() {
  return 'CheckIdCardResultModel(isDuplicate: $isDuplicate, existingMemberId: $existingMemberId)';
}


}

/// @nodoc
abstract mixin class _$CheckIdCardResultModelCopyWith<$Res> implements $CheckIdCardResultModelCopyWith<$Res> {
  factory _$CheckIdCardResultModelCopyWith(_CheckIdCardResultModel value, $Res Function(_CheckIdCardResultModel) _then) = __$CheckIdCardResultModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'is_duplicate') bool isDuplicate,@JsonKey(name: 'existing_member_id') String? existingMemberId
});




}
/// @nodoc
class __$CheckIdCardResultModelCopyWithImpl<$Res>
    implements _$CheckIdCardResultModelCopyWith<$Res> {
  __$CheckIdCardResultModelCopyWithImpl(this._self, this._then);

  final _CheckIdCardResultModel _self;
  final $Res Function(_CheckIdCardResultModel) _then;

/// Create a copy of CheckIdCardResultModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isDuplicate = null,Object? existingMemberId = freezed,}) {
  return _then(_CheckIdCardResultModel(
isDuplicate: null == isDuplicate ? _self.isDuplicate : isDuplicate // ignore: cast_nullable_to_non_nullable
as bool,existingMemberId: freezed == existingMemberId ? _self.existingMemberId : existingMemberId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
