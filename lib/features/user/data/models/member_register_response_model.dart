import 'dart:developer' as developer;
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/member_register_response.dart';
import 'login_data_model.dart';

part 'member_register_response_model.freezed.dart';
part 'member_register_response_model.g.dart';

@freezed
abstract class MemberRegisterResponseModel with _$MemberRegisterResponseModel {
  const factory MemberRegisterResponseModel({
    required bool status,
    @<PERSON>son<PERSON>ey(name: 'error_message') String? errorMessage,
    @JsonKey(name: 'error_code') String? errorCode,
    @JsonKey(name: 'api_version') required String apiVersion,
    @J<PERSON><PERSON>ey(name: 'validation_errors')
    Map<String, List<String>>? validationErrors,
    LoginDataModel? loginData, // Contains user data and tokens on success
    String? message, // Success message from API
  }) = _MemberRegisterResponseModel;

  factory MemberRegisterResponseModel.fromJson(Map<String, dynamic> json) =>
      _$MemberRegisterResponseModelFromJson(json);

  factory MemberRegisterResponseModel.fromApiResponse(
    Map<String, dynamic> json,
  ) {
    // Handle validation errors from nested data structure
    Map<String, List<String>>? validationErrors;
    LoginDataModel? loginData;
    String? message;

    if (json['data'] is Map<String, dynamic>) {
      final data = json['data'] as Map<String, dynamic>;

      // Handle validation errors (for error responses)
      if (data['validation_errors'] is Map<String, dynamic>) {
        final errors = data['validation_errors'] as Map<String, dynamic>;
        validationErrors = errors.map((key, value) {
          if (value is List) {
            return MapEntry(key, value.cast<String>());
          }
          return MapEntry(key, [value.toString()]);
        });
      }

      // Handle success response with user data and tokens
      if (json['status'] == true &&
          data['user'] != null &&
          data['tokens'] != null) {
        try {
          // Parse login data from the success response
          loginData = LoginDataModel.fromJson(data);
          message = data['message'] as String?;
        } catch (e) {
          // If parsing fails, treat as error
          developer.log(
            'Parse login data failed in MemberRegisterResponseModel: $e',
          );
          // loginData will remain null
        }
      }
    }

    return MemberRegisterResponseModel(
      status: json['status'] ?? false,
      errorMessage: json['error_message'],
      errorCode: json['error_code']?.toString(),
      apiVersion: json['api_version'] ?? '',
      validationErrors: validationErrors,
      loginData: loginData,
      message: message,
    );
  }
}

extension MemberRegisterResponseModelX on MemberRegisterResponseModel {
  MemberRegisterResponse toEntity() => MemberRegisterResponse(
    status: status,
    errorMessage: errorMessage,
    errorCode: errorCode,
    apiVersion: apiVersion,
    validationErrors: validationErrors,
    loginData: loginData?.toEntity(),
    message: message,
  );
}
