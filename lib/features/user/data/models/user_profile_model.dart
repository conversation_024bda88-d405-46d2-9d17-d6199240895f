import '../../domain/entities/user_profile.dart';

/// User profile data model for data layer operations
class UserProfileModel extends UserProfile {
  const UserProfileModel({
    required super.username,
    required super.firstName,
    required super.lastName,
    required super.idCardNumber,
    required super.email,
    required super.phoneNumber,
    required super.sectorType,
    required super.organizationName,
    super.websiteName,
  });

  /// Creates a [UserProfileModel] from JSON
  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    return UserProfileModel(
      username: json['username'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      idCardNumber: json['idCardNumber'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String,
      sectorType: json['sectorType'] as String,
      organizationName: json['organizationName'] as String,
      websiteName: json['websiteName'] as String?,
    );
  }

  /// Converts [UserProfileModel] to JSON
  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'firstName': firstName,
      'lastName': lastName,
      'idCardNumber': idCardNumber,
      'email': email,
      'phoneNumber': phoneNumber,
      'sectorType': sectorType,
      'organizationName': organizationName,
      'websiteName': websiteName,
    };
  }

  /// Creates a [UserProfileModel] from [UserProfile] entity
  factory UserProfileModel.fromEntity(UserProfile profile) {
    return UserProfileModel(
      username: profile.username,
      firstName: profile.firstName,
      lastName: profile.lastName,
      idCardNumber: profile.idCardNumber,
      email: profile.email,
      phoneNumber: profile.phoneNumber,
      sectorType: profile.sectorType,
      organizationName: profile.organizationName,
      websiteName: profile.websiteName,
    );
  }

  /// Converts [UserProfileModel] to [UserProfile] entity
  UserProfile toEntity() {
    return UserProfile(
      username: username,
      firstName: firstName,
      lastName: lastName,
      idCardNumber: idCardNumber,
      email: email,
      phoneNumber: phoneNumber,
      sectorType: sectorType,
      organizationName: organizationName,
      websiteName: websiteName,
    );
  }

  @override
  UserProfileModel copyWith({
    String? username,
    String? firstName,
    String? lastName,
    String? idCardNumber,
    String? email,
    String? phoneNumber,
    String? sectorType,
    String? organizationName,
    String? websiteName,
  }) {
    return UserProfileModel(
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      idCardNumber: idCardNumber ?? this.idCardNumber,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      sectorType: sectorType ?? this.sectorType,
      organizationName: organizationName ?? this.organizationName,
      websiteName: websiteName ?? this.websiteName,
    );
  }
}
