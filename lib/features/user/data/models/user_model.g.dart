// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserModel _$UserModelFromJson(Map<String, dynamic> json) => _UserModel(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String?,
  firstName: json['first_name'] as String?,
  lastName: json['last_name'] as String?,
  email: json['email'] as String,
  emailSecond: json['email_second'] as String?,
  phone: json['phone'] as String,
  phoneSecond: json['phone_second'] as String?,
  identityCardNo: json['identity_card_no'] as String?,
  username: json['username'] as String,
  appMasMemberTypeId: (json['app_mas_member_type_id'] as num?)?.toInt(),
  appMasMemberTypeDisplay: json['app_mas_member_type_display'] as String?,
  appMasGovernmentSectorId:
      (json['app_mas_government_sector_id'] as num?)?.toInt(),
  appMasGovernmentSectorDisplay:
      json['app_mas_government_sector_display'] as String?,
  appMasMinistryId: (json['app_mas_ministry_id'] as num?)?.toInt(),
  appMasMinistryDisplay: json['app_mas_ministry_display'] as String?,
  appMasDepartmentId: (json['app_mas_department_id'] as num?)?.toInt(),
  appMasDepartmentDisplay: json['app_mas_department_display'] as String?,
  website: json['website'] as String?,
  createDate:
      json['create_date'] == null
          ? null
          : DateTime.parse(json['create_date'] as String),
  status: json['status'] as String?,
  statusDisplay: json['status_display'] as String?,
  consultType: (json['consult_type'] as num?)?.toInt(),
  consultTypeDisplay: json['consult_type_display'] as String?,
  corporateTypeId: (json['corporate_type_id'] as num?)?.toInt(),
  corporateTypeDisplay: json['corporate_type_display'] as String?,
  makerName: json['maker_name'] as String?,
  makerPhone: json['maker_phone'] as String?,
  makerEmail: json['maker_email'] as String?,
  verify: json['verify'] as String?,
  verifyDisplay: json['verify_display'] as String?,
  score: (json['score'] as num?)?.toDouble(),
  isActiveMatching: json['is_active_matching'] as bool?,
  isNotification: json['is_notification'] as String,
  lang: json['lang'] as String,
  userType: UserType.fromJson(json['user_type'] as String),
);

Map<String, dynamic> _$UserModelToJson(
  _UserModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'email': instance.email,
  'email_second': instance.emailSecond,
  'phone': instance.phone,
  'phone_second': instance.phoneSecond,
  'identity_card_no': instance.identityCardNo,
  'username': instance.username,
  'app_mas_member_type_id': instance.appMasMemberTypeId,
  'app_mas_member_type_display': instance.appMasMemberTypeDisplay,
  'app_mas_government_sector_id': instance.appMasGovernmentSectorId,
  'app_mas_government_sector_display': instance.appMasGovernmentSectorDisplay,
  'app_mas_ministry_id': instance.appMasMinistryId,
  'app_mas_ministry_display': instance.appMasMinistryDisplay,
  'app_mas_department_id': instance.appMasDepartmentId,
  'app_mas_department_display': instance.appMasDepartmentDisplay,
  'website': instance.website,
  'create_date': instance.createDate?.toIso8601String(),
  'status': instance.status,
  'status_display': instance.statusDisplay,
  'consult_type': instance.consultType,
  'consult_type_display': instance.consultTypeDisplay,
  'corporate_type_id': instance.corporateTypeId,
  'corporate_type_display': instance.corporateTypeDisplay,
  'maker_name': instance.makerName,
  'maker_phone': instance.makerPhone,
  'maker_email': instance.makerEmail,
  'verify': instance.verify,
  'verify_display': instance.verifyDisplay,
  'score': instance.score,
  'is_active_matching': instance.isActiveMatching,
  'is_notification': instance.isNotification,
  'lang': instance.lang,
  'user_type': UserType.toJson(instance.userType),
};
