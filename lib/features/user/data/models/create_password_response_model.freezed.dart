// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_password_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreatePasswordResponseModel {

 String? get message;@JsonKey(name: 'user_type') String? get userType;
/// Create a copy of CreatePasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreatePasswordResponseModelCopyWith<CreatePasswordResponseModel> get copyWith => _$CreatePasswordResponseModelCopyWithImpl<CreatePasswordResponseModel>(this as CreatePasswordResponseModel, _$identity);

  /// Serializes this CreatePasswordResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordResponseModel&&(identical(other.message, message) || other.message == message)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,message,userType);

@override
String toString() {
  return 'CreatePasswordResponseModel(message: $message, userType: $userType)';
}


}

/// @nodoc
abstract mixin class $CreatePasswordResponseModelCopyWith<$Res>  {
  factory $CreatePasswordResponseModelCopyWith(CreatePasswordResponseModel value, $Res Function(CreatePasswordResponseModel) _then) = _$CreatePasswordResponseModelCopyWithImpl;
@useResult
$Res call({
 String? message,@JsonKey(name: 'user_type') String? userType
});




}
/// @nodoc
class _$CreatePasswordResponseModelCopyWithImpl<$Res>
    implements $CreatePasswordResponseModelCopyWith<$Res> {
  _$CreatePasswordResponseModelCopyWithImpl(this._self, this._then);

  final CreatePasswordResponseModel _self;
  final $Res Function(CreatePasswordResponseModel) _then;

/// Create a copy of CreatePasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? message = freezed,Object? userType = freezed,}) {
  return _then(_self.copyWith(
message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,userType: freezed == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CreatePasswordResponseModel implements CreatePasswordResponseModel {
  const _CreatePasswordResponseModel({this.message, @JsonKey(name: 'user_type') this.userType});
  factory _CreatePasswordResponseModel.fromJson(Map<String, dynamic> json) => _$CreatePasswordResponseModelFromJson(json);

@override final  String? message;
@override@JsonKey(name: 'user_type') final  String? userType;

/// Create a copy of CreatePasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreatePasswordResponseModelCopyWith<_CreatePasswordResponseModel> get copyWith => __$CreatePasswordResponseModelCopyWithImpl<_CreatePasswordResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreatePasswordResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreatePasswordResponseModel&&(identical(other.message, message) || other.message == message)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,message,userType);

@override
String toString() {
  return 'CreatePasswordResponseModel(message: $message, userType: $userType)';
}


}

/// @nodoc
abstract mixin class _$CreatePasswordResponseModelCopyWith<$Res> implements $CreatePasswordResponseModelCopyWith<$Res> {
  factory _$CreatePasswordResponseModelCopyWith(_CreatePasswordResponseModel value, $Res Function(_CreatePasswordResponseModel) _then) = __$CreatePasswordResponseModelCopyWithImpl;
@override @useResult
$Res call({
 String? message,@JsonKey(name: 'user_type') String? userType
});




}
/// @nodoc
class __$CreatePasswordResponseModelCopyWithImpl<$Res>
    implements _$CreatePasswordResponseModelCopyWith<$Res> {
  __$CreatePasswordResponseModelCopyWithImpl(this._self, this._then);

  final _CreatePasswordResponseModel _self;
  final $Res Function(_CreatePasswordResponseModel) _then;

/// Create a copy of CreatePasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? message = freezed,Object? userType = freezed,}) {
  return _then(_CreatePasswordResponseModel(
message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,userType: freezed == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
