// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_password_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreatePasswordRequestModel {

 String get email;@JsonKey(name: 'new_password') String get newPassword;@JsonKey(name: 'confirm_password') String get confirmPassword;@JsonKey(name: 'verified_token') String get verifiedToken;
/// Create a copy of CreatePasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreatePasswordRequestModelCopyWith<CreatePasswordRequestModel> get copyWith => _$CreatePasswordRequestModelCopyWithImpl<CreatePasswordRequestModel>(this as CreatePasswordRequestModel, _$identity);

  /// Serializes this CreatePasswordRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordRequestModel&&(identical(other.email, email) || other.email == email)&&(identical(other.newPassword, newPassword) || other.newPassword == newPassword)&&(identical(other.confirmPassword, confirmPassword) || other.confirmPassword == confirmPassword)&&(identical(other.verifiedToken, verifiedToken) || other.verifiedToken == verifiedToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,newPassword,confirmPassword,verifiedToken);

@override
String toString() {
  return 'CreatePasswordRequestModel(email: $email, newPassword: $newPassword, confirmPassword: $confirmPassword, verifiedToken: $verifiedToken)';
}


}

/// @nodoc
abstract mixin class $CreatePasswordRequestModelCopyWith<$Res>  {
  factory $CreatePasswordRequestModelCopyWith(CreatePasswordRequestModel value, $Res Function(CreatePasswordRequestModel) _then) = _$CreatePasswordRequestModelCopyWithImpl;
@useResult
$Res call({
 String email,@JsonKey(name: 'new_password') String newPassword,@JsonKey(name: 'confirm_password') String confirmPassword,@JsonKey(name: 'verified_token') String verifiedToken
});




}
/// @nodoc
class _$CreatePasswordRequestModelCopyWithImpl<$Res>
    implements $CreatePasswordRequestModelCopyWith<$Res> {
  _$CreatePasswordRequestModelCopyWithImpl(this._self, this._then);

  final CreatePasswordRequestModel _self;
  final $Res Function(CreatePasswordRequestModel) _then;

/// Create a copy of CreatePasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? newPassword = null,Object? confirmPassword = null,Object? verifiedToken = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,newPassword: null == newPassword ? _self.newPassword : newPassword // ignore: cast_nullable_to_non_nullable
as String,confirmPassword: null == confirmPassword ? _self.confirmPassword : confirmPassword // ignore: cast_nullable_to_non_nullable
as String,verifiedToken: null == verifiedToken ? _self.verifiedToken : verifiedToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CreatePasswordRequestModel implements CreatePasswordRequestModel {
  const _CreatePasswordRequestModel({required this.email, @JsonKey(name: 'new_password') required this.newPassword, @JsonKey(name: 'confirm_password') required this.confirmPassword, @JsonKey(name: 'verified_token') required this.verifiedToken});
  factory _CreatePasswordRequestModel.fromJson(Map<String, dynamic> json) => _$CreatePasswordRequestModelFromJson(json);

@override final  String email;
@override@JsonKey(name: 'new_password') final  String newPassword;
@override@JsonKey(name: 'confirm_password') final  String confirmPassword;
@override@JsonKey(name: 'verified_token') final  String verifiedToken;

/// Create a copy of CreatePasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreatePasswordRequestModelCopyWith<_CreatePasswordRequestModel> get copyWith => __$CreatePasswordRequestModelCopyWithImpl<_CreatePasswordRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CreatePasswordRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreatePasswordRequestModel&&(identical(other.email, email) || other.email == email)&&(identical(other.newPassword, newPassword) || other.newPassword == newPassword)&&(identical(other.confirmPassword, confirmPassword) || other.confirmPassword == confirmPassword)&&(identical(other.verifiedToken, verifiedToken) || other.verifiedToken == verifiedToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,newPassword,confirmPassword,verifiedToken);

@override
String toString() {
  return 'CreatePasswordRequestModel(email: $email, newPassword: $newPassword, confirmPassword: $confirmPassword, verifiedToken: $verifiedToken)';
}


}

/// @nodoc
abstract mixin class _$CreatePasswordRequestModelCopyWith<$Res> implements $CreatePasswordRequestModelCopyWith<$Res> {
  factory _$CreatePasswordRequestModelCopyWith(_CreatePasswordRequestModel value, $Res Function(_CreatePasswordRequestModel) _then) = __$CreatePasswordRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String email,@JsonKey(name: 'new_password') String newPassword,@JsonKey(name: 'confirm_password') String confirmPassword,@JsonKey(name: 'verified_token') String verifiedToken
});




}
/// @nodoc
class __$CreatePasswordRequestModelCopyWithImpl<$Res>
    implements _$CreatePasswordRequestModelCopyWith<$Res> {
  __$CreatePasswordRequestModelCopyWithImpl(this._self, this._then);

  final _CreatePasswordRequestModel _self;
  final $Res Function(_CreatePasswordRequestModel) _then;

/// Create a copy of CreatePasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? newPassword = null,Object? confirmPassword = null,Object? verifiedToken = null,}) {
  return _then(_CreatePasswordRequestModel(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,newPassword: null == newPassword ? _self.newPassword : newPassword // ignore: cast_nullable_to_non_nullable
as String,confirmPassword: null == confirmPassword ? _self.confirmPassword : confirmPassword // ignore: cast_nullable_to_non_nullable
as String,verifiedToken: null == verifiedToken ? _self.verifiedToken : verifiedToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
