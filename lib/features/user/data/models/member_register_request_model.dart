import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/member_register_request.dart';

part 'member_register_request_model.freezed.dart';
part 'member_register_request_model.g.dart';

@freezed
abstract class MemberRegisterRequestModel with _$MemberRegisterRequestModel {
  const factory MemberRegisterRequestModel({
    required String username,
    required String email,
    required String password,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'confirm_password') required String confirmPassword,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name') required String firstName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name') required String lastName,
    required String name, // Organization name
    required String phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'identity_card_no') required String identityCardNo,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_member_type_id') required int appMasMemberTypeId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_government_sector_other') String? appMasGovernmentSectorOther,
    @J<PERSON><PERSON><PERSON>(name: 'app_mas_ministry_id') int? appMasMinistryId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_ministry_other') String? appMasMinistryOther,
    @JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,
    @JsonKey(name: 'app_mas_department_other') String? appMasDepartmentOther,
    @JsonKey(name: 'is_notification') @Default("1") String isNotification,
    @Default("th") String lang,
    @Default("1") String status,
    @JsonKey(name: 'otp_token') required String otpToken,
  }) = _MemberRegisterRequestModel;

  factory MemberRegisterRequestModel.fromJson(Map<String, dynamic> json) =>
      _$MemberRegisterRequestModelFromJson(json);
}

extension MemberRegisterRequestModelX on MemberRegisterRequestModel {
  MemberRegisterRequest toEntity() => MemberRegisterRequest(
        username: username,
        email: email,
        password: password,
        confirmPassword: confirmPassword,
        firstName: firstName,
        lastName: lastName,
        name: name,
        phone: phone,
        identityCardNo: identityCardNo,
        appMasMemberTypeId: appMasMemberTypeId,
        appMasGovernmentSectorId: appMasGovernmentSectorId,
        appMasGovernmentSectorOther: appMasGovernmentSectorOther,
        appMasMinistryId: appMasMinistryId,
        appMasMinistryOther: appMasMinistryOther,
        appMasDepartmentId: appMasDepartmentId,
        appMasDepartmentOther: appMasDepartmentOther,
        isNotification: isNotification,
        lang: lang,
        status: status,
        otpToken: otpToken,
      );
}

extension MemberRegisterRequestX on MemberRegisterRequest {
  MemberRegisterRequestModel toModel() => MemberRegisterRequestModel(
        username: username,
        email: email,
        password: password,
        confirmPassword: confirmPassword,
        firstName: firstName,
        lastName: lastName,
        name: name,
        phone: phone,
        identityCardNo: identityCardNo,
        appMasMemberTypeId: appMasMemberTypeId,
        appMasGovernmentSectorId: appMasGovernmentSectorId,
        appMasGovernmentSectorOther: appMasGovernmentSectorOther,
        appMasMinistryId: appMasMinistryId,
        appMasMinistryOther: appMasMinistryOther,
        appMasDepartmentId: appMasDepartmentId,
        appMasDepartmentOther: appMasDepartmentOther,
        isNotification: isNotification,
        lang: lang,
        status: status,
        otpToken: otpToken,
      );
}
