// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserModel {

 int get id; String? get name;@JsonKey(name: 'first_name') String? get firstName;@JsonKey(name: 'last_name') String? get lastName; String get email;@JsonKey(name: 'email_second') String? get emailSecond; String get phone;@JsonKey(name: 'phone_second') String? get phoneSecond;@JsonKey(name: 'identity_card_no') String? get identityCardNo; String get username;// Member-specific fields
@JsonKey(name: 'app_mas_member_type_id') int? get appMasMemberTypeId;@JsonKey(name: 'app_mas_member_type_display') String? get appMasMemberTypeDisplay;@JsonKey(name: 'app_mas_government_sector_id') int? get appMasGovernmentSectorId;@JsonKey(name: 'app_mas_government_sector_display') String? get appMasGovernmentSectorDisplay;@JsonKey(name: 'app_mas_ministry_id') int? get appMasMinistryId;@JsonKey(name: 'app_mas_ministry_display') String? get appMasMinistryDisplay;@JsonKey(name: 'app_mas_department_id') int? get appMasDepartmentId;@JsonKey(name: 'app_mas_department_display') String? get appMasDepartmentDisplay; String? get website;@JsonKey(name: 'create_date') DateTime? get createDate; String? get status;@JsonKey(name: 'status_display') String? get statusDisplay;// Consultant-specific fields
@JsonKey(name: 'consult_type') int? get consultType;@JsonKey(name: 'consult_type_display') String? get consultTypeDisplay;@JsonKey(name: 'corporate_type_id') int? get corporateTypeId;@JsonKey(name: 'corporate_type_display') String? get corporateTypeDisplay;@JsonKey(name: 'maker_name') String? get makerName;@JsonKey(name: 'maker_phone') String? get makerPhone;@JsonKey(name: 'maker_email') String? get makerEmail; String? get verify;@JsonKey(name: 'verify_display') String? get verifyDisplay; double? get score;@JsonKey(name: 'is_active_matching') bool? get isActiveMatching;// Common fields
@JsonKey(name: 'is_notification') String get isNotification; String get lang;@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType get userType;
/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserModelCopyWith<UserModel> get copyWith => _$UserModelCopyWithImpl<UserModel>(this as UserModel, _$identity);

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.email, email) || other.email == email)&&(identical(other.emailSecond, emailSecond) || other.emailSecond == emailSecond)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.phoneSecond, phoneSecond) || other.phoneSecond == phoneSecond)&&(identical(other.identityCardNo, identityCardNo) || other.identityCardNo == identityCardNo)&&(identical(other.username, username) || other.username == username)&&(identical(other.appMasMemberTypeId, appMasMemberTypeId) || other.appMasMemberTypeId == appMasMemberTypeId)&&(identical(other.appMasMemberTypeDisplay, appMasMemberTypeDisplay) || other.appMasMemberTypeDisplay == appMasMemberTypeDisplay)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.appMasGovernmentSectorDisplay, appMasGovernmentSectorDisplay) || other.appMasGovernmentSectorDisplay == appMasGovernmentSectorDisplay)&&(identical(other.appMasMinistryId, appMasMinistryId) || other.appMasMinistryId == appMasMinistryId)&&(identical(other.appMasMinistryDisplay, appMasMinistryDisplay) || other.appMasMinistryDisplay == appMasMinistryDisplay)&&(identical(other.appMasDepartmentId, appMasDepartmentId) || other.appMasDepartmentId == appMasDepartmentId)&&(identical(other.appMasDepartmentDisplay, appMasDepartmentDisplay) || other.appMasDepartmentDisplay == appMasDepartmentDisplay)&&(identical(other.website, website) || other.website == website)&&(identical(other.createDate, createDate) || other.createDate == createDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusDisplay, statusDisplay) || other.statusDisplay == statusDisplay)&&(identical(other.consultType, consultType) || other.consultType == consultType)&&(identical(other.consultTypeDisplay, consultTypeDisplay) || other.consultTypeDisplay == consultTypeDisplay)&&(identical(other.corporateTypeId, corporateTypeId) || other.corporateTypeId == corporateTypeId)&&(identical(other.corporateTypeDisplay, corporateTypeDisplay) || other.corporateTypeDisplay == corporateTypeDisplay)&&(identical(other.makerName, makerName) || other.makerName == makerName)&&(identical(other.makerPhone, makerPhone) || other.makerPhone == makerPhone)&&(identical(other.makerEmail, makerEmail) || other.makerEmail == makerEmail)&&(identical(other.verify, verify) || other.verify == verify)&&(identical(other.verifyDisplay, verifyDisplay) || other.verifyDisplay == verifyDisplay)&&(identical(other.score, score) || other.score == score)&&(identical(other.isActiveMatching, isActiveMatching) || other.isActiveMatching == isActiveMatching)&&(identical(other.isNotification, isNotification) || other.isNotification == isNotification)&&(identical(other.lang, lang) || other.lang == lang)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,firstName,lastName,email,emailSecond,phone,phoneSecond,identityCardNo,username,appMasMemberTypeId,appMasMemberTypeDisplay,appMasGovernmentSectorId,appMasGovernmentSectorDisplay,appMasMinistryId,appMasMinistryDisplay,appMasDepartmentId,appMasDepartmentDisplay,website,createDate,status,statusDisplay,consultType,consultTypeDisplay,corporateTypeId,corporateTypeDisplay,makerName,makerPhone,makerEmail,verify,verifyDisplay,score,isActiveMatching,isNotification,lang,userType]);

@override
String toString() {
  return 'UserModel(id: $id, name: $name, firstName: $firstName, lastName: $lastName, email: $email, emailSecond: $emailSecond, phone: $phone, phoneSecond: $phoneSecond, identityCardNo: $identityCardNo, username: $username, appMasMemberTypeId: $appMasMemberTypeId, appMasMemberTypeDisplay: $appMasMemberTypeDisplay, appMasGovernmentSectorId: $appMasGovernmentSectorId, appMasGovernmentSectorDisplay: $appMasGovernmentSectorDisplay, appMasMinistryId: $appMasMinistryId, appMasMinistryDisplay: $appMasMinistryDisplay, appMasDepartmentId: $appMasDepartmentId, appMasDepartmentDisplay: $appMasDepartmentDisplay, website: $website, createDate: $createDate, status: $status, statusDisplay: $statusDisplay, consultType: $consultType, consultTypeDisplay: $consultTypeDisplay, corporateTypeId: $corporateTypeId, corporateTypeDisplay: $corporateTypeDisplay, makerName: $makerName, makerPhone: $makerPhone, makerEmail: $makerEmail, verify: $verify, verifyDisplay: $verifyDisplay, score: $score, isActiveMatching: $isActiveMatching, isNotification: $isNotification, lang: $lang, userType: $userType)';
}


}

/// @nodoc
abstract mixin class $UserModelCopyWith<$Res>  {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) _then) = _$UserModelCopyWithImpl;
@useResult
$Res call({
 int id, String? name,@JsonKey(name: 'first_name') String? firstName,@JsonKey(name: 'last_name') String? lastName, String email,@JsonKey(name: 'email_second') String? emailSecond, String phone,@JsonKey(name: 'phone_second') String? phoneSecond,@JsonKey(name: 'identity_card_no') String? identityCardNo, String username,@JsonKey(name: 'app_mas_member_type_id') int? appMasMemberTypeId,@JsonKey(name: 'app_mas_member_type_display') String? appMasMemberTypeDisplay,@JsonKey(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,@JsonKey(name: 'app_mas_government_sector_display') String? appMasGovernmentSectorDisplay,@JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,@JsonKey(name: 'app_mas_ministry_display') String? appMasMinistryDisplay,@JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,@JsonKey(name: 'app_mas_department_display') String? appMasDepartmentDisplay, String? website,@JsonKey(name: 'create_date') DateTime? createDate, String? status,@JsonKey(name: 'status_display') String? statusDisplay,@JsonKey(name: 'consult_type') int? consultType,@JsonKey(name: 'consult_type_display') String? consultTypeDisplay,@JsonKey(name: 'corporate_type_id') int? corporateTypeId,@JsonKey(name: 'corporate_type_display') String? corporateTypeDisplay,@JsonKey(name: 'maker_name') String? makerName,@JsonKey(name: 'maker_phone') String? makerPhone,@JsonKey(name: 'maker_email') String? makerEmail, String? verify,@JsonKey(name: 'verify_display') String? verifyDisplay, double? score,@JsonKey(name: 'is_active_matching') bool? isActiveMatching,@JsonKey(name: 'is_notification') String isNotification, String lang,@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType userType
});




}
/// @nodoc
class _$UserModelCopyWithImpl<$Res>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._self, this._then);

  final UserModel _self;
  final $Res Function(UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = freezed,Object? firstName = freezed,Object? lastName = freezed,Object? email = null,Object? emailSecond = freezed,Object? phone = null,Object? phoneSecond = freezed,Object? identityCardNo = freezed,Object? username = null,Object? appMasMemberTypeId = freezed,Object? appMasMemberTypeDisplay = freezed,Object? appMasGovernmentSectorId = freezed,Object? appMasGovernmentSectorDisplay = freezed,Object? appMasMinistryId = freezed,Object? appMasMinistryDisplay = freezed,Object? appMasDepartmentId = freezed,Object? appMasDepartmentDisplay = freezed,Object? website = freezed,Object? createDate = freezed,Object? status = freezed,Object? statusDisplay = freezed,Object? consultType = freezed,Object? consultTypeDisplay = freezed,Object? corporateTypeId = freezed,Object? corporateTypeDisplay = freezed,Object? makerName = freezed,Object? makerPhone = freezed,Object? makerEmail = freezed,Object? verify = freezed,Object? verifyDisplay = freezed,Object? score = freezed,Object? isActiveMatching = freezed,Object? isNotification = null,Object? lang = null,Object? userType = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,emailSecond: freezed == emailSecond ? _self.emailSecond : emailSecond // ignore: cast_nullable_to_non_nullable
as String?,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,phoneSecond: freezed == phoneSecond ? _self.phoneSecond : phoneSecond // ignore: cast_nullable_to_non_nullable
as String?,identityCardNo: freezed == identityCardNo ? _self.identityCardNo : identityCardNo // ignore: cast_nullable_to_non_nullable
as String?,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,appMasMemberTypeId: freezed == appMasMemberTypeId ? _self.appMasMemberTypeId : appMasMemberTypeId // ignore: cast_nullable_to_non_nullable
as int?,appMasMemberTypeDisplay: freezed == appMasMemberTypeDisplay ? _self.appMasMemberTypeDisplay : appMasMemberTypeDisplay // ignore: cast_nullable_to_non_nullable
as String?,appMasGovernmentSectorId: freezed == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int?,appMasGovernmentSectorDisplay: freezed == appMasGovernmentSectorDisplay ? _self.appMasGovernmentSectorDisplay : appMasGovernmentSectorDisplay // ignore: cast_nullable_to_non_nullable
as String?,appMasMinistryId: freezed == appMasMinistryId ? _self.appMasMinistryId : appMasMinistryId // ignore: cast_nullable_to_non_nullable
as int?,appMasMinistryDisplay: freezed == appMasMinistryDisplay ? _self.appMasMinistryDisplay : appMasMinistryDisplay // ignore: cast_nullable_to_non_nullable
as String?,appMasDepartmentId: freezed == appMasDepartmentId ? _self.appMasDepartmentId : appMasDepartmentId // ignore: cast_nullable_to_non_nullable
as int?,appMasDepartmentDisplay: freezed == appMasDepartmentDisplay ? _self.appMasDepartmentDisplay : appMasDepartmentDisplay // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,createDate: freezed == createDate ? _self.createDate : createDate // ignore: cast_nullable_to_non_nullable
as DateTime?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,statusDisplay: freezed == statusDisplay ? _self.statusDisplay : statusDisplay // ignore: cast_nullable_to_non_nullable
as String?,consultType: freezed == consultType ? _self.consultType : consultType // ignore: cast_nullable_to_non_nullable
as int?,consultTypeDisplay: freezed == consultTypeDisplay ? _self.consultTypeDisplay : consultTypeDisplay // ignore: cast_nullable_to_non_nullable
as String?,corporateTypeId: freezed == corporateTypeId ? _self.corporateTypeId : corporateTypeId // ignore: cast_nullable_to_non_nullable
as int?,corporateTypeDisplay: freezed == corporateTypeDisplay ? _self.corporateTypeDisplay : corporateTypeDisplay // ignore: cast_nullable_to_non_nullable
as String?,makerName: freezed == makerName ? _self.makerName : makerName // ignore: cast_nullable_to_non_nullable
as String?,makerPhone: freezed == makerPhone ? _self.makerPhone : makerPhone // ignore: cast_nullable_to_non_nullable
as String?,makerEmail: freezed == makerEmail ? _self.makerEmail : makerEmail // ignore: cast_nullable_to_non_nullable
as String?,verify: freezed == verify ? _self.verify : verify // ignore: cast_nullable_to_non_nullable
as String?,verifyDisplay: freezed == verifyDisplay ? _self.verifyDisplay : verifyDisplay // ignore: cast_nullable_to_non_nullable
as String?,score: freezed == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as double?,isActiveMatching: freezed == isActiveMatching ? _self.isActiveMatching : isActiveMatching // ignore: cast_nullable_to_non_nullable
as bool?,isNotification: null == isNotification ? _self.isNotification : isNotification // ignore: cast_nullable_to_non_nullable
as String,lang: null == lang ? _self.lang : lang // ignore: cast_nullable_to_non_nullable
as String,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as UserType,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _UserModel implements UserModel {
  const _UserModel({required this.id, this.name, @JsonKey(name: 'first_name') this.firstName, @JsonKey(name: 'last_name') this.lastName, required this.email, @JsonKey(name: 'email_second') this.emailSecond, required this.phone, @JsonKey(name: 'phone_second') this.phoneSecond, @JsonKey(name: 'identity_card_no') this.identityCardNo, required this.username, @JsonKey(name: 'app_mas_member_type_id') this.appMasMemberTypeId, @JsonKey(name: 'app_mas_member_type_display') this.appMasMemberTypeDisplay, @JsonKey(name: 'app_mas_government_sector_id') this.appMasGovernmentSectorId, @JsonKey(name: 'app_mas_government_sector_display') this.appMasGovernmentSectorDisplay, @JsonKey(name: 'app_mas_ministry_id') this.appMasMinistryId, @JsonKey(name: 'app_mas_ministry_display') this.appMasMinistryDisplay, @JsonKey(name: 'app_mas_department_id') this.appMasDepartmentId, @JsonKey(name: 'app_mas_department_display') this.appMasDepartmentDisplay, this.website, @JsonKey(name: 'create_date') this.createDate, this.status, @JsonKey(name: 'status_display') this.statusDisplay, @JsonKey(name: 'consult_type') this.consultType, @JsonKey(name: 'consult_type_display') this.consultTypeDisplay, @JsonKey(name: 'corporate_type_id') this.corporateTypeId, @JsonKey(name: 'corporate_type_display') this.corporateTypeDisplay, @JsonKey(name: 'maker_name') this.makerName, @JsonKey(name: 'maker_phone') this.makerPhone, @JsonKey(name: 'maker_email') this.makerEmail, this.verify, @JsonKey(name: 'verify_display') this.verifyDisplay, this.score, @JsonKey(name: 'is_active_matching') this.isActiveMatching, @JsonKey(name: 'is_notification') required this.isNotification, required this.lang, @JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) required this.userType});
  factory _UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

@override final  int id;
@override final  String? name;
@override@JsonKey(name: 'first_name') final  String? firstName;
@override@JsonKey(name: 'last_name') final  String? lastName;
@override final  String email;
@override@JsonKey(name: 'email_second') final  String? emailSecond;
@override final  String phone;
@override@JsonKey(name: 'phone_second') final  String? phoneSecond;
@override@JsonKey(name: 'identity_card_no') final  String? identityCardNo;
@override final  String username;
// Member-specific fields
@override@JsonKey(name: 'app_mas_member_type_id') final  int? appMasMemberTypeId;
@override@JsonKey(name: 'app_mas_member_type_display') final  String? appMasMemberTypeDisplay;
@override@JsonKey(name: 'app_mas_government_sector_id') final  int? appMasGovernmentSectorId;
@override@JsonKey(name: 'app_mas_government_sector_display') final  String? appMasGovernmentSectorDisplay;
@override@JsonKey(name: 'app_mas_ministry_id') final  int? appMasMinistryId;
@override@JsonKey(name: 'app_mas_ministry_display') final  String? appMasMinistryDisplay;
@override@JsonKey(name: 'app_mas_department_id') final  int? appMasDepartmentId;
@override@JsonKey(name: 'app_mas_department_display') final  String? appMasDepartmentDisplay;
@override final  String? website;
@override@JsonKey(name: 'create_date') final  DateTime? createDate;
@override final  String? status;
@override@JsonKey(name: 'status_display') final  String? statusDisplay;
// Consultant-specific fields
@override@JsonKey(name: 'consult_type') final  int? consultType;
@override@JsonKey(name: 'consult_type_display') final  String? consultTypeDisplay;
@override@JsonKey(name: 'corporate_type_id') final  int? corporateTypeId;
@override@JsonKey(name: 'corporate_type_display') final  String? corporateTypeDisplay;
@override@JsonKey(name: 'maker_name') final  String? makerName;
@override@JsonKey(name: 'maker_phone') final  String? makerPhone;
@override@JsonKey(name: 'maker_email') final  String? makerEmail;
@override final  String? verify;
@override@JsonKey(name: 'verify_display') final  String? verifyDisplay;
@override final  double? score;
@override@JsonKey(name: 'is_active_matching') final  bool? isActiveMatching;
// Common fields
@override@JsonKey(name: 'is_notification') final  String isNotification;
@override final  String lang;
@override@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) final  UserType userType;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserModelCopyWith<_UserModel> get copyWith => __$UserModelCopyWithImpl<_UserModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.email, email) || other.email == email)&&(identical(other.emailSecond, emailSecond) || other.emailSecond == emailSecond)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.phoneSecond, phoneSecond) || other.phoneSecond == phoneSecond)&&(identical(other.identityCardNo, identityCardNo) || other.identityCardNo == identityCardNo)&&(identical(other.username, username) || other.username == username)&&(identical(other.appMasMemberTypeId, appMasMemberTypeId) || other.appMasMemberTypeId == appMasMemberTypeId)&&(identical(other.appMasMemberTypeDisplay, appMasMemberTypeDisplay) || other.appMasMemberTypeDisplay == appMasMemberTypeDisplay)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.appMasGovernmentSectorDisplay, appMasGovernmentSectorDisplay) || other.appMasGovernmentSectorDisplay == appMasGovernmentSectorDisplay)&&(identical(other.appMasMinistryId, appMasMinistryId) || other.appMasMinistryId == appMasMinistryId)&&(identical(other.appMasMinistryDisplay, appMasMinistryDisplay) || other.appMasMinistryDisplay == appMasMinistryDisplay)&&(identical(other.appMasDepartmentId, appMasDepartmentId) || other.appMasDepartmentId == appMasDepartmentId)&&(identical(other.appMasDepartmentDisplay, appMasDepartmentDisplay) || other.appMasDepartmentDisplay == appMasDepartmentDisplay)&&(identical(other.website, website) || other.website == website)&&(identical(other.createDate, createDate) || other.createDate == createDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusDisplay, statusDisplay) || other.statusDisplay == statusDisplay)&&(identical(other.consultType, consultType) || other.consultType == consultType)&&(identical(other.consultTypeDisplay, consultTypeDisplay) || other.consultTypeDisplay == consultTypeDisplay)&&(identical(other.corporateTypeId, corporateTypeId) || other.corporateTypeId == corporateTypeId)&&(identical(other.corporateTypeDisplay, corporateTypeDisplay) || other.corporateTypeDisplay == corporateTypeDisplay)&&(identical(other.makerName, makerName) || other.makerName == makerName)&&(identical(other.makerPhone, makerPhone) || other.makerPhone == makerPhone)&&(identical(other.makerEmail, makerEmail) || other.makerEmail == makerEmail)&&(identical(other.verify, verify) || other.verify == verify)&&(identical(other.verifyDisplay, verifyDisplay) || other.verifyDisplay == verifyDisplay)&&(identical(other.score, score) || other.score == score)&&(identical(other.isActiveMatching, isActiveMatching) || other.isActiveMatching == isActiveMatching)&&(identical(other.isNotification, isNotification) || other.isNotification == isNotification)&&(identical(other.lang, lang) || other.lang == lang)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,firstName,lastName,email,emailSecond,phone,phoneSecond,identityCardNo,username,appMasMemberTypeId,appMasMemberTypeDisplay,appMasGovernmentSectorId,appMasGovernmentSectorDisplay,appMasMinistryId,appMasMinistryDisplay,appMasDepartmentId,appMasDepartmentDisplay,website,createDate,status,statusDisplay,consultType,consultTypeDisplay,corporateTypeId,corporateTypeDisplay,makerName,makerPhone,makerEmail,verify,verifyDisplay,score,isActiveMatching,isNotification,lang,userType]);

@override
String toString() {
  return 'UserModel(id: $id, name: $name, firstName: $firstName, lastName: $lastName, email: $email, emailSecond: $emailSecond, phone: $phone, phoneSecond: $phoneSecond, identityCardNo: $identityCardNo, username: $username, appMasMemberTypeId: $appMasMemberTypeId, appMasMemberTypeDisplay: $appMasMemberTypeDisplay, appMasGovernmentSectorId: $appMasGovernmentSectorId, appMasGovernmentSectorDisplay: $appMasGovernmentSectorDisplay, appMasMinistryId: $appMasMinistryId, appMasMinistryDisplay: $appMasMinistryDisplay, appMasDepartmentId: $appMasDepartmentId, appMasDepartmentDisplay: $appMasDepartmentDisplay, website: $website, createDate: $createDate, status: $status, statusDisplay: $statusDisplay, consultType: $consultType, consultTypeDisplay: $consultTypeDisplay, corporateTypeId: $corporateTypeId, corporateTypeDisplay: $corporateTypeDisplay, makerName: $makerName, makerPhone: $makerPhone, makerEmail: $makerEmail, verify: $verify, verifyDisplay: $verifyDisplay, score: $score, isActiveMatching: $isActiveMatching, isNotification: $isNotification, lang: $lang, userType: $userType)';
}


}

/// @nodoc
abstract mixin class _$UserModelCopyWith<$Res> implements $UserModelCopyWith<$Res> {
  factory _$UserModelCopyWith(_UserModel value, $Res Function(_UserModel) _then) = __$UserModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String? name,@JsonKey(name: 'first_name') String? firstName,@JsonKey(name: 'last_name') String? lastName, String email,@JsonKey(name: 'email_second') String? emailSecond, String phone,@JsonKey(name: 'phone_second') String? phoneSecond,@JsonKey(name: 'identity_card_no') String? identityCardNo, String username,@JsonKey(name: 'app_mas_member_type_id') int? appMasMemberTypeId,@JsonKey(name: 'app_mas_member_type_display') String? appMasMemberTypeDisplay,@JsonKey(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,@JsonKey(name: 'app_mas_government_sector_display') String? appMasGovernmentSectorDisplay,@JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,@JsonKey(name: 'app_mas_ministry_display') String? appMasMinistryDisplay,@JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,@JsonKey(name: 'app_mas_department_display') String? appMasDepartmentDisplay, String? website,@JsonKey(name: 'create_date') DateTime? createDate, String? status,@JsonKey(name: 'status_display') String? statusDisplay,@JsonKey(name: 'consult_type') int? consultType,@JsonKey(name: 'consult_type_display') String? consultTypeDisplay,@JsonKey(name: 'corporate_type_id') int? corporateTypeId,@JsonKey(name: 'corporate_type_display') String? corporateTypeDisplay,@JsonKey(name: 'maker_name') String? makerName,@JsonKey(name: 'maker_phone') String? makerPhone,@JsonKey(name: 'maker_email') String? makerEmail, String? verify,@JsonKey(name: 'verify_display') String? verifyDisplay, double? score,@JsonKey(name: 'is_active_matching') bool? isActiveMatching,@JsonKey(name: 'is_notification') String isNotification, String lang,@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType userType
});




}
/// @nodoc
class __$UserModelCopyWithImpl<$Res>
    implements _$UserModelCopyWith<$Res> {
  __$UserModelCopyWithImpl(this._self, this._then);

  final _UserModel _self;
  final $Res Function(_UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = freezed,Object? firstName = freezed,Object? lastName = freezed,Object? email = null,Object? emailSecond = freezed,Object? phone = null,Object? phoneSecond = freezed,Object? identityCardNo = freezed,Object? username = null,Object? appMasMemberTypeId = freezed,Object? appMasMemberTypeDisplay = freezed,Object? appMasGovernmentSectorId = freezed,Object? appMasGovernmentSectorDisplay = freezed,Object? appMasMinistryId = freezed,Object? appMasMinistryDisplay = freezed,Object? appMasDepartmentId = freezed,Object? appMasDepartmentDisplay = freezed,Object? website = freezed,Object? createDate = freezed,Object? status = freezed,Object? statusDisplay = freezed,Object? consultType = freezed,Object? consultTypeDisplay = freezed,Object? corporateTypeId = freezed,Object? corporateTypeDisplay = freezed,Object? makerName = freezed,Object? makerPhone = freezed,Object? makerEmail = freezed,Object? verify = freezed,Object? verifyDisplay = freezed,Object? score = freezed,Object? isActiveMatching = freezed,Object? isNotification = null,Object? lang = null,Object? userType = null,}) {
  return _then(_UserModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,firstName: freezed == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,emailSecond: freezed == emailSecond ? _self.emailSecond : emailSecond // ignore: cast_nullable_to_non_nullable
as String?,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,phoneSecond: freezed == phoneSecond ? _self.phoneSecond : phoneSecond // ignore: cast_nullable_to_non_nullable
as String?,identityCardNo: freezed == identityCardNo ? _self.identityCardNo : identityCardNo // ignore: cast_nullable_to_non_nullable
as String?,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,appMasMemberTypeId: freezed == appMasMemberTypeId ? _self.appMasMemberTypeId : appMasMemberTypeId // ignore: cast_nullable_to_non_nullable
as int?,appMasMemberTypeDisplay: freezed == appMasMemberTypeDisplay ? _self.appMasMemberTypeDisplay : appMasMemberTypeDisplay // ignore: cast_nullable_to_non_nullable
as String?,appMasGovernmentSectorId: freezed == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int?,appMasGovernmentSectorDisplay: freezed == appMasGovernmentSectorDisplay ? _self.appMasGovernmentSectorDisplay : appMasGovernmentSectorDisplay // ignore: cast_nullable_to_non_nullable
as String?,appMasMinistryId: freezed == appMasMinistryId ? _self.appMasMinistryId : appMasMinistryId // ignore: cast_nullable_to_non_nullable
as int?,appMasMinistryDisplay: freezed == appMasMinistryDisplay ? _self.appMasMinistryDisplay : appMasMinistryDisplay // ignore: cast_nullable_to_non_nullable
as String?,appMasDepartmentId: freezed == appMasDepartmentId ? _self.appMasDepartmentId : appMasDepartmentId // ignore: cast_nullable_to_non_nullable
as int?,appMasDepartmentDisplay: freezed == appMasDepartmentDisplay ? _self.appMasDepartmentDisplay : appMasDepartmentDisplay // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,createDate: freezed == createDate ? _self.createDate : createDate // ignore: cast_nullable_to_non_nullable
as DateTime?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,statusDisplay: freezed == statusDisplay ? _self.statusDisplay : statusDisplay // ignore: cast_nullable_to_non_nullable
as String?,consultType: freezed == consultType ? _self.consultType : consultType // ignore: cast_nullable_to_non_nullable
as int?,consultTypeDisplay: freezed == consultTypeDisplay ? _self.consultTypeDisplay : consultTypeDisplay // ignore: cast_nullable_to_non_nullable
as String?,corporateTypeId: freezed == corporateTypeId ? _self.corporateTypeId : corporateTypeId // ignore: cast_nullable_to_non_nullable
as int?,corporateTypeDisplay: freezed == corporateTypeDisplay ? _self.corporateTypeDisplay : corporateTypeDisplay // ignore: cast_nullable_to_non_nullable
as String?,makerName: freezed == makerName ? _self.makerName : makerName // ignore: cast_nullable_to_non_nullable
as String?,makerPhone: freezed == makerPhone ? _self.makerPhone : makerPhone // ignore: cast_nullable_to_non_nullable
as String?,makerEmail: freezed == makerEmail ? _self.makerEmail : makerEmail // ignore: cast_nullable_to_non_nullable
as String?,verify: freezed == verify ? _self.verify : verify // ignore: cast_nullable_to_non_nullable
as String?,verifyDisplay: freezed == verifyDisplay ? _self.verifyDisplay : verifyDisplay // ignore: cast_nullable_to_non_nullable
as String?,score: freezed == score ? _self.score : score // ignore: cast_nullable_to_non_nullable
as double?,isActiveMatching: freezed == isActiveMatching ? _self.isActiveMatching : isActiveMatching // ignore: cast_nullable_to_non_nullable
as bool?,isNotification: null == isNotification ? _self.isNotification : isNotification // ignore: cast_nullable_to_non_nullable
as String,lang: null == lang ? _self.lang : lang // ignore: cast_nullable_to_non_nullable
as String,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as UserType,
  ));
}


}

// dart format on
