import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/create_password_request.dart';

part 'create_password_request_model.freezed.dart';
part 'create_password_request_model.g.dart';

@freezed
abstract class CreatePasswordRequestModel with _$CreatePasswordRequestModel {
  const factory CreatePasswordRequestModel({
    required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'new_password') required String newPassword,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'confirm_password') required String confirmPassword,
    @<PERSON><PERSON><PERSON>ey(name: 'verified_token') required String verifiedToken,
  }) = _CreatePasswordRequestModel;

  factory CreatePasswordRequestModel.fromJson(Map<String, dynamic> json) =>
      _$CreatePasswordRequestModelFromJson(json);
}

extension CreatePasswordRequestModelX on CreatePasswordRequestModel {
  CreatePasswordRequest toEntity() {
    return CreatePasswordRequest(
      email: email,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      verifiedToken: verifiedToken,
    );
  }
}

extension CreatePasswordRequestX on CreatePasswordRequest {
  CreatePasswordRequestModel toModel() {
    return CreatePasswordRequestModel(
      email: email,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      verifiedToken: verifiedToken,
    );
  }
}
