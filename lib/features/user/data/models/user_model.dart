import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';
import 'package:mcdc/core/enums/user_type.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
abstract class UserModel with _$UserModel {
  const factory UserModel({
    required int id,
    String? name,
    @Json<PERSON>ey(name: 'first_name') String? firstName,
    @Json<PERSON>ey(name: 'last_name') String? lastName,
    required String email,
    @Json<PERSON><PERSON>(name: 'email_second') String? emailSecond,
    required String phone,
    @Json<PERSON>ey(name: 'phone_second') String? phoneSecond,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'identity_card_no') String? identityCardNo,
    required String username,
    // Member-specific fields
    @JsonKey(name: 'app_mas_member_type_id') int? appMasMemberTypeId,
    @<PERSON>son<PERSON>ey(name: 'app_mas_member_type_display')
    String? appMasMemberTypeDisplay,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_government_sector_id')
    int? appMasGovernmentSectorId,
    @Json<PERSON>ey(name: 'app_mas_government_sector_display')
    String? appMasGovernmentSectorDisplay,
    @JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,
    @JsonKey(name: 'app_mas_ministry_display') String? appMasMinistryDisplay,
    @JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,
    @JsonKey(name: 'app_mas_department_display')
    String? appMasDepartmentDisplay,
    String? website,
    @JsonKey(name: 'create_date') DateTime? createDate,
    String? status,
    @JsonKey(name: 'status_display') String? statusDisplay,
    // Consultant-specific fields
    @JsonKey(name: 'consult_type') int? consultType,
    @JsonKey(name: 'consult_type_display') String? consultTypeDisplay,
    @JsonKey(name: 'corporate_type_id') int? corporateTypeId,
    @JsonKey(name: 'corporate_type_display') String? corporateTypeDisplay,
    @JsonKey(name: 'maker_name') String? makerName,
    @JsonKey(name: 'maker_phone') String? makerPhone,
    @JsonKey(name: 'maker_email') String? makerEmail,
    String? verify,
    @JsonKey(name: 'verify_display') String? verifyDisplay,
    double? score,
    @JsonKey(name: 'is_active_matching') bool? isActiveMatching,
    // Common fields
    @JsonKey(name: 'is_notification') required String isNotification,

    required String lang,
    @JsonKey(
      name: 'user_type',
      fromJson: UserType.fromJson,
      toJson: UserType.toJson,
    )
    required UserType userType,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  static List<UserModel> fromJsonList(List<Map<String, dynamic>> jsonList) =>
      jsonList.map((e) => UserModel.fromJson(e)).toList();
}

extension UserModelX on UserModel {
  User toEntity() => User(
    id: id,
    name: name,
    firstName: firstName,
    lastName: lastName,
    email: email,
    emailSecond: emailSecond,
    phone: phone,
    phoneSecond: phoneSecond,
    identityCardNo: identityCardNo,
    username: username,
    appMasMemberTypeId: appMasMemberTypeId,
    appMasMemberTypeDisplay: appMasMemberTypeDisplay,
    appMasGovernmentSectorId: appMasGovernmentSectorId,
    appMasGovernmentSectorDisplay: appMasGovernmentSectorDisplay,
    appMasMinistryId: appMasMinistryId,
    appMasMinistryDisplay: appMasMinistryDisplay,
    appMasDepartmentId: appMasDepartmentId,
    appMasDepartmentDisplay: appMasDepartmentDisplay,
    website: website,
    createDate: createDate,
    consultType: consultType,
    consultTypeDisplay: consultTypeDisplay,
    corporateTypeId: corporateTypeId,
    corporateTypeDisplay: corporateTypeDisplay,
    makerName: makerName,
    makerPhone: makerPhone,
    makerEmail: makerEmail,
    verify: verify,
    verifyDisplay: verifyDisplay,
    score: score,
    isActiveMatching: isActiveMatching,
    isNotification: isNotification,
    status: status,
    statusDisplay: statusDisplay,
    lang: lang,
    userType: userType,
  );
}
