import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart';

part 'check_id_card_result_model.freezed.dart';
part 'check_id_card_result_model.g.dart';

@freezed
abstract class CheckIdCardResultModel with _$CheckIdCardResultModel {
  const factory CheckIdCardResultModel({
    @JsonKey(name: 'is_duplicate') required bool isDuplicate,
    @JsonKey(name: 'existing_member_id') String? existingMemberId,
  }) = _CheckIdCardResultModel;

  factory CheckIdCardResultModel.fromJson(Map<String, dynamic> json) =>
      _$CheckIdCardResultModelFromJson(json);
}

extension CheckIdCardResultModelX on CheckIdCardResultModel {
  CheckIdCardResult toEntity() => CheckIdCardResult(
    isDuplicate: isDuplicate,
    existingMemberId: existingMemberId,
  );
}
