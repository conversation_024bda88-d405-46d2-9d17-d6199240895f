import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';

part 'register_validation_error_model.freezed.dart';
part 'register_validation_error_model.g.dart';

@freezed
abstract class RegisterValidationErrorModel
    with _$RegisterValidationErrorModel {
  const factory RegisterValidationErrorModel({
    @Json<PERSON>ey(name: 'username') List<String>? username,
    @<PERSON>son<PERSON><PERSON>(name: 'email') List<String>? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone') List<String>? phone,
  }) = _RegisterValidationErrorModel;

  factory RegisterValidationErrorModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterValidationErrorModelFromJson(json);
}

extension RegisterValidationErrorModelX on RegisterValidationErrorModel {
  RegisterValidationError toEntity() =>
      RegisterValidationError(username: username, email: email, phone: phone);
}
