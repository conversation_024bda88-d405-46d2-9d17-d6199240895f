// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'forgot_password_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ForgotPasswordRequestModel {

 String get email;
/// Create a copy of ForgotPasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ForgotPasswordRequestModelCopyWith<ForgotPasswordRequestModel> get copyWith => _$ForgotPasswordRequestModelCopyWithImpl<ForgotPasswordRequestModel>(this as ForgotPasswordRequestModel, _$identity);

  /// Serializes this ForgotPasswordRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ForgotPasswordRequestModel&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString() {
  return 'ForgotPasswordRequestModel(email: $email)';
}


}

/// @nodoc
abstract mixin class $ForgotPasswordRequestModelCopyWith<$Res>  {
  factory $ForgotPasswordRequestModelCopyWith(ForgotPasswordRequestModel value, $Res Function(ForgotPasswordRequestModel) _then) = _$ForgotPasswordRequestModelCopyWithImpl;
@useResult
$Res call({
 String email
});




}
/// @nodoc
class _$ForgotPasswordRequestModelCopyWithImpl<$Res>
    implements $ForgotPasswordRequestModelCopyWith<$Res> {
  _$ForgotPasswordRequestModelCopyWithImpl(this._self, this._then);

  final ForgotPasswordRequestModel _self;
  final $Res Function(ForgotPasswordRequestModel) _then;

/// Create a copy of ForgotPasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ForgotPasswordRequestModel implements ForgotPasswordRequestModel {
  const _ForgotPasswordRequestModel({required this.email});
  factory _ForgotPasswordRequestModel.fromJson(Map<String, dynamic> json) => _$ForgotPasswordRequestModelFromJson(json);

@override final  String email;

/// Create a copy of ForgotPasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ForgotPasswordRequestModelCopyWith<_ForgotPasswordRequestModel> get copyWith => __$ForgotPasswordRequestModelCopyWithImpl<_ForgotPasswordRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ForgotPasswordRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ForgotPasswordRequestModel&&(identical(other.email, email) || other.email == email));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString() {
  return 'ForgotPasswordRequestModel(email: $email)';
}


}

/// @nodoc
abstract mixin class _$ForgotPasswordRequestModelCopyWith<$Res> implements $ForgotPasswordRequestModelCopyWith<$Res> {
  factory _$ForgotPasswordRequestModelCopyWith(_ForgotPasswordRequestModel value, $Res Function(_ForgotPasswordRequestModel) _then) = __$ForgotPasswordRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String email
});




}
/// @nodoc
class __$ForgotPasswordRequestModelCopyWithImpl<$Res>
    implements _$ForgotPasswordRequestModelCopyWith<$Res> {
  __$ForgotPasswordRequestModelCopyWithImpl(this._self, this._then);

  final _ForgotPasswordRequestModel _self;
  final $Res Function(_ForgotPasswordRequestModel) _then;

/// Create a copy of ForgotPasswordRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,}) {
  return _then(_ForgotPasswordRequestModel(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
