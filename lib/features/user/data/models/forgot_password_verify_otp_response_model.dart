import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/forgot_password_verify_otp_response.dart';

part 'forgot_password_verify_otp_response_model.freezed.dart';
part 'forgot_password_verify_otp_response_model.g.dart';

@freezed
abstract class ForgotPasswordVerifyOtpResponseModel with _$ForgotPasswordVerifyOtpResponseModel {
  const factory ForgotPasswordVerifyOtpResponseModel({
    required bool verified,
    @JsonKey(name: 'verified_token') required String verifiedToken,
    required String message,
  }) = _ForgotPasswordVerifyOtpResponseModel;

  factory ForgotPasswordVerifyOtpResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordVerifyOtpResponseModelFromJson(json);
}

extension ForgotPasswordVerifyOtpResponseModelX on ForgotPasswordVerifyOtpResponseModel {
  ForgotPasswordVerifyOtpResponse toEntity() => ForgotPasswordVerifyOtpResponse(
    verified: verified,
    verifiedToken: verifiedToken,
    message: message,
  );
}
