// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'member_register_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MemberRegisterRequestModel {

 String get username; String get email; String get password;@JsonKey(name: 'confirm_password') String get confirmPassword;@JsonKey(name: 'first_name') String get firstName;@JsonKey(name: 'last_name') String get lastName; String get name;// Organization name
 String get phone;@JsonKey(name: 'identity_card_no') String get identityCardNo;@JsonKey(name: 'app_mas_member_type_id') int get appMasMemberTypeId;@JsonKey(name: 'app_mas_government_sector_id') int? get appMasGovernmentSectorId;@JsonKey(name: 'app_mas_government_sector_other') String? get appMasGovernmentSectorOther;@JsonKey(name: 'app_mas_ministry_id') int? get appMasMinistryId;@JsonKey(name: 'app_mas_ministry_other') String? get appMasMinistryOther;@JsonKey(name: 'app_mas_department_id') int? get appMasDepartmentId;@JsonKey(name: 'app_mas_department_other') String? get appMasDepartmentOther;@JsonKey(name: 'is_notification') String get isNotification; String get lang; String get status;@JsonKey(name: 'otp_token') String get otpToken;
/// Create a copy of MemberRegisterRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemberRegisterRequestModelCopyWith<MemberRegisterRequestModel> get copyWith => _$MemberRegisterRequestModelCopyWithImpl<MemberRegisterRequestModel>(this as MemberRegisterRequestModel, _$identity);

  /// Serializes this MemberRegisterRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterRequestModel&&(identical(other.username, username) || other.username == username)&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.confirmPassword, confirmPassword) || other.confirmPassword == confirmPassword)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.name, name) || other.name == name)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.identityCardNo, identityCardNo) || other.identityCardNo == identityCardNo)&&(identical(other.appMasMemberTypeId, appMasMemberTypeId) || other.appMasMemberTypeId == appMasMemberTypeId)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.appMasGovernmentSectorOther, appMasGovernmentSectorOther) || other.appMasGovernmentSectorOther == appMasGovernmentSectorOther)&&(identical(other.appMasMinistryId, appMasMinistryId) || other.appMasMinistryId == appMasMinistryId)&&(identical(other.appMasMinistryOther, appMasMinistryOther) || other.appMasMinistryOther == appMasMinistryOther)&&(identical(other.appMasDepartmentId, appMasDepartmentId) || other.appMasDepartmentId == appMasDepartmentId)&&(identical(other.appMasDepartmentOther, appMasDepartmentOther) || other.appMasDepartmentOther == appMasDepartmentOther)&&(identical(other.isNotification, isNotification) || other.isNotification == isNotification)&&(identical(other.lang, lang) || other.lang == lang)&&(identical(other.status, status) || other.status == status)&&(identical(other.otpToken, otpToken) || other.otpToken == otpToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,username,email,password,confirmPassword,firstName,lastName,name,phone,identityCardNo,appMasMemberTypeId,appMasGovernmentSectorId,appMasGovernmentSectorOther,appMasMinistryId,appMasMinistryOther,appMasDepartmentId,appMasDepartmentOther,isNotification,lang,status,otpToken]);

@override
String toString() {
  return 'MemberRegisterRequestModel(username: $username, email: $email, password: $password, confirmPassword: $confirmPassword, firstName: $firstName, lastName: $lastName, name: $name, phone: $phone, identityCardNo: $identityCardNo, appMasMemberTypeId: $appMasMemberTypeId, appMasGovernmentSectorId: $appMasGovernmentSectorId, appMasGovernmentSectorOther: $appMasGovernmentSectorOther, appMasMinistryId: $appMasMinistryId, appMasMinistryOther: $appMasMinistryOther, appMasDepartmentId: $appMasDepartmentId, appMasDepartmentOther: $appMasDepartmentOther, isNotification: $isNotification, lang: $lang, status: $status, otpToken: $otpToken)';
}


}

/// @nodoc
abstract mixin class $MemberRegisterRequestModelCopyWith<$Res>  {
  factory $MemberRegisterRequestModelCopyWith(MemberRegisterRequestModel value, $Res Function(MemberRegisterRequestModel) _then) = _$MemberRegisterRequestModelCopyWithImpl;
@useResult
$Res call({
 String username, String email, String password,@JsonKey(name: 'confirm_password') String confirmPassword,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName, String name, String phone,@JsonKey(name: 'identity_card_no') String identityCardNo,@JsonKey(name: 'app_mas_member_type_id') int appMasMemberTypeId,@JsonKey(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,@JsonKey(name: 'app_mas_government_sector_other') String? appMasGovernmentSectorOther,@JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,@JsonKey(name: 'app_mas_ministry_other') String? appMasMinistryOther,@JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,@JsonKey(name: 'app_mas_department_other') String? appMasDepartmentOther,@JsonKey(name: 'is_notification') String isNotification, String lang, String status,@JsonKey(name: 'otp_token') String otpToken
});




}
/// @nodoc
class _$MemberRegisterRequestModelCopyWithImpl<$Res>
    implements $MemberRegisterRequestModelCopyWith<$Res> {
  _$MemberRegisterRequestModelCopyWithImpl(this._self, this._then);

  final MemberRegisterRequestModel _self;
  final $Res Function(MemberRegisterRequestModel) _then;

/// Create a copy of MemberRegisterRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? username = null,Object? email = null,Object? password = null,Object? confirmPassword = null,Object? firstName = null,Object? lastName = null,Object? name = null,Object? phone = null,Object? identityCardNo = null,Object? appMasMemberTypeId = null,Object? appMasGovernmentSectorId = freezed,Object? appMasGovernmentSectorOther = freezed,Object? appMasMinistryId = freezed,Object? appMasMinistryOther = freezed,Object? appMasDepartmentId = freezed,Object? appMasDepartmentOther = freezed,Object? isNotification = null,Object? lang = null,Object? status = null,Object? otpToken = null,}) {
  return _then(_self.copyWith(
username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,confirmPassword: null == confirmPassword ? _self.confirmPassword : confirmPassword // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,identityCardNo: null == identityCardNo ? _self.identityCardNo : identityCardNo // ignore: cast_nullable_to_non_nullable
as String,appMasMemberTypeId: null == appMasMemberTypeId ? _self.appMasMemberTypeId : appMasMemberTypeId // ignore: cast_nullable_to_non_nullable
as int,appMasGovernmentSectorId: freezed == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int?,appMasGovernmentSectorOther: freezed == appMasGovernmentSectorOther ? _self.appMasGovernmentSectorOther : appMasGovernmentSectorOther // ignore: cast_nullable_to_non_nullable
as String?,appMasMinistryId: freezed == appMasMinistryId ? _self.appMasMinistryId : appMasMinistryId // ignore: cast_nullable_to_non_nullable
as int?,appMasMinistryOther: freezed == appMasMinistryOther ? _self.appMasMinistryOther : appMasMinistryOther // ignore: cast_nullable_to_non_nullable
as String?,appMasDepartmentId: freezed == appMasDepartmentId ? _self.appMasDepartmentId : appMasDepartmentId // ignore: cast_nullable_to_non_nullable
as int?,appMasDepartmentOther: freezed == appMasDepartmentOther ? _self.appMasDepartmentOther : appMasDepartmentOther // ignore: cast_nullable_to_non_nullable
as String?,isNotification: null == isNotification ? _self.isNotification : isNotification // ignore: cast_nullable_to_non_nullable
as String,lang: null == lang ? _self.lang : lang // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,otpToken: null == otpToken ? _self.otpToken : otpToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _MemberRegisterRequestModel implements MemberRegisterRequestModel {
  const _MemberRegisterRequestModel({required this.username, required this.email, required this.password, @JsonKey(name: 'confirm_password') required this.confirmPassword, @JsonKey(name: 'first_name') required this.firstName, @JsonKey(name: 'last_name') required this.lastName, required this.name, required this.phone, @JsonKey(name: 'identity_card_no') required this.identityCardNo, @JsonKey(name: 'app_mas_member_type_id') required this.appMasMemberTypeId, @JsonKey(name: 'app_mas_government_sector_id') this.appMasGovernmentSectorId, @JsonKey(name: 'app_mas_government_sector_other') this.appMasGovernmentSectorOther, @JsonKey(name: 'app_mas_ministry_id') this.appMasMinistryId, @JsonKey(name: 'app_mas_ministry_other') this.appMasMinistryOther, @JsonKey(name: 'app_mas_department_id') this.appMasDepartmentId, @JsonKey(name: 'app_mas_department_other') this.appMasDepartmentOther, @JsonKey(name: 'is_notification') this.isNotification = "1", this.lang = "th", this.status = "1", @JsonKey(name: 'otp_token') required this.otpToken});
  factory _MemberRegisterRequestModel.fromJson(Map<String, dynamic> json) => _$MemberRegisterRequestModelFromJson(json);

@override final  String username;
@override final  String email;
@override final  String password;
@override@JsonKey(name: 'confirm_password') final  String confirmPassword;
@override@JsonKey(name: 'first_name') final  String firstName;
@override@JsonKey(name: 'last_name') final  String lastName;
@override final  String name;
// Organization name
@override final  String phone;
@override@JsonKey(name: 'identity_card_no') final  String identityCardNo;
@override@JsonKey(name: 'app_mas_member_type_id') final  int appMasMemberTypeId;
@override@JsonKey(name: 'app_mas_government_sector_id') final  int? appMasGovernmentSectorId;
@override@JsonKey(name: 'app_mas_government_sector_other') final  String? appMasGovernmentSectorOther;
@override@JsonKey(name: 'app_mas_ministry_id') final  int? appMasMinistryId;
@override@JsonKey(name: 'app_mas_ministry_other') final  String? appMasMinistryOther;
@override@JsonKey(name: 'app_mas_department_id') final  int? appMasDepartmentId;
@override@JsonKey(name: 'app_mas_department_other') final  String? appMasDepartmentOther;
@override@JsonKey(name: 'is_notification') final  String isNotification;
@override@JsonKey() final  String lang;
@override@JsonKey() final  String status;
@override@JsonKey(name: 'otp_token') final  String otpToken;

/// Create a copy of MemberRegisterRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MemberRegisterRequestModelCopyWith<_MemberRegisterRequestModel> get copyWith => __$MemberRegisterRequestModelCopyWithImpl<_MemberRegisterRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MemberRegisterRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MemberRegisterRequestModel&&(identical(other.username, username) || other.username == username)&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.confirmPassword, confirmPassword) || other.confirmPassword == confirmPassword)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.name, name) || other.name == name)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.identityCardNo, identityCardNo) || other.identityCardNo == identityCardNo)&&(identical(other.appMasMemberTypeId, appMasMemberTypeId) || other.appMasMemberTypeId == appMasMemberTypeId)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.appMasGovernmentSectorOther, appMasGovernmentSectorOther) || other.appMasGovernmentSectorOther == appMasGovernmentSectorOther)&&(identical(other.appMasMinistryId, appMasMinistryId) || other.appMasMinistryId == appMasMinistryId)&&(identical(other.appMasMinistryOther, appMasMinistryOther) || other.appMasMinistryOther == appMasMinistryOther)&&(identical(other.appMasDepartmentId, appMasDepartmentId) || other.appMasDepartmentId == appMasDepartmentId)&&(identical(other.appMasDepartmentOther, appMasDepartmentOther) || other.appMasDepartmentOther == appMasDepartmentOther)&&(identical(other.isNotification, isNotification) || other.isNotification == isNotification)&&(identical(other.lang, lang) || other.lang == lang)&&(identical(other.status, status) || other.status == status)&&(identical(other.otpToken, otpToken) || other.otpToken == otpToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,username,email,password,confirmPassword,firstName,lastName,name,phone,identityCardNo,appMasMemberTypeId,appMasGovernmentSectorId,appMasGovernmentSectorOther,appMasMinistryId,appMasMinistryOther,appMasDepartmentId,appMasDepartmentOther,isNotification,lang,status,otpToken]);

@override
String toString() {
  return 'MemberRegisterRequestModel(username: $username, email: $email, password: $password, confirmPassword: $confirmPassword, firstName: $firstName, lastName: $lastName, name: $name, phone: $phone, identityCardNo: $identityCardNo, appMasMemberTypeId: $appMasMemberTypeId, appMasGovernmentSectorId: $appMasGovernmentSectorId, appMasGovernmentSectorOther: $appMasGovernmentSectorOther, appMasMinistryId: $appMasMinistryId, appMasMinistryOther: $appMasMinistryOther, appMasDepartmentId: $appMasDepartmentId, appMasDepartmentOther: $appMasDepartmentOther, isNotification: $isNotification, lang: $lang, status: $status, otpToken: $otpToken)';
}


}

/// @nodoc
abstract mixin class _$MemberRegisterRequestModelCopyWith<$Res> implements $MemberRegisterRequestModelCopyWith<$Res> {
  factory _$MemberRegisterRequestModelCopyWith(_MemberRegisterRequestModel value, $Res Function(_MemberRegisterRequestModel) _then) = __$MemberRegisterRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String username, String email, String password,@JsonKey(name: 'confirm_password') String confirmPassword,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName, String name, String phone,@JsonKey(name: 'identity_card_no') String identityCardNo,@JsonKey(name: 'app_mas_member_type_id') int appMasMemberTypeId,@JsonKey(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,@JsonKey(name: 'app_mas_government_sector_other') String? appMasGovernmentSectorOther,@JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,@JsonKey(name: 'app_mas_ministry_other') String? appMasMinistryOther,@JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,@JsonKey(name: 'app_mas_department_other') String? appMasDepartmentOther,@JsonKey(name: 'is_notification') String isNotification, String lang, String status,@JsonKey(name: 'otp_token') String otpToken
});




}
/// @nodoc
class __$MemberRegisterRequestModelCopyWithImpl<$Res>
    implements _$MemberRegisterRequestModelCopyWith<$Res> {
  __$MemberRegisterRequestModelCopyWithImpl(this._self, this._then);

  final _MemberRegisterRequestModel _self;
  final $Res Function(_MemberRegisterRequestModel) _then;

/// Create a copy of MemberRegisterRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? username = null,Object? email = null,Object? password = null,Object? confirmPassword = null,Object? firstName = null,Object? lastName = null,Object? name = null,Object? phone = null,Object? identityCardNo = null,Object? appMasMemberTypeId = null,Object? appMasGovernmentSectorId = freezed,Object? appMasGovernmentSectorOther = freezed,Object? appMasMinistryId = freezed,Object? appMasMinistryOther = freezed,Object? appMasDepartmentId = freezed,Object? appMasDepartmentOther = freezed,Object? isNotification = null,Object? lang = null,Object? status = null,Object? otpToken = null,}) {
  return _then(_MemberRegisterRequestModel(
username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,confirmPassword: null == confirmPassword ? _self.confirmPassword : confirmPassword // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,identityCardNo: null == identityCardNo ? _self.identityCardNo : identityCardNo // ignore: cast_nullable_to_non_nullable
as String,appMasMemberTypeId: null == appMasMemberTypeId ? _self.appMasMemberTypeId : appMasMemberTypeId // ignore: cast_nullable_to_non_nullable
as int,appMasGovernmentSectorId: freezed == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int?,appMasGovernmentSectorOther: freezed == appMasGovernmentSectorOther ? _self.appMasGovernmentSectorOther : appMasGovernmentSectorOther // ignore: cast_nullable_to_non_nullable
as String?,appMasMinistryId: freezed == appMasMinistryId ? _self.appMasMinistryId : appMasMinistryId // ignore: cast_nullable_to_non_nullable
as int?,appMasMinistryOther: freezed == appMasMinistryOther ? _self.appMasMinistryOther : appMasMinistryOther // ignore: cast_nullable_to_non_nullable
as String?,appMasDepartmentId: freezed == appMasDepartmentId ? _self.appMasDepartmentId : appMasDepartmentId // ignore: cast_nullable_to_non_nullable
as int?,appMasDepartmentOther: freezed == appMasDepartmentOther ? _self.appMasDepartmentOther : appMasDepartmentOther // ignore: cast_nullable_to_non_nullable
as String?,isNotification: null == isNotification ? _self.isNotification : isNotification // ignore: cast_nullable_to_non_nullable
as String,lang: null == lang ? _self.lang : lang // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,otpToken: null == otpToken ? _self.otpToken : otpToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
