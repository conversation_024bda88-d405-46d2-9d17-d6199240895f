// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_validation_error_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RegisterValidationErrorModel {

@JsonKey(name: 'username') List<String>? get username;@JsonKey(name: 'email') List<String>? get email;@JsonKey(name: 'phone') List<String>? get phone;
/// Create a copy of RegisterValidationErrorModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisterValidationErrorModelCopyWith<RegisterValidationErrorModel> get copyWith => _$RegisterValidationErrorModelCopyWithImpl<RegisterValidationErrorModel>(this as RegisterValidationErrorModel, _$identity);

  /// Serializes this RegisterValidationErrorModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterValidationErrorModel&&const DeepCollectionEquality().equals(other.username, username)&&const DeepCollectionEquality().equals(other.email, email)&&const DeepCollectionEquality().equals(other.phone, phone));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(username),const DeepCollectionEquality().hash(email),const DeepCollectionEquality().hash(phone));

@override
String toString() {
  return 'RegisterValidationErrorModel(username: $username, email: $email, phone: $phone)';
}


}

/// @nodoc
abstract mixin class $RegisterValidationErrorModelCopyWith<$Res>  {
  factory $RegisterValidationErrorModelCopyWith(RegisterValidationErrorModel value, $Res Function(RegisterValidationErrorModel) _then) = _$RegisterValidationErrorModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'username') List<String>? username,@JsonKey(name: 'email') List<String>? email,@JsonKey(name: 'phone') List<String>? phone
});




}
/// @nodoc
class _$RegisterValidationErrorModelCopyWithImpl<$Res>
    implements $RegisterValidationErrorModelCopyWith<$Res> {
  _$RegisterValidationErrorModelCopyWithImpl(this._self, this._then);

  final RegisterValidationErrorModel _self;
  final $Res Function(RegisterValidationErrorModel) _then;

/// Create a copy of RegisterValidationErrorModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? username = freezed,Object? email = freezed,Object? phone = freezed,}) {
  return _then(_self.copyWith(
username: freezed == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as List<String>?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as List<String>?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _RegisterValidationErrorModel implements RegisterValidationErrorModel {
  const _RegisterValidationErrorModel({@JsonKey(name: 'username') final  List<String>? username, @JsonKey(name: 'email') final  List<String>? email, @JsonKey(name: 'phone') final  List<String>? phone}): _username = username,_email = email,_phone = phone;
  factory _RegisterValidationErrorModel.fromJson(Map<String, dynamic> json) => _$RegisterValidationErrorModelFromJson(json);

 final  List<String>? _username;
@override@JsonKey(name: 'username') List<String>? get username {
  final value = _username;
  if (value == null) return null;
  if (_username is EqualUnmodifiableListView) return _username;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _email;
@override@JsonKey(name: 'email') List<String>? get email {
  final value = _email;
  if (value == null) return null;
  if (_email is EqualUnmodifiableListView) return _email;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _phone;
@override@JsonKey(name: 'phone') List<String>? get phone {
  final value = _phone;
  if (value == null) return null;
  if (_phone is EqualUnmodifiableListView) return _phone;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of RegisterValidationErrorModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisterValidationErrorModelCopyWith<_RegisterValidationErrorModel> get copyWith => __$RegisterValidationErrorModelCopyWithImpl<_RegisterValidationErrorModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RegisterValidationErrorModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisterValidationErrorModel&&const DeepCollectionEquality().equals(other._username, _username)&&const DeepCollectionEquality().equals(other._email, _email)&&const DeepCollectionEquality().equals(other._phone, _phone));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_username),const DeepCollectionEquality().hash(_email),const DeepCollectionEquality().hash(_phone));

@override
String toString() {
  return 'RegisterValidationErrorModel(username: $username, email: $email, phone: $phone)';
}


}

/// @nodoc
abstract mixin class _$RegisterValidationErrorModelCopyWith<$Res> implements $RegisterValidationErrorModelCopyWith<$Res> {
  factory _$RegisterValidationErrorModelCopyWith(_RegisterValidationErrorModel value, $Res Function(_RegisterValidationErrorModel) _then) = __$RegisterValidationErrorModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'username') List<String>? username,@JsonKey(name: 'email') List<String>? email,@JsonKey(name: 'phone') List<String>? phone
});




}
/// @nodoc
class __$RegisterValidationErrorModelCopyWithImpl<$Res>
    implements _$RegisterValidationErrorModelCopyWith<$Res> {
  __$RegisterValidationErrorModelCopyWithImpl(this._self, this._then);

  final _RegisterValidationErrorModel _self;
  final $Res Function(_RegisterValidationErrorModel) _then;

/// Create a copy of RegisterValidationErrorModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? username = freezed,Object? email = freezed,Object? phone = freezed,}) {
  return _then(_RegisterValidationErrorModel(
username: freezed == username ? _self._username : username // ignore: cast_nullable_to_non_nullable
as List<String>?,email: freezed == email ? _self._email : email // ignore: cast_nullable_to_non_nullable
as List<String>?,phone: freezed == phone ? _self._phone : phone // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}


}

// dart format on
