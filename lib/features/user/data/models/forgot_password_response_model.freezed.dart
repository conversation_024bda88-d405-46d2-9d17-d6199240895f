// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'forgot_password_response_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ForgotPasswordResponseModel {

 String get token;@JsonKey(name: 'ref_code') String get refCode;@JsonKey(name: 'expires_at') String get expiresAt; String get message;
/// Create a copy of ForgotPasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ForgotPasswordResponseModelCopyWith<ForgotPasswordResponseModel> get copyWith => _$ForgotPasswordResponseModelCopyWithImpl<ForgotPasswordResponseModel>(this as ForgotPasswordResponseModel, _$identity);

  /// Serializes this ForgotPasswordResponseModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ForgotPasswordResponseModel&&(identical(other.token, token) || other.token == token)&&(identical(other.refCode, refCode) || other.refCode == refCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,token,refCode,expiresAt,message);

@override
String toString() {
  return 'ForgotPasswordResponseModel(token: $token, refCode: $refCode, expiresAt: $expiresAt, message: $message)';
}


}

/// @nodoc
abstract mixin class $ForgotPasswordResponseModelCopyWith<$Res>  {
  factory $ForgotPasswordResponseModelCopyWith(ForgotPasswordResponseModel value, $Res Function(ForgotPasswordResponseModel) _then) = _$ForgotPasswordResponseModelCopyWithImpl;
@useResult
$Res call({
 String token,@JsonKey(name: 'ref_code') String refCode,@JsonKey(name: 'expires_at') String expiresAt, String message
});




}
/// @nodoc
class _$ForgotPasswordResponseModelCopyWithImpl<$Res>
    implements $ForgotPasswordResponseModelCopyWith<$Res> {
  _$ForgotPasswordResponseModelCopyWithImpl(this._self, this._then);

  final ForgotPasswordResponseModel _self;
  final $Res Function(ForgotPasswordResponseModel) _then;

/// Create a copy of ForgotPasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? token = null,Object? refCode = null,Object? expiresAt = null,Object? message = null,}) {
  return _then(_self.copyWith(
token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refCode: null == refCode ? _self.refCode : refCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ForgotPasswordResponseModel implements ForgotPasswordResponseModel {
  const _ForgotPasswordResponseModel({required this.token, @JsonKey(name: 'ref_code') required this.refCode, @JsonKey(name: 'expires_at') required this.expiresAt, required this.message});
  factory _ForgotPasswordResponseModel.fromJson(Map<String, dynamic> json) => _$ForgotPasswordResponseModelFromJson(json);

@override final  String token;
@override@JsonKey(name: 'ref_code') final  String refCode;
@override@JsonKey(name: 'expires_at') final  String expiresAt;
@override final  String message;

/// Create a copy of ForgotPasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ForgotPasswordResponseModelCopyWith<_ForgotPasswordResponseModel> get copyWith => __$ForgotPasswordResponseModelCopyWithImpl<_ForgotPasswordResponseModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ForgotPasswordResponseModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ForgotPasswordResponseModel&&(identical(other.token, token) || other.token == token)&&(identical(other.refCode, refCode) || other.refCode == refCode)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,token,refCode,expiresAt,message);

@override
String toString() {
  return 'ForgotPasswordResponseModel(token: $token, refCode: $refCode, expiresAt: $expiresAt, message: $message)';
}


}

/// @nodoc
abstract mixin class _$ForgotPasswordResponseModelCopyWith<$Res> implements $ForgotPasswordResponseModelCopyWith<$Res> {
  factory _$ForgotPasswordResponseModelCopyWith(_ForgotPasswordResponseModel value, $Res Function(_ForgotPasswordResponseModel) _then) = __$ForgotPasswordResponseModelCopyWithImpl;
@override @useResult
$Res call({
 String token,@JsonKey(name: 'ref_code') String refCode,@JsonKey(name: 'expires_at') String expiresAt, String message
});




}
/// @nodoc
class __$ForgotPasswordResponseModelCopyWithImpl<$Res>
    implements _$ForgotPasswordResponseModelCopyWith<$Res> {
  __$ForgotPasswordResponseModelCopyWithImpl(this._self, this._then);

  final _ForgotPasswordResponseModel _self;
  final $Res Function(_ForgotPasswordResponseModel) _then;

/// Create a copy of ForgotPasswordResponseModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? token = null,Object? refCode = null,Object? expiresAt = null,Object? message = null,}) {
  return _then(_ForgotPasswordResponseModel(
token: null == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String,refCode: null == refCode ? _self.refCode : refCode // ignore: cast_nullable_to_non_nullable
as String,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
