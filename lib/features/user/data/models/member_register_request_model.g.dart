// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member_register_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MemberRegisterRequestModel _$MemberRegisterRequestModelFromJson(
  Map<String, dynamic> json,
) => _MemberRegisterRequestModel(
  username: json['username'] as String,
  email: json['email'] as String,
  password: json['password'] as String,
  confirmPassword: json['confirm_password'] as String,
  firstName: json['first_name'] as String,
  lastName: json['last_name'] as String,
  name: json['name'] as String,
  phone: json['phone'] as String,
  identityCardNo: json['identity_card_no'] as String,
  appMasMemberTypeId: (json['app_mas_member_type_id'] as num).toInt(),
  appMasGovernmentSectorId:
      (json['app_mas_government_sector_id'] as num?)?.toInt(),
  appMasGovernmentSectorOther:
      json['app_mas_government_sector_other'] as String?,
  appMasMinistryId: (json['app_mas_ministry_id'] as num?)?.toInt(),
  appMasMinistryOther: json['app_mas_ministry_other'] as String?,
  appMasDepartmentId: (json['app_mas_department_id'] as num?)?.toInt(),
  appMasDepartmentOther: json['app_mas_department_other'] as String?,
  isNotification: json['is_notification'] as String? ?? "1",
  lang: json['lang'] as String? ?? "th",
  status: json['status'] as String? ?? "1",
  otpToken: json['otp_token'] as String,
);

Map<String, dynamic> _$MemberRegisterRequestModelToJson(
  _MemberRegisterRequestModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'email': instance.email,
  'password': instance.password,
  'confirm_password': instance.confirmPassword,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'name': instance.name,
  'phone': instance.phone,
  'identity_card_no': instance.identityCardNo,
  'app_mas_member_type_id': instance.appMasMemberTypeId,
  'app_mas_government_sector_id': instance.appMasGovernmentSectorId,
  'app_mas_government_sector_other': instance.appMasGovernmentSectorOther,
  'app_mas_ministry_id': instance.appMasMinistryId,
  'app_mas_ministry_other': instance.appMasMinistryOther,
  'app_mas_department_id': instance.appMasDepartmentId,
  'app_mas_department_other': instance.appMasDepartmentOther,
  'is_notification': instance.isNotification,
  'lang': instance.lang,
  'status': instance.status,
  'otp_token': instance.otpToken,
};
