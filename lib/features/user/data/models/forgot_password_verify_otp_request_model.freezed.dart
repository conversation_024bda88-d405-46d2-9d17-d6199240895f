// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'forgot_password_verify_otp_request_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ForgotPasswordVerifyOtpRequestModel {

 String get email; String get otp;@JsonKey(name: 'ref_code') String get refCode;@JsonKey(name: 'otp_token') String get otpToken;
/// Create a copy of ForgotPasswordVerifyOtpRequestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ForgotPasswordVerifyOtpRequestModelCopyWith<ForgotPasswordVerifyOtpRequestModel> get copyWith => _$ForgotPasswordVerifyOtpRequestModelCopyWithImpl<ForgotPasswordVerifyOtpRequestModel>(this as ForgotPasswordVerifyOtpRequestModel, _$identity);

  /// Serializes this ForgotPasswordVerifyOtpRequestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ForgotPasswordVerifyOtpRequestModel&&(identical(other.email, email) || other.email == email)&&(identical(other.otp, otp) || other.otp == otp)&&(identical(other.refCode, refCode) || other.refCode == refCode)&&(identical(other.otpToken, otpToken) || other.otpToken == otpToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,otp,refCode,otpToken);

@override
String toString() {
  return 'ForgotPasswordVerifyOtpRequestModel(email: $email, otp: $otp, refCode: $refCode, otpToken: $otpToken)';
}


}

/// @nodoc
abstract mixin class $ForgotPasswordVerifyOtpRequestModelCopyWith<$Res>  {
  factory $ForgotPasswordVerifyOtpRequestModelCopyWith(ForgotPasswordVerifyOtpRequestModel value, $Res Function(ForgotPasswordVerifyOtpRequestModel) _then) = _$ForgotPasswordVerifyOtpRequestModelCopyWithImpl;
@useResult
$Res call({
 String email, String otp,@JsonKey(name: 'ref_code') String refCode,@JsonKey(name: 'otp_token') String otpToken
});




}
/// @nodoc
class _$ForgotPasswordVerifyOtpRequestModelCopyWithImpl<$Res>
    implements $ForgotPasswordVerifyOtpRequestModelCopyWith<$Res> {
  _$ForgotPasswordVerifyOtpRequestModelCopyWithImpl(this._self, this._then);

  final ForgotPasswordVerifyOtpRequestModel _self;
  final $Res Function(ForgotPasswordVerifyOtpRequestModel) _then;

/// Create a copy of ForgotPasswordVerifyOtpRequestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? otp = null,Object? refCode = null,Object? otpToken = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,otp: null == otp ? _self.otp : otp // ignore: cast_nullable_to_non_nullable
as String,refCode: null == refCode ? _self.refCode : refCode // ignore: cast_nullable_to_non_nullable
as String,otpToken: null == otpToken ? _self.otpToken : otpToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ForgotPasswordVerifyOtpRequestModel implements ForgotPasswordVerifyOtpRequestModel {
  const _ForgotPasswordVerifyOtpRequestModel({required this.email, required this.otp, @JsonKey(name: 'ref_code') required this.refCode, @JsonKey(name: 'otp_token') required this.otpToken});
  factory _ForgotPasswordVerifyOtpRequestModel.fromJson(Map<String, dynamic> json) => _$ForgotPasswordVerifyOtpRequestModelFromJson(json);

@override final  String email;
@override final  String otp;
@override@JsonKey(name: 'ref_code') final  String refCode;
@override@JsonKey(name: 'otp_token') final  String otpToken;

/// Create a copy of ForgotPasswordVerifyOtpRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ForgotPasswordVerifyOtpRequestModelCopyWith<_ForgotPasswordVerifyOtpRequestModel> get copyWith => __$ForgotPasswordVerifyOtpRequestModelCopyWithImpl<_ForgotPasswordVerifyOtpRequestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ForgotPasswordVerifyOtpRequestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ForgotPasswordVerifyOtpRequestModel&&(identical(other.email, email) || other.email == email)&&(identical(other.otp, otp) || other.otp == otp)&&(identical(other.refCode, refCode) || other.refCode == refCode)&&(identical(other.otpToken, otpToken) || other.otpToken == otpToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,otp,refCode,otpToken);

@override
String toString() {
  return 'ForgotPasswordVerifyOtpRequestModel(email: $email, otp: $otp, refCode: $refCode, otpToken: $otpToken)';
}


}

/// @nodoc
abstract mixin class _$ForgotPasswordVerifyOtpRequestModelCopyWith<$Res> implements $ForgotPasswordVerifyOtpRequestModelCopyWith<$Res> {
  factory _$ForgotPasswordVerifyOtpRequestModelCopyWith(_ForgotPasswordVerifyOtpRequestModel value, $Res Function(_ForgotPasswordVerifyOtpRequestModel) _then) = __$ForgotPasswordVerifyOtpRequestModelCopyWithImpl;
@override @useResult
$Res call({
 String email, String otp,@JsonKey(name: 'ref_code') String refCode,@JsonKey(name: 'otp_token') String otpToken
});




}
/// @nodoc
class __$ForgotPasswordVerifyOtpRequestModelCopyWithImpl<$Res>
    implements _$ForgotPasswordVerifyOtpRequestModelCopyWith<$Res> {
  __$ForgotPasswordVerifyOtpRequestModelCopyWithImpl(this._self, this._then);

  final _ForgotPasswordVerifyOtpRequestModel _self;
  final $Res Function(_ForgotPasswordVerifyOtpRequestModel) _then;

/// Create a copy of ForgotPasswordVerifyOtpRequestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? otp = null,Object? refCode = null,Object? otpToken = null,}) {
  return _then(_ForgotPasswordVerifyOtpRequestModel(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,otp: null == otp ? _self.otp : otp // ignore: cast_nullable_to_non_nullable
as String,refCode: null == refCode ? _self.refCode : refCode // ignore: cast_nullable_to_non_nullable
as String,otpToken: null == otpToken ? _self.otpToken : otpToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
