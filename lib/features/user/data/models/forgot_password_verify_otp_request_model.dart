import 'package:freezed_annotation/freezed_annotation.dart';

part 'forgot_password_verify_otp_request_model.freezed.dart';
part 'forgot_password_verify_otp_request_model.g.dart';

@freezed
abstract class ForgotPasswordVerifyOtpRequestModel with _$ForgotPasswordVerifyOtpRequestModel {
  const factory ForgotPasswordVerifyOtpRequestModel({
    required String email,
    required String otp,
    @JsonKey(name: 'ref_code') required String refCode,
    @JsonKey(name: 'otp_token') required String otpToken,
  }) = _ForgotPasswordVerifyOtpRequestModel;

  factory ForgotPasswordVerifyOtpRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordVerifyOtpRequestModelFromJson(json);
}
