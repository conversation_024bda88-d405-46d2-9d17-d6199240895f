import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/session_info.dart';
import 'package:mcdc/core/enums/user_type.dart';

part 'session_info_model.freezed.dart';
part 'session_info_model.g.dart';

@freezed
abstract class SessionInfoModel with _$SessionInfoModel {
  const factory SessionInfoModel({
    @JsonKey(name: 'login_time') required DateTime loginTime,
    @JsonKey(
      name: 'user_type',
      fromJson: UserType.fromJson,
      toJson: UserType.toJson,
    )
    required UserType userType,
  }) = _SessionInfoModel;

  factory SessionInfoModel.fromJson(Map<String, dynamic> json) =>
      _$SessionInfoModelFromJson(json);
}

extension SessionInfoModelX on SessionInfoModel {
  SessionInfo toEntity() =>
      SessionInfo(loginTime: loginTime, userType: userType);
}
