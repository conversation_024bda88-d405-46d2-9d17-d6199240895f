import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/forgot_password_response.dart';

part 'forgot_password_response_model.freezed.dart';
part 'forgot_password_response_model.g.dart';

@freezed
abstract class ForgotPasswordResponseModel with _$ForgotPasswordResponseModel {
  const factory ForgotPasswordResponseModel({
    required String token,
    @Json<PERSON>ey(name: 'ref_code') required String refCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_at') required String expiresAt,
    required String message,
  }) = _ForgotPasswordResponseModel;

  factory ForgotPasswordResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordResponseModelFromJson(json);
}

extension ForgotPasswordResponseModelX on ForgotPasswordResponseModel {
  ForgotPasswordResponse toEntity() => ForgotPasswordResponse(
    token: token,
    refCode: refCode,
    expiresAt: DateTime.parse(expiresAt),
    message: message,
  );
}
