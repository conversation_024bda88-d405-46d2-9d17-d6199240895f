import 'package:equatable/equatable.dart';

/// Entity representing validation errors for login form
class LoginValidationError extends Equatable {
  const LoginValidationError({this.username, this.password});

  final List<String>? username;
  final List<String>? password;

  @override
  List<Object?> get props => [username, password];

  /// Check if there are any validation errors
  bool get hasErrors =>
      (username?.isNotEmpty ?? false) || (password?.isNotEmpty ?? false);

  bool get hasUsernameError => username?.isNotEmpty ?? false;
  bool get hasPasswordError => password?.isNotEmpty ?? false;

  /// Get the first username error message
  String? get firstUsernameError =>
      username?.isNotEmpty == true ? username!.first : null;

  /// Get the first password error message
  String? get firstPasswordError =>
      password?.isNotEmpty == true ? password!.first : null;
}
