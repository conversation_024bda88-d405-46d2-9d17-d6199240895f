import 'package:equatable/equatable.dart';

class ForgotPasswordResponse extends Equatable {
  final String token;
  final String refCode;
  final DateTime expiresAt;
  final String message;

  const ForgotPasswordResponse({
    required this.token,
    required this.refCode,
    required this.expiresAt,
    required this.message,
  });

  @override
  List<Object> get props => [token, refCode, expiresAt, message];
}
