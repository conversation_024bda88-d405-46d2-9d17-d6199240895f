import 'package:equatable/equatable.dart';

/// Domain entity for check ID card result
class CheckIdCardResult extends Equatable {
  final bool isDuplicate;
  final String? existingMemberId;

  const CheckIdCardResult({required this.isDuplicate, this.existingMemberId});

  @override
  List<Object?> get props => [isDuplicate, existingMemberId];

  CheckIdCardResult copyWith({bool? isDuplicate, String? existingMemberId}) {
    return CheckIdCardResult(
      isDuplicate: isDuplicate ?? this.isDuplicate,
      existingMemberId: existingMemberId ?? this.existingMemberId,
    );
  }
}
