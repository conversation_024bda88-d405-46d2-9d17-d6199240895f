import 'package:equatable/equatable.dart';

/// Entity representing a complete member registration request
class MemberRegisterRequest extends Equatable {
  final String username;
  final String email;
  final String password;
  final String confirmPassword;
  final String firstName;
  final String lastName;
  final String name; // Organization name
  final String phone;
  final String identityCardNo;
  final int appMasMemberTypeId;
  final int? appMasGovernmentSectorId;
  final String? appMasGovernmentSectorOther;
  final int? appMasMinistryId;
  final String? appMasMinistryOther;
  final int? appMasDepartmentId;
  final String? appMasDepartmentOther;
  final String isNotification; // Fixed value "1"
  final String lang; // Fixed value "th"
  final String status; // Fixed value "1"
  final String otpToken;

  const MemberRegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.firstName,
    required this.lastName,
    required this.name,
    required this.phone,
    required this.identityCardNo,
    required this.appMasMemberTypeId,
    this.appMasGovernmentSectorId,
    this.appMasGovernmentSectorOther,
    this.appMasMinistryId,
    this.appMasMinistryOther,
    this.appMasDepartmentId,
    this.appMasDepartmentOther,
    this.isNotification = "1",
    this.lang = "th",
    this.status = "1",
    required this.otpToken,
  });

  @override
  List<Object?> get props => [
        username,
        email,
        password,
        confirmPassword,
        firstName,
        lastName,
        name,
        phone,
        identityCardNo,
        appMasMemberTypeId,
        appMasGovernmentSectorId,
        appMasGovernmentSectorOther,
        appMasMinistryId,
        appMasMinistryOther,
        appMasDepartmentId,
        appMasDepartmentOther,
        isNotification,
        lang,
        status,
        otpToken,
      ];
}
