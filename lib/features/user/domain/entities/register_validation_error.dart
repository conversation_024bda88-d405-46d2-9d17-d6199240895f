import 'package:equatable/equatable.dart';

class RegisterValidationError extends Equatable {
  const RegisterValidationError({this.username, this.email, this.phone});

  final List<String>? username;
  final List<String>? email;
  final List<String>? phone;

  @override
  List<Object?> get props => [username, email, phone];

  bool get hasErrors =>
      (username?.isNotEmpty ?? false) ||
      (email?.isNotEmpty ?? false) ||
      (phone?.isNotEmpty ?? false);

  bool get hasUsernameError => username?.isNotEmpty ?? false;
  bool get hasEmailError => email?.isNotEmpty ?? false;
  bool get hasPhoneError => phone?.isNotEmpty ?? false;

  String? get firstUsernameError =>
      username?.isNotEmpty == true ? username!.first : null;
  String? get firstEmailError =>
      email?.isNotEmpty == true ? email!.first : null;
  String? get firstPhoneError =>
      phone?.isNotEmpty == true ? phone!.first : null;
}
