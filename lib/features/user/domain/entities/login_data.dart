import 'package:equatable/equatable.dart';
import 'user.dart';
import 'token.dart';
import 'session_info.dart';

/// Login data entity representing the complete login API response
class LoginData extends Equatable {
  final User user;
  final Token tokens;
  final SessionInfo? sessionInfo;

  const LoginData({required this.user, required this.tokens, this.sessionInfo});

  @override
  List<Object?> get props => [user, tokens, sessionInfo];

  LoginData copyWith({User? user, Token? tokens, SessionInfo? sessionInfo}) {
    return LoginData(
      user: user ?? this.user,
      tokens: tokens ?? this.tokens,
      sessionInfo: sessionInfo ?? this.sessionInfo,
    );
  }
}
