import 'package:equatable/equatable.dart';

/// Organization entity for government agencies, ministries, and departments
class Organization extends Equatable {
  final int id;
  final String name;
  final String displayName;
  final int? parentId;

  const Organization({
    required this.id,
    required this.name,
    required this.displayName,
    this.parentId,
  });

  @override
  List<Object?> get props => [id, name, displayName, parentId];

  Organization copyWith({
    int? id,
    String? name,
    String? displayName,
    int? parentId,
  }) {
    return Organization(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      parentId: parentId ?? this.parentId,
    );
  }
}
