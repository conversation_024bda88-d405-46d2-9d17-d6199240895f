import 'package:equatable/equatable.dart';
import 'login_data.dart';

/// Entity representing a member registration response
class MemberRegisterResponse extends Equatable {
  final bool status;
  final String? errorMessage;
  final String? errorCode;
  final String apiVersion;
  final Map<String, List<String>>? validationErrors;
  final LoginData? loginData; // Contains user data and tokens on success
  final String? message; // Success message from API

  const MemberRegisterResponse({
    required this.status,
    this.errorMessage,
    this.errorCode,
    required this.apiVersion,
    this.validationErrors,
    this.loginData,
    this.message,
  });

  /// Check if registration was successful
  bool get isSuccess => status && errorMessage == null && loginData != null;

  /// Check if there are validation errors
  bool get hasValidationErrors =>
      validationErrors != null && validationErrors!.isNotEmpty;

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    errorCode,
    apiVersion,
    validationErrors,
    loginData,
    message,
  ];
}
