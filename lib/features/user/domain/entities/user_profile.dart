import 'package:equatable/equatable.dart';

/// User profile entity for domain layer
class UserProfile extends Equatable {
  const UserProfile({
    required this.username,
    required this.firstName,
    required this.lastName,
    required this.idCardNumber,
    required this.email,
    required this.phoneNumber,
    required this.sectorType,
    required this.organizationName,
    this.websiteName,
  });

  final String username;
  final String firstName;
  final String lastName;
  final String idCardNumber;
  final String email;
  final String phoneNumber;
  final String sectorType;
  final String organizationName;
  final String? websiteName;

  UserProfile copyWith({
    String? username,
    String? firstName,
    String? lastName,
    String? idCardNumber,
    String? email,
    String? phoneNumber,
    String? sectorType,
    String? organizationName,
    String? websiteName,
  }) {
    return UserProfile(
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      idCardNumber: idCardNumber ?? this.idCardNumber,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      sectorType: sectorType ?? this.sectorType,
      organizationName: organizationName ?? this.organizationName,
      websiteName: websiteName ?? this.websiteName,
    );
  }

  @override
  List<Object?> get props => [
        username,
        firstName,
        lastName,
        idCardNumber,
        email,
        phoneNumber,
        sectorType,
        organizationName,
        websiteName,
      ];
}
