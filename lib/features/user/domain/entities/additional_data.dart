import 'package:equatable/equatable.dart';
import 'package:mcdc/features/master_data/domain/entities/department.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/ministry.dart';

/// Additional data entity for Step 3 registration
class AdditionalData extends Equatable {
  final String firstName;
  final String lastName;
  final MemberType memberType;
  final GovernmentSector? governmentAgency;
  final Ministry? ministry;
  final Department? department;
  final String organizationName;

  const AdditionalData({
    required this.firstName,
    required this.lastName,
    required this.memberType,
    this.governmentAgency,
    this.ministry,
    this.department,
    required this.organizationName,
  });

  @override
  List<Object?> get props => [
    firstName,
    lastName,
    memberType,
    governmentAgency,
    ministry,
    department,
    organizationName,
  ];

  AdditionalData copyWith({
    String? firstName,
    String? lastName,
    MemberType? memberType,
    GovernmentSector? governmentAgency,
    Ministry? ministry,
    Department? department,
    String? organizationName,
  }) {
    return AdditionalData(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      memberType: memberType ?? this.memberType,
      governmentAgency: governmentAgency ?? this.governmentAgency,
      ministry: ministry ?? this.ministry,
      department: department ?? this.department,
      organizationName: organizationName ?? this.organizationName,
    );
  }

  /// Check if all required fields are filled
  bool get isValid {
    return firstName.isNotEmpty &&
        lastName.isNotEmpty &&
        organizationName.isNotEmpty &&
        (memberType.id == 2 ||
            governmentAgency != null); // id: 2 is private sector
  }
}
