import 'package:equatable/equatable.dart';
import 'package:mcdc/core/enums/user_type.dart';

/// User entity representing both member and consultant users
class User extends Equatable {
  final int id;
  final String? name;
  final String? firstName;
  final String? lastName;
  final String email;
  final String? emailSecond;
  final String phone;
  final String? phoneSecond;
  final String? identityCardNo;
  final String username;
  // Member-specific fields
  final int? appMasMemberTypeId;
  final String? appMasMemberTypeDisplay;
  final int? appMasGovernmentSectorId;
  final String? appMasGovernmentSectorDisplay;
  final int? appMasMinistryId;
  final String? appMasMinistryDisplay;
  final int? appMasDepartmentId;
  final String? appMasDepartmentDisplay;
  final String? website;
  final DateTime? createDate;
  final String? status;
  final String? statusDisplay;
  // Consultant-specific fields
  final int? consultType;
  final String? consultTypeDisplay;
  final int? corporateTypeId;
  final String? corporateTypeDisplay;
  final String? makerName;
  final String? makerPhone;
  final String? makerEmail;
  final String? verify;
  final String? verifyDisplay;
  final double? score;
  final bool? isActiveMatching;
  // Common fields
  final String isNotification;
  final String lang;
  final UserType userType;

  const User({
    required this.id,
    required this.name,
    this.firstName,
    this.lastName,
    required this.email,
    this.emailSecond,
    required this.phone,
    this.phoneSecond,
    this.identityCardNo,
    required this.username,
    // Member-specific fields
    this.appMasMemberTypeId,
    this.appMasMemberTypeDisplay,
    this.appMasGovernmentSectorId,
    this.appMasGovernmentSectorDisplay,
    this.appMasMinistryId,
    this.appMasMinistryDisplay,
    this.appMasDepartmentId,
    this.appMasDepartmentDisplay,
    this.website,
    this.createDate,
    // Consultant-specific fields
    this.consultType,
    this.consultTypeDisplay,
    this.corporateTypeId,
    this.corporateTypeDisplay,
    this.makerName,
    this.makerPhone,
    this.makerEmail,
    this.verify,
    this.verifyDisplay,
    this.score,
    this.isActiveMatching,
    // Common fields
    required this.isNotification,
    this.status,
    this.statusDisplay,
    required this.lang,
    required this.userType,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    firstName,
    lastName,
    email,
    emailSecond,
    phone,
    phoneSecond,
    identityCardNo,
    username,
    appMasMemberTypeId,
    appMasMemberTypeDisplay,
    appMasGovernmentSectorId,
    appMasGovernmentSectorDisplay,
    appMasMinistryId,
    appMasMinistryDisplay,
    appMasDepartmentId,
    appMasDepartmentDisplay,
    website,
    createDate,
    consultType,
    consultTypeDisplay,
    corporateTypeId,
    corporateTypeDisplay,
    makerName,
    makerPhone,
    makerEmail,
    verify,
    verifyDisplay,
    score,
    isActiveMatching,
    isNotification,
    status,
    statusDisplay,
    lang,
    userType,
  ];

  User copyWith({
    int? id,
    String? name,
    String? firstName,
    String? lastName,
    String? email,
    String? emailSecond,
    String? phone,
    String? phoneSecond,
    String? identityCardNo,
    String? username,
    int? appMasMemberTypeId,
    String? appMasMemberTypeDisplay,
    int? appMasGovernmentSectorId,
    String? appMasGovernmentSectorDisplay,
    int? appMasMinistryId,
    String? appMasMinistryDisplay,
    int? appMasDepartmentId,
    String? appMasDepartmentDisplay,
    String? website,
    DateTime? createDate,
    int? consultType,
    String? consultTypeDisplay,
    int? corporateTypeId,
    String? corporateTypeDisplay,
    String? makerName,
    String? makerPhone,
    String? makerEmail,
    String? verify,
    String? verifyDisplay,
    double? score,
    bool? isActiveMatching,
    String? isNotification,
    String? status,
    String? statusDisplay,
    String? lang,
    UserType? userType,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      emailSecond: emailSecond ?? this.emailSecond,
      phone: phone ?? this.phone,
      phoneSecond: phoneSecond ?? this.phoneSecond,
      identityCardNo: identityCardNo ?? this.identityCardNo,
      username: username ?? this.username,
      appMasMemberTypeId: appMasMemberTypeId ?? this.appMasMemberTypeId,
      appMasMemberTypeDisplay:
          appMasMemberTypeDisplay ?? this.appMasMemberTypeDisplay,
      appMasGovernmentSectorId:
          appMasGovernmentSectorId ?? this.appMasGovernmentSectorId,
      appMasGovernmentSectorDisplay:
          appMasGovernmentSectorDisplay ?? this.appMasGovernmentSectorDisplay,
      appMasMinistryId: appMasMinistryId ?? this.appMasMinistryId,
      appMasMinistryDisplay:
          appMasMinistryDisplay ?? this.appMasMinistryDisplay,
      appMasDepartmentId: appMasDepartmentId ?? this.appMasDepartmentId,
      appMasDepartmentDisplay:
          appMasDepartmentDisplay ?? this.appMasDepartmentDisplay,
      website: website ?? this.website,
      createDate: createDate ?? this.createDate,
      consultType: consultType ?? this.consultType,
      consultTypeDisplay: consultTypeDisplay ?? this.consultTypeDisplay,
      corporateTypeId: corporateTypeId ?? this.corporateTypeId,
      corporateTypeDisplay: corporateTypeDisplay ?? this.corporateTypeDisplay,
      makerName: makerName ?? this.makerName,
      makerPhone: makerPhone ?? this.makerPhone,
      makerEmail: makerEmail ?? this.makerEmail,
      verify: verify ?? this.verify,
      verifyDisplay: verifyDisplay ?? this.verifyDisplay,
      score: score ?? this.score,
      isActiveMatching: isActiveMatching ?? this.isActiveMatching,
      isNotification: isNotification ?? this.isNotification,
      status: status ?? this.status,
      statusDisplay: statusDisplay ?? this.statusDisplay,
      lang: lang ?? this.lang,
      userType: userType ?? this.userType,
    );
  }
}
