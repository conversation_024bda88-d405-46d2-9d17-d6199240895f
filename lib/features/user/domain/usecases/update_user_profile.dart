import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/user_profile.dart';
import '../repositories/user_profile_repository.dart';

/// Parameters for updating user profile
class UpdateUserProfileParams {
  final String firstName;
  final String lastName;
  final String idCardNumber;
  final String email;
  final String phoneNumber;
  final String sectorType;
  final String organizationName;
  final String? websiteName;
  final String currentPassword;

  const UpdateUserProfileParams({
    required this.firstName,
    required this.lastName,
    required this.idCardNumber,
    required this.email,
    required this.phoneNumber,
    required this.sectorType,
    required this.organizationName,
    this.websiteName,
    required this.currentPassword,
  });
}

/// Use case for updating user profile
class UpdateUserProfile implements UseCase<UserProfile, UpdateUserProfileParams> {
  final UserProfileRepository repository;

  const UpdateUserProfile(this.repository);

  @override
  Future<Either<Failure, UserProfile>> call(UpdateUserProfileParams params) async {
    return await repository.updateUserProfile(
      firstName: params.firstName,
      lastName: params.lastName,
      idCardNumber: params.idCardNumber,
      email: params.email,
      phoneNumber: params.phoneNumber,
      sectorType: params.sectorType,
      organizationName: params.organizationName,
      websiteName: params.websiteName,
      currentPassword: params.currentPassword,
    );
  }
}
