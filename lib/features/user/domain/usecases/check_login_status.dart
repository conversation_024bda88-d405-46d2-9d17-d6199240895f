import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';

/// Use case for checking if user is currently logged in
class CheckLoginStatus extends UseCase<LoginData?, NoParams> {
  final AuthStorageDataSource _authStorage;

  CheckLoginStatus(this._authStorage);

  @override
  Future<Either<Failure, LoginData?>> call(NoParams params) async {
    try {
      final isLoggedIn = await _authStorage.isLoggedIn();
      
      if (isLoggedIn) {
        final loginData = await _authStorage.getLoginData();
        return Right(loginData);
      } else {
        return const Right(null);
      }
    } catch (e) {
      return Left(
        UnhandledFailure(message: 'Failed to check login status: ${e.toString()}'),
      );
    }
  }
}
