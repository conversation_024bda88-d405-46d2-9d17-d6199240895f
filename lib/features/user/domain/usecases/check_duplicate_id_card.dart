import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/domain/entities/check_id_card_result.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

/// Parameters for checking duplicate ID card
class CheckDuplicateIdCardParams extends Equatable {
  final String idCard;

  const CheckDuplicateIdCardParams({required this.idCard});

  @override
  List<Object> get props => [idCard];
}

/// Use case for checking if an ID card is already registered (duplicate)
class CheckDuplicateIdCard
    extends UseCase<CheckIdCardResult, CheckDuplicateIdCardParams> {
  final UserRepository _userRepository;

  CheckDuplicateIdCard(this._userRepository);

  @override
  Future<Either<Failure, CheckIdCardResult>> call(
    CheckDuplicateIdCardParams params,
  ) async {
    return await _userRepository.memberCheckIdCard(idCard: params.idCard);
  }
}
