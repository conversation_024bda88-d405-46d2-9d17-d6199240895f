import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';

/// Use case for logging out a member user
class MemberLogout extends UseCase<void, NoParams> {
  final AuthStorageDataSource _authStorage;

  MemberLogout(this._authStorage);

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    try {
      await _authStorage.clearAuthData();
      return const Right(null);
    } catch (e) {
      return Left(
        UnhandledFailure(message: 'Failed to logout: ${e.toString()}'),
      );
    }
  }
}
