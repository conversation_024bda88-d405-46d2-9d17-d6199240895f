import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/forgot_password_verify_otp_response.dart';
import '../repositories/user_repository.dart';

class ForgotPasswordVerifyOtp implements UseCase<ForgotPasswordVerifyOtpResponse, ForgotPasswordVerifyOtpParams> {
  final UserRepository repository;

  ForgotPasswordVerifyOtp(this.repository);

  @override
  Future<Either<Failure, ForgotPasswordVerifyOtpResponse>> call(ForgotPasswordVerifyOtpParams params) async {
    return await repository.forgotPasswordVerifyOtp(
      email: params.email,
      otp: params.otp,
      refCode: params.refCode,
      otpToken: params.otpToken,
    );
  }
}

class ForgotPasswordVerifyOtpParams {
  final String email;
  final String otp;
  final String refCode;
  final String otpToken;

  ForgotPasswordVerifyOtpParams({
    required this.email,
    required this.otp,
    required this.refCode,
    required this.otpToken,
  });
}
