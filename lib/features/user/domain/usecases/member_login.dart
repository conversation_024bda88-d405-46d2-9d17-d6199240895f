import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

class MemberLogin extends UseCase<LoginData, MemberLoginParams> {
  final UserRepository _userRepository;
  final AuthStorageDataSource _authStorage;

  MemberLogin(this._userRepository, this._authStorage);

  @override
  Future<Either<Failure, LoginData>> call(MemberLoginParams params) async {
    final result = await _userRepository.memberLogin(
      username: params.username,
      password: params.password,
    );

    // Save login data to secure storage if login is successful
    return result.fold((failure) => Left(failure), (loginData) async {
      try {
        await _authStorage.saveLoginData(loginData);
        return Right(loginData);
      } catch (e) {
        // If saving fails, still return success but log the error
        // The user is logged in, but data might not be persisted
        return Right(loginData);
      }
    });
  }
}

class MemberLoginParams extends Equatable {
  final String username;
  final String password;

  const MemberLoginParams({required this.username, required this.password});

  @override
  List<Object?> get props => [username, password];
}
