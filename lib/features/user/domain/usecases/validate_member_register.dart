import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';
import 'package:mcdc/features/user/domain/repositories/user_repository.dart';

/// Parameters for validating member registration
class ValidateMemberRegisterParams extends Equatable {
  final String username;
  final String email;
  final String phone;

  const ValidateMemberRegisterParams({
    required this.username,
    required this.email,
    required this.phone,
  });

  @override
  List<Object> get props => [username, email, phone];
}

/// Use case for validating member registration data
class ValidateMemberRegister
    extends UseCase<RegisterValidationError, ValidateMemberRegisterParams> {
  final UserRepository _userRepository;

  ValidateMemberRegister(this._userRepository);

  @override
  Future<Either<Failure, RegisterValidationError>> call(
    ValidateMemberRegisterParams params,
  ) async {
    return await _userRepository.validateMemberRegister(
      username: params.username,
      email: params.email,
      phone: params.phone,
    );
  }
}
