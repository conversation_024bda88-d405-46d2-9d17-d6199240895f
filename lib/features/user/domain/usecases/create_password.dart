import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/create_password_request.dart';
import '../entities/create_password_response.dart';
import '../repositories/user_repository.dart';

/// Parameters for creating password
class CreatePasswordParams {
  final String email;
  final String newPassword;
  final String confirmPassword;
  final String verifiedToken;

  const CreatePasswordParams({
    required this.email,
    required this.newPassword,
    required this.confirmPassword,
    required this.verifiedToken,
  });
}

/// Use case for creating new password
class CreatePassword
    implements UseCase<CreatePasswordResponse, CreatePasswordParams> {
  final UserRepository repository;

  const CreatePassword(this.repository);

  @override
  Future<Either<Failure, CreatePasswordResponse>> call(
    CreatePasswordParams params,
  ) async {
    final request = CreatePasswordRequest(
      email: params.email,
      newPassword: params.newPassword,
      confirmPassword: params.confirmPassword,
      verifiedToken: params.verifiedToken,
    );

    return await repository.createPassword(request: request);
  }
}
