import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/forgot_password_response.dart';
import '../repositories/user_repository.dart';

class ForgotPasswordRequest implements UseCase<ForgotPasswordResponse, ForgotPasswordRequestParams> {
  final UserRepository repository;

  ForgotPasswordRequest(this.repository);

  @override
  Future<Either<Failure, ForgotPasswordResponse>> call(ForgotPasswordRequestParams params) async {
    return await repository.forgotPasswordRequest(email: params.email);
  }
}

class ForgotPasswordRequestParams {
  final String email;

  ForgotPasswordRequestParams({required this.email});
}
