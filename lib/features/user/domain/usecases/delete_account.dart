import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../repositories/user_profile_repository.dart';

/// Parameters for deleting account
class DeleteAccountParams {
  final String password;

  const DeleteAccountParams({required this.password});
}

/// Use case for deleting user account
class DeleteAccount implements UseCase<void, DeleteAccountParams> {
  final UserProfileRepository repository;

  const DeleteAccount(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteAccountParams params) async {
    return await repository.deleteAccount(password: params.password);
  }
}
