import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import '../entities/user_profile.dart';

/// Repository interface for user profile operations
abstract class UserProfileRepository {
  /// Get current user profile
  Future<Either<Failure, UserProfile>> getUserProfile();

  /// Update user profile
  Future<Either<Failure, UserProfile>> updateUserProfile({
    required String firstName,
    required String lastName,
    required String idCardNumber,
    required String email,
    required String phoneNumber,
    required String sectorType,
    required String organizationName,
    String? websiteName,
    required String currentPassword,
  });

  /// Change user password
  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Delete user account
  Future<Either<Failure, void>> deleteAccount({required String password});
}
