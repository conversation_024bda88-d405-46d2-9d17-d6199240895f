import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_confirm.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_success.dart';
import 'package:mcdc/shared/presentation/widgets/loading/loading_overlay.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'bloc/edit_password_cubit.dart';
import 'bloc/edit_password_state.dart';

@RoutePage()
class EditPasswordPage extends StatelessWidget {
  const EditPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<EditPasswordCubit>(
      create: (context) => AppInjector.get<EditPasswordCubit>(),
      child: const _EditPasswordPageContent(),
    );
  }
}

class _EditPasswordPageContent extends StatefulWidget {
  const _EditPasswordPageContent();

  @override
  State<_EditPasswordPageContent> createState() => _EditPasswordPageState();
}

class _EditPasswordPageState extends State<_EditPasswordPageContent> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  late final TextEditingController _currentPasswordController;
  late final TextEditingController _newPasswordController;
  late final TextEditingController _confirmPasswordController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _currentPasswordController = TextEditingController();
    _newPasswordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.editPasswordPageTitle),
      body: SafeArea(
        child: BlocConsumer<EditPasswordCubit, EditPasswordState>(
          listener: (context, state) {
            if (state is EditPasswordSuccess) {
              _showSuccessDialog(l10n);
            } else if (state is EditPasswordError) {
              _showErrorSnackBar(state.message);
            }
          },
          builder: (context, state) {
            return LoadingOverlay(
              isLoading: state is EditPasswordLoading,
              child: _buildBody(l10n),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBody(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Set New Password Section
            _buildPasswordSection(l10n),
            SizedBox(height: 32.h),

            // Action Buttons
            _buildActionButtons(l10n),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.setNewPasswordSection,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),

        // Current Password field
        CustomTextField(
          controller: _currentPasswordController,
          label: l10n.currentPasswordLabel,
          hintText: l10n.currentPasswordHint,
          obscureText: true,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateCurrentPasswordRequired;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // New Password field
        CustomTextField(
          controller: _newPasswordController,
          label: l10n.newPasswordLabel,
          hintText: l10n.newPasswordHint,
          obscureText: true,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateNewPasswordRequired;
            }
            if (value!.length < 6) {
              return l10n.validateNewPasswordLength;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // Confirm New Password field
        CustomTextField(
          controller: _confirmPasswordController,
          label: l10n.confirmNewPasswordLabel,
          hintText: l10n.confirmNewPasswordHint,
          obscureText: true,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateConfirmNewPasswordRequired;
            }
            if (value != _newPasswordController.text) {
              return l10n.validateNewPasswordMismatch;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.cancel,
            onPressed: () => context.router.back(),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: PrimaryButton(
            text: l10n.saveButton,
            onPressed: () => _onSavePressed(l10n),
          ),
        ),
      ],
    );
  }

  void _onSavePressed(AppLocalizations l10n) async {
    if (_formKey.currentState?.validate() ?? false) {
      final confirmed = await _showConfirmationDialog(l10n);
      if (!confirmed) return;
      if (confirmed && mounted) {
        context.read<EditPasswordCubit>().changePassword(
          currentPassword: _currentPasswordController.text,
          newPassword: _newPasswordController.text,
        );
      }
    }
  }

  Future<bool> _showConfirmationDialog(AppLocalizations l10n) async {
    final confirmed = await DialogConfirm.show(
      context: context,
      title: l10n.confirmChangePassword,
      message: l10n.confirmChangePasswordMessage,
    );

    return confirmed ?? false;
  }

  void _showSuccessDialog(AppLocalizations l10n) {
    DialogSuccess.show(
      context: context,
      afterDismiss: () {
        context.read<EditPasswordCubit>().resetToInitial();
        context.router.back();
      },
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.critical),
    );
  }
}
