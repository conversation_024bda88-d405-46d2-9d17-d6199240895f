import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';

part 'member_register_state.freezed.dart';

@freezed
abstract class MemberRegisterState with _$MemberRegisterState {
  const factory MemberRegisterState.idle() = MemberRegisterIdle;

  const factory MemberRegisterState.loading() = MemberRegisterLoading;

  const factory MemberRegisterState.success({
    required LoginData loginData,
    String? message,
  }) = MemberRegisterSuccess;

  const factory MemberRegisterState.error({
    required String message,
    String? errorCode,
    Map<String, List<String>>? validationErrors,
  }) = MemberRegisterError;
}
