import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/check_login_status.dart';
import 'profile_header_state.dart';

/// Cubit for managing profile header functionality
class ProfileHeaderCubit extends Cubit<ProfileHeaderState> {
  final CheckLoginStatus _checkLoginStatus;

  ProfileHeaderCubit({required CheckLoginStatus checkLoginStatus})
    : _checkLoginStatus = checkLoginStatus,
      super(const ProfileHeaderState.initial());

  /// Load current user data from storage
  Future<void> loadUserData() async {
    emit(const ProfileHeaderState.loading());

    final result = await _checkLoginStatus(NoParams());

    result.fold(
      (failure) => emit(ProfileHeaderState.error(message: failure.message)),
      (loginData) {
        if (loginData != null) {
          emit(ProfileHeaderState.loaded(user: loginData.user));
        } else {
          emit(
            const ProfileHeaderState.error(
              message: 'No user data found. Please login again.',
            ),
          );
        }
      },
    );
  }

  /// Get user profile name based on user type and available data
  String getUserProfileName(User user) {
    // Primary: Use the name field which should contain organization name
    if (user.name != null && user.name!.isNotEmpty) {
      return user.name!;
    }

    // For members, try government sector or department display
    if (user.userType.isMember) {
      // Try department display first (most specific)
      if (user.appMasDepartmentDisplay != null &&
          user.appMasDepartmentDisplay!.isNotEmpty) {
        return user.appMasDepartmentDisplay!;
      }
      // Try ministry display
      if (user.appMasMinistryDisplay != null &&
          user.appMasMinistryDisplay!.isNotEmpty) {
        return user.appMasMinistryDisplay!;
      }
      // Try government sector display
      if (user.appMasGovernmentSectorDisplay != null &&
          user.appMasGovernmentSectorDisplay!.isNotEmpty) {
        return user.appMasGovernmentSectorDisplay!;
      }
    }

    // For consultants, try corporate type display
    if (user.userType.isConsultant) {
      if (user.corporateTypeDisplay != null &&
          user.corporateTypeDisplay!.isNotEmpty) {
        return user.corporateTypeDisplay!;
      }
    }

    // Final fallback - use username or email
    if (user.username.isNotEmpty) {
      return user.username;
    }

    return user.email;
  }

  /// Get user type description for display
  String getUserTypeDescription(User user) {
    if (user.userType.isMember) {
      // For members, show member type display (ภาครัฐ/ภาคเอกชน) if available
      if (user.appMasMemberTypeDisplay != null &&
          user.appMasMemberTypeDisplay!.isNotEmpty) {
        return user.appMasMemberTypeDisplay!;
      }
      return 'สมาชิก'; // Default member text in Thai
    } else if (user.userType.isConsultant) {
      // For consultants, show consultant type display if available
      if (user.consultTypeDisplay != null &&
          user.consultTypeDisplay!.isNotEmpty) {
        return user.consultTypeDisplay!;
      }
      return 'ที่ปรึกษา'; // Default consultant text in Thai
    }

    return user.userType.displayName;
  }
}
