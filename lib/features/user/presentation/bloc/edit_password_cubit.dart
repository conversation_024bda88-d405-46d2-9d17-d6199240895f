import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/change_password.dart';
import 'edit_password_state.dart';

/// Cubit for managing edit password state
class EditPasswordCubit extends Cubit<EditPasswordState> {
  final ChangePassword _changePassword;

  EditPasswordCubit({
    required ChangePassword changePassword,
  })  : _changePassword = changePassword,
        super(const EditPasswordInitial());

  /// Change user password
  void changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    emit(const EditPasswordLoading());

    final params = ChangePasswordParams(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );

    final result = await _changePassword(params);

    result.fold(
      (failure) => emit(EditPasswordError(message: failure.message)),
      (_) => emit(const EditPasswordSuccess()),
    );
  }

  /// Reset to initial state
  void resetToInitial() {
    emit(const EditPasswordInitial());
  }
}
