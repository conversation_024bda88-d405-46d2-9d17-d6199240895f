import 'dart:developer' as developer;

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/utils/validators.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';
import 'package:mcdc/features/user/domain/usecases/validate_member_register.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_state.dart';

class ValidateRegisterCubit extends Cubit<ValidateRegisterState> {
  final ValidateMemberRegister _validateMemberRegister;

  ValidateRegisterCubit({
    required ValidateMemberRegister validateMemberRegister,
  }) : _validateMemberRegister = validateMemberRegister,
       super(const ValidateRegisterState.idle());

  /// Validates registration form fields and checks for duplicates
  /// Returns error messages for each field if validation fails
  Future<void> validateRegistrationForm({
    required String username,
    required String email,
    required String phone,
    required String password,
    required String confirmPassword,
    AppLocalizations? l10n,
  }) async {
    // Don't reset state here - let the caller handle it if needed
    // This prevents the error text from disappearing during validation

    // First validate local form fields
    final localErrors = _validateLocalFields(
      username: username,
      email: email,
      phone: phone,
      password: password,
      confirmPassword: confirmPassword,
      l10n: l10n,
    );

    // If there are local validation errors, emit invalid state
    if (localErrors.hasErrors) {
      emit(ValidateRegisterState.invalid(localErrors));
      return;
    }

    // If local validation passes, check for duplicates via API
    emit(const ValidateRegisterState.loading());

    final result = await _validateMemberRegister(
      ValidateMemberRegisterParams(
        username: username,
        email: email,
        phone: phone,
      ),
    );

    return result.fold(
      (failure) {
        developer.log('ValidateRegisterCubit: ${failure.message}');
        // On API failure, consider form valid locally but show error
        emit(const ValidateRegisterState.valid());
      },
      (validationError) {
        developer.log('ValidateRegisterCubit: ${validationError.toString()}');
        if (validationError.hasErrors) {
          emit(ValidateRegisterState.invalid(validationError));
        } else {
          emit(const ValidateRegisterState.valid());
        }
      },
    );
  }

  /// Validates individual field and returns error message if invalid
  String? validateField({
    required String fieldName,
    required String value,
    String? confirmValue,
    AppLocalizations? l10n,
  }) {
    switch (fieldName) {
      case 'username':
        if (value.isEmpty) {
          return l10n?.validateUsernameRequired ?? 'กรุณาระบุ';
        }
        return Validators.validateUsername(value, l10n);
      case 'email':
        if (value.isEmpty) {
          return l10n?.validateEmailRequired ?? 'กรุณาระบุ';
        }
        return Validators.validateEmail(value, l10n);
      case 'phone':
        if (value.isEmpty) {
          return l10n?.validatePhoneRequired ?? 'กรุณาระบุ';
        }
        return Validators.validatePhone(value, l10n);
      case 'password':
        if (value.isEmpty) {
          return l10n?.validatePasswordRequired ?? 'กรุณาระบุ';
        }
        return Validators.validatePassword(value, l10n);
      case 'confirmPassword':
        if (value.isEmpty) {
          return l10n?.validateConfirmPasswordRequired ?? 'กรุณาระบุ';
        }
        if (value != confirmValue) {
          return l10n?.validatePasswordMismatch ?? 'รหัสผ่านไม่ตรงกัน';
        }
        return null;
      default:
        return null;
    }
  }

  /// Reset state to idle
  void reset() {
    emit(const ValidateRegisterState.idle());
  }

  /// Clear field errors for a specific field when user starts typing
  void clearFieldError(String fieldName) {
    if (state is ValidateRegisterInvalid) {
      final currentError = (state as ValidateRegisterInvalid).error;

      // Create new error object without the specified field error
      RegisterValidationError newError;
      switch (fieldName) {
        case 'username':
          newError = RegisterValidationError(
            username: null,
            email: currentError.email,
            phone: currentError.phone,
          );
          break;
        case 'email':
          newError = RegisterValidationError(
            username: currentError.username,
            email: null,
            phone: currentError.phone,
          );
          break;
        case 'phone':
          newError = RegisterValidationError(
            username: currentError.username,
            email: currentError.email,
            phone: null,
          );
          break;
        default:
          return;
      }

      // If no errors remain, reset to idle, otherwise update with new error
      if (!newError.hasErrors) {
        emit(const ValidateRegisterState.idle());
      } else {
        emit(ValidateRegisterState.invalid(newError));
      }
    }
  }

  /// Check if current state indicates an error that should be shown
  bool get hasError {
    return state is ValidateRegisterInvalid;
  }

  /// Get error message for a specific field from current state
  String? getFieldError(String fieldName, [AppLocalizations? l10n]) {
    if (state is ValidateRegisterInvalid) {
      final error = (state as ValidateRegisterInvalid).error;
      switch (fieldName) {
        case 'username':
          return error.firstUsernameError;
        case 'email':
          return error.firstEmailError;
        case 'phone':
          return error.firstPhoneError;
        default:
          return null;
      }
    }
    return null;
  }

  /// Check if form is valid (no errors and not loading)
  bool get isFormValid {
    return state is ValidateRegisterValid;
  }

  /// Check if validation is in progress
  bool get isLoading {
    return state is ValidateRegisterLoading;
  }

  /// Validates local form fields without API calls
  RegisterValidationError _validateLocalFields({
    required String username,
    required String email,
    required String phone,
    required String password,
    required String confirmPassword,
    AppLocalizations? l10n,
  }) {
    final List<String> usernameErrors = [];
    final List<String> emailErrors = [];
    final List<String> phoneErrors = [];

    // Validate username
    if (username.isEmpty) {
      usernameErrors.add(l10n?.validateUsernameRequired ?? 'กรุณาระบุ');
    } else {
      final usernameError = Validators.validateUsername(username, l10n);
      if (usernameError != null) {
        usernameErrors.add(usernameError);
      }
    }

    // Validate email
    if (email.isEmpty) {
      emailErrors.add(l10n?.validateEmailRequired ?? 'กรุณาระบุ');
    } else {
      final emailError = Validators.validateEmail(email, l10n);
      if (emailError != null) {
        emailErrors.add(emailError);
      }
    }

    // Validate phone
    if (phone.isEmpty) {
      phoneErrors.add(l10n?.validatePhoneRequired ?? 'กรุณาระบุ');
    } else {
      final phoneError = Validators.validatePhone(phone, l10n);
      if (phoneError != null) {
        phoneErrors.add(phoneError);
      }
    }

    // Note: Password validation is handled separately in the form
    // since it doesn't need API validation

    return RegisterValidationError(
      username: usernameErrors.isEmpty ? null : usernameErrors,
      email: emailErrors.isEmpty ? null : emailErrors,
      phone: phoneErrors.isEmpty ? null : phoneErrors,
    );
  }
}
