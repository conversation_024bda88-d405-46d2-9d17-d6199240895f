// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'additional_data_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AdditionalDataState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AdditionalDataState()';
}


}

/// @nodoc
class $AdditionalDataStateCopyWith<$Res>  {
$AdditionalDataStateCopyWith(AdditionalDataState _, $Res Function(AdditionalDataState) __);
}


/// @nodoc


class AdditionalDataInitial implements AdditionalDataState {
  const AdditionalDataInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AdditionalDataState.initial()';
}


}




/// @nodoc


class AdditionalDataLoading implements AdditionalDataState {
  const AdditionalDataLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AdditionalDataState.loading()';
}


}




/// @nodoc


class AdditionalDataLoaded implements AdditionalDataState {
  const AdditionalDataLoaded({required final  List<GovernmentSector> governmentAgencies, required final  List<Ministry> ministries, required final  List<Department> departments, this.selectedMemberType, this.selectedGovernmentAgency, this.selectedMinistry, this.selectedDepartment}): _governmentAgencies = governmentAgencies,_ministries = ministries,_departments = departments;
  

 final  List<GovernmentSector> _governmentAgencies;
 List<GovernmentSector> get governmentAgencies {
  if (_governmentAgencies is EqualUnmodifiableListView) return _governmentAgencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_governmentAgencies);
}

 final  List<Ministry> _ministries;
 List<Ministry> get ministries {
  if (_ministries is EqualUnmodifiableListView) return _ministries;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_ministries);
}

 final  List<Department> _departments;
 List<Department> get departments {
  if (_departments is EqualUnmodifiableListView) return _departments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_departments);
}

 final  MemberType? selectedMemberType;
 final  GovernmentSector? selectedGovernmentAgency;
 final  Ministry? selectedMinistry;
 final  Department? selectedDepartment;

/// Create a copy of AdditionalDataState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdditionalDataLoadedCopyWith<AdditionalDataLoaded> get copyWith => _$AdditionalDataLoadedCopyWithImpl<AdditionalDataLoaded>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataLoaded&&const DeepCollectionEquality().equals(other._governmentAgencies, _governmentAgencies)&&const DeepCollectionEquality().equals(other._ministries, _ministries)&&const DeepCollectionEquality().equals(other._departments, _departments)&&(identical(other.selectedMemberType, selectedMemberType) || other.selectedMemberType == selectedMemberType)&&(identical(other.selectedGovernmentAgency, selectedGovernmentAgency) || other.selectedGovernmentAgency == selectedGovernmentAgency)&&(identical(other.selectedMinistry, selectedMinistry) || other.selectedMinistry == selectedMinistry)&&(identical(other.selectedDepartment, selectedDepartment) || other.selectedDepartment == selectedDepartment));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_governmentAgencies),const DeepCollectionEquality().hash(_ministries),const DeepCollectionEquality().hash(_departments),selectedMemberType,selectedGovernmentAgency,selectedMinistry,selectedDepartment);

@override
String toString() {
  return 'AdditionalDataState.loaded(governmentAgencies: $governmentAgencies, ministries: $ministries, departments: $departments, selectedMemberType: $selectedMemberType, selectedGovernmentAgency: $selectedGovernmentAgency, selectedMinistry: $selectedMinistry, selectedDepartment: $selectedDepartment)';
}


}

/// @nodoc
abstract mixin class $AdditionalDataLoadedCopyWith<$Res> implements $AdditionalDataStateCopyWith<$Res> {
  factory $AdditionalDataLoadedCopyWith(AdditionalDataLoaded value, $Res Function(AdditionalDataLoaded) _then) = _$AdditionalDataLoadedCopyWithImpl;
@useResult
$Res call({
 List<GovernmentSector> governmentAgencies, List<Ministry> ministries, List<Department> departments, MemberType? selectedMemberType, GovernmentSector? selectedGovernmentAgency, Ministry? selectedMinistry, Department? selectedDepartment
});




}
/// @nodoc
class _$AdditionalDataLoadedCopyWithImpl<$Res>
    implements $AdditionalDataLoadedCopyWith<$Res> {
  _$AdditionalDataLoadedCopyWithImpl(this._self, this._then);

  final AdditionalDataLoaded _self;
  final $Res Function(AdditionalDataLoaded) _then;

/// Create a copy of AdditionalDataState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? governmentAgencies = null,Object? ministries = null,Object? departments = null,Object? selectedMemberType = freezed,Object? selectedGovernmentAgency = freezed,Object? selectedMinistry = freezed,Object? selectedDepartment = freezed,}) {
  return _then(AdditionalDataLoaded(
governmentAgencies: null == governmentAgencies ? _self._governmentAgencies : governmentAgencies // ignore: cast_nullable_to_non_nullable
as List<GovernmentSector>,ministries: null == ministries ? _self._ministries : ministries // ignore: cast_nullable_to_non_nullable
as List<Ministry>,departments: null == departments ? _self._departments : departments // ignore: cast_nullable_to_non_nullable
as List<Department>,selectedMemberType: freezed == selectedMemberType ? _self.selectedMemberType : selectedMemberType // ignore: cast_nullable_to_non_nullable
as MemberType?,selectedGovernmentAgency: freezed == selectedGovernmentAgency ? _self.selectedGovernmentAgency : selectedGovernmentAgency // ignore: cast_nullable_to_non_nullable
as GovernmentSector?,selectedMinistry: freezed == selectedMinistry ? _self.selectedMinistry : selectedMinistry // ignore: cast_nullable_to_non_nullable
as Ministry?,selectedDepartment: freezed == selectedDepartment ? _self.selectedDepartment : selectedDepartment // ignore: cast_nullable_to_non_nullable
as Department?,
  ));
}


}

/// @nodoc


class AdditionalDataError implements AdditionalDataState {
  const AdditionalDataError({required this.message});
  

 final  String message;

/// Create a copy of AdditionalDataState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdditionalDataErrorCopyWith<AdditionalDataError> get copyWith => _$AdditionalDataErrorCopyWithImpl<AdditionalDataError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'AdditionalDataState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $AdditionalDataErrorCopyWith<$Res> implements $AdditionalDataStateCopyWith<$Res> {
  factory $AdditionalDataErrorCopyWith(AdditionalDataError value, $Res Function(AdditionalDataError) _then) = _$AdditionalDataErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$AdditionalDataErrorCopyWithImpl<$Res>
    implements $AdditionalDataErrorCopyWith<$Res> {
  _$AdditionalDataErrorCopyWithImpl(this._self, this._then);

  final AdditionalDataError _self;
  final $Res Function(AdditionalDataError) _then;

/// Create a copy of AdditionalDataState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(AdditionalDataError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class AdditionalDataValidationError implements AdditionalDataState {
  const AdditionalDataValidationError({this.firstNameError, this.lastNameError, this.sectorTypeError, this.governmentSectorError, this.departmentNameError});
  

 final  String? firstNameError;
 final  String? lastNameError;
 final  String? sectorTypeError;
 final  String? governmentSectorError;
 final  String? departmentNameError;

/// Create a copy of AdditionalDataState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AdditionalDataValidationErrorCopyWith<AdditionalDataValidationError> get copyWith => _$AdditionalDataValidationErrorCopyWithImpl<AdditionalDataValidationError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataValidationError&&(identical(other.firstNameError, firstNameError) || other.firstNameError == firstNameError)&&(identical(other.lastNameError, lastNameError) || other.lastNameError == lastNameError)&&(identical(other.sectorTypeError, sectorTypeError) || other.sectorTypeError == sectorTypeError)&&(identical(other.governmentSectorError, governmentSectorError) || other.governmentSectorError == governmentSectorError)&&(identical(other.departmentNameError, departmentNameError) || other.departmentNameError == departmentNameError));
}


@override
int get hashCode => Object.hash(runtimeType,firstNameError,lastNameError,sectorTypeError,governmentSectorError,departmentNameError);

@override
String toString() {
  return 'AdditionalDataState.validationError(firstNameError: $firstNameError, lastNameError: $lastNameError, sectorTypeError: $sectorTypeError, governmentSectorError: $governmentSectorError, departmentNameError: $departmentNameError)';
}


}

/// @nodoc
abstract mixin class $AdditionalDataValidationErrorCopyWith<$Res> implements $AdditionalDataStateCopyWith<$Res> {
  factory $AdditionalDataValidationErrorCopyWith(AdditionalDataValidationError value, $Res Function(AdditionalDataValidationError) _then) = _$AdditionalDataValidationErrorCopyWithImpl;
@useResult
$Res call({
 String? firstNameError, String? lastNameError, String? sectorTypeError, String? governmentSectorError, String? departmentNameError
});




}
/// @nodoc
class _$AdditionalDataValidationErrorCopyWithImpl<$Res>
    implements $AdditionalDataValidationErrorCopyWith<$Res> {
  _$AdditionalDataValidationErrorCopyWithImpl(this._self, this._then);

  final AdditionalDataValidationError _self;
  final $Res Function(AdditionalDataValidationError) _then;

/// Create a copy of AdditionalDataState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? firstNameError = freezed,Object? lastNameError = freezed,Object? sectorTypeError = freezed,Object? governmentSectorError = freezed,Object? departmentNameError = freezed,}) {
  return _then(AdditionalDataValidationError(
firstNameError: freezed == firstNameError ? _self.firstNameError : firstNameError // ignore: cast_nullable_to_non_nullable
as String?,lastNameError: freezed == lastNameError ? _self.lastNameError : lastNameError // ignore: cast_nullable_to_non_nullable
as String?,sectorTypeError: freezed == sectorTypeError ? _self.sectorTypeError : sectorTypeError // ignore: cast_nullable_to_non_nullable
as String?,governmentSectorError: freezed == governmentSectorError ? _self.governmentSectorError : governmentSectorError // ignore: cast_nullable_to_non_nullable
as String?,departmentNameError: freezed == departmentNameError ? _self.departmentNameError : departmentNameError // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class AdditionalDataValid implements AdditionalDataState {
  const AdditionalDataValid();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AdditionalDataValid);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AdditionalDataState.valid()';
}


}




// dart format on
