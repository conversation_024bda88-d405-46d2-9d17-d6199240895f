import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/delete_account.dart';
import 'delete_account_state.dart';

/// Cubit for managing delete account state
class DeleteAccountCubit extends Cubit<DeleteAccountState> {
  final DeleteAccount _deleteAccount;

  DeleteAccountCubit({required DeleteAccount deleteAccount})
    : _deleteAccount = deleteAccount,
      super(const DeleteAccountInitial());

  /// Delete user account
  void deleteAccount({required String password}) async {
    emit(const DeleteAccountLoading());

    final params = DeleteAccountParams(password: password);

    final result = await _deleteAccount(params);

    result.fold(
      (failure) => emit(DeleteAccountError(message: failure.message)),
      (_) => emit(const DeleteAccountSuccess()),
    );
  }

  /// Reset to initial state
  void resetToInitial() {
    emit(const DeleteAccountInitial());
  }
}
