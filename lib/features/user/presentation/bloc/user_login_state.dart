import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';

part 'user_login_state.freezed.dart';

@freezed
sealed class UserLoginState with _$UserLoginState {
  const factory UserLoginState.idle() = _Idle;
  const factory UserLoginState.loading() = _Loading;
  const factory UserLoginState.success(LoginData loginData) = _Success;
  const factory UserLoginState.showError(String message) = _ShowError;
  const factory UserLoginState.showFallback(String message) = _ShowFallback;
}
