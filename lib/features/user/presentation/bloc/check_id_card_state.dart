import 'package:freezed_annotation/freezed_annotation.dart';

part 'check_id_card_state.freezed.dart';

@freezed
class CheckIdCardState with _$CheckIdCardState {
  const factory CheckIdCardState.idle() = CheckIdCardIdle;
  const factory CheckIdCardState.loading() = CheckIdCardLoading;
  const factory CheckIdCardState.valid() = CheckIdCardValid;
  const factory CheckIdCardState.invalid(String errorMessage) =
      CheckIdCardInvalid;
}
