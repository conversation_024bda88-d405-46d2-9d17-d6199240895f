import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../../domain/usecases/get_user_profile.dart';
import '../../domain/usecases/update_user_profile.dart';
import 'edit_profile_state.dart';

/// Cubit for managing edit profile functionality
class EditProfileCubit extends Cubit<EditProfileState> {
  final GetUserProfile getUserProfile;
  final UpdateUserProfile updateUserProfile;

  EditProfileCubit({
    required this.getUserProfile,
    required this.updateUserProfile,
  }) : super(const EditProfileInitial());

  /// Load user profile data
  void loadProfile() async {
    emit(const EditProfileLoading());

    final result = await getUserProfile(NoParams());

    result.fold(
      (failure) => emit(EditProfileError(message: failure.message)),
      (profile) => emit(EditProfileLoaded(profile: profile)),
    );
  }

  /// Update user profile
  void updateProfile({
    required String firstName,
    required String lastName,
    required String idCardNumber,
    required String email,
    required String phoneNumber,
    required String sectorType,
    required String organizationName,
    String? websiteName,
    required String currentPassword,
  }) async {
    if (state is! EditProfileLoaded) return;

    emit(const EditProfileUpdating());

    final params = UpdateUserProfileParams(
      firstName: firstName,
      lastName: lastName,
      idCardNumber: idCardNumber,
      email: email,
      phoneNumber: phoneNumber,
      sectorType: sectorType,
      organizationName: organizationName,
      websiteName: websiteName?.isEmpty == true ? null : websiteName,
      currentPassword: currentPassword,
    );

    final result = await updateUserProfile(params);

    result.fold(
      (failure) => emit(EditProfileError(message: failure.message)),
      (updatedProfile) =>
          emit(EditProfileUpdateSuccess(updatedProfile: updatedProfile)),
    );
  }

  /// Reset to loaded state after successful update
  void resetToLoaded() {
    if (state is EditProfileUpdateSuccess) {
      final profile = (state as EditProfileUpdateSuccess).updatedProfile;
      emit(EditProfileLoaded(profile: profile));
    }
  }
}
