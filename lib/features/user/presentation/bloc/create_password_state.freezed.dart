// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_password_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CreatePasswordState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreatePasswordState()';
}


}

/// @nodoc
class $CreatePasswordStateCopyWith<$Res>  {
$CreatePasswordStateCopyWith(CreatePasswordState _, $Res Function(CreatePasswordState) __);
}


/// @nodoc


class CreatePasswordInitial implements CreatePasswordState {
  const CreatePasswordInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreatePasswordState.initial()';
}


}




/// @nodoc


class CreatePasswordLoading implements CreatePasswordState {
  const CreatePasswordLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreatePasswordState.loading()';
}


}




/// @nodoc


class CreatePasswordSuccess implements CreatePasswordState {
  const CreatePasswordSuccess({required this.message});
  

 final  String message;

/// Create a copy of CreatePasswordState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreatePasswordSuccessCopyWith<CreatePasswordSuccess> get copyWith => _$CreatePasswordSuccessCopyWithImpl<CreatePasswordSuccess>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordSuccess&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'CreatePasswordState.success(message: $message)';
}


}

/// @nodoc
abstract mixin class $CreatePasswordSuccessCopyWith<$Res> implements $CreatePasswordStateCopyWith<$Res> {
  factory $CreatePasswordSuccessCopyWith(CreatePasswordSuccess value, $Res Function(CreatePasswordSuccess) _then) = _$CreatePasswordSuccessCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$CreatePasswordSuccessCopyWithImpl<$Res>
    implements $CreatePasswordSuccessCopyWith<$Res> {
  _$CreatePasswordSuccessCopyWithImpl(this._self, this._then);

  final CreatePasswordSuccess _self;
  final $Res Function(CreatePasswordSuccess) _then;

/// Create a copy of CreatePasswordState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(CreatePasswordSuccess(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class CreatePasswordError implements CreatePasswordState {
  const CreatePasswordError({required this.message});
  

 final  String message;

/// Create a copy of CreatePasswordState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreatePasswordErrorCopyWith<CreatePasswordError> get copyWith => _$CreatePasswordErrorCopyWithImpl<CreatePasswordError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePasswordError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'CreatePasswordState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $CreatePasswordErrorCopyWith<$Res> implements $CreatePasswordStateCopyWith<$Res> {
  factory $CreatePasswordErrorCopyWith(CreatePasswordError value, $Res Function(CreatePasswordError) _then) = _$CreatePasswordErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$CreatePasswordErrorCopyWithImpl<$Res>
    implements $CreatePasswordErrorCopyWith<$Res> {
  _$CreatePasswordErrorCopyWithImpl(this._self, this._then);

  final CreatePasswordError _self;
  final $Res Function(CreatePasswordError) _then;

/// Create a copy of CreatePasswordState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(CreatePasswordError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
