import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/login_data.dart';

part 'auth_status_state.freezed.dart';

@freezed
sealed class AuthStatusState with _$AuthStatusState {
  const factory AuthStatusState.initial() = Initial;
  const factory AuthStatusState.checking() = Checking;
  const factory AuthStatusState.authenticated(LoginData loginData) =
      Authenticated;
  const factory AuthStatusState.unauthenticated() = Unauthenticated;
  const factory AuthStatusState.error(String message) = Error;
}
