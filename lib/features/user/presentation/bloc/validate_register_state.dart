import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/domain/entities/register_validation_error.dart';

part 'validate_register_state.freezed.dart';

@freezed
sealed class ValidateRegisterState with _$ValidateRegisterState {
  const factory ValidateRegisterState.idle() = ValidateRegisterIdle;
  const factory ValidateRegisterState.loading() = ValidateRegisterLoading;
  const factory ValidateRegisterState.valid() = ValidateRegisterValid;
  const factory ValidateRegisterState.invalid(RegisterValidationError error) =
      ValidateRegisterInvalid;
}
