import 'dart:developer' as developer;

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/features/user/domain/entities/member_register_request.dart';
import 'package:mcdc/features/user/domain/usecases/member_register.dart';
import 'package:mcdc/features/user/presentation/bloc/member_register_state.dart';

class MemberRegisterCubit extends Cubit<MemberRegisterState> {
  final MemberRegister _memberRegister;

  MemberRegisterCubit({required MemberRegister memberRegister})
    : _memberRegister = memberRegister,
      super(const MemberRegisterState.idle());

  /// Register a new member with complete form data
  Future<void> registerMember({required MemberRegisterRequest request}) async {
    emit(const MemberRegisterState.loading());

    final result = await _memberRegister(
      MemberRegisterParams(request: request),
    );

    result.fold(
      (failure) {
        developer.log('MemberRegisterCubit: ${failure.message}');
        emit(MemberRegisterState.error(message: failure.message));
      },
      (response) {
        developer.log(
          'MemberRegisterCubit: Registration response - ${response.toString()}',
        );

        if (response.isSuccess && response.loginData != null) {
          emit(
            MemberRegisterState.success(
              loginData: response.loginData!,
              message: response.message,
            ),
          );
        } else {
          // Handle validation errors or other errors
          emit(
            MemberRegisterState.error(
              message: response.errorMessage ?? 'Registration failed',
              errorCode: response.errorCode,
              validationErrors: response.validationErrors,
            ),
          );
        }
      },
    );
  }

  /// Reset the cubit state
  void reset() {
    emit(const MemberRegisterState.idle());
  }

  /// Check if there are validation errors for a specific field
  String? getFieldError(String fieldName) {
    final currentState = state;
    if (currentState is MemberRegisterError &&
        currentState.validationErrors != null) {
      final errors = currentState.validationErrors![fieldName];
      return errors?.isNotEmpty == true ? errors!.first : null;
    }
    return null;
  }

  /// Check if the cubit has any errors
  bool get hasError => state is MemberRegisterError;

  /// Get the current error message
  String? get errorMessage {
    final currentState = state;
    if (currentState is MemberRegisterError) {
      return currentState.message;
    }
    return null;
  }
}
