import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../domain/usecases/forgot_password_request.dart';
import 'forgot_password_state.dart';

class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  final ForgotPasswordRequest _forgotPasswordRequest;

  // Business logic data
  String _email = '';
  String? _emailError;

  ForgotPasswordCubit({required ForgotPasswordRequest forgotPasswordRequest})
    : _forgotPasswordRequest = forgotPasswordRequest,
      super(const ForgotPasswordState.initial());

  // Getters for accessing business logic data
  String get email => _email;
  String? get emailError => _emailError;

  void updateEmail(String email) {
    _email = email;
    _emailError = null; // Clear error when user types
  }

  Future<void> requestPasswordReset(String email, AppLocalizations l10n) async {
    _email = email;
    _emailError = null;

    emit(const ForgotPasswordState.loading());

    final result = await _forgotPasswordRequest(
      ForgotPasswordRequestParams(email: email),
    );

    result.fold(
      (failure) {
        // Check if it's a validation error for email
        if (failure.message.contains('ไม่พบอีเมลนี้ในระบบ') ||
            failure.message.contains('email') ||
            failure.message.contains('Email not found')) {
          _emailError = l10n.forgotPasswordEmailNotFound;
          emit(
            ForgotPasswordState.error(
              message: failure.message,
              emailError: l10n.forgotPasswordEmailNotFound,
            ),
          );
        } else {
          emit(ForgotPasswordState.error(message: failure.message));
        }
      },
      (response) {
        emit(
          ForgotPasswordState.success(
            token: response.token,
            refCode: response.refCode,
            message: response.message,
          ),
        );
      },
    );
  }

  String? getEmailErrorMessage() {
    return _emailError;
  }
}
