import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/utils/validators.dart';
import '../../domain/usecases/create_password.dart';
import 'create_password_state.dart';

/// Cubit for managing create password state
class CreatePasswordCubit extends Cubit<CreatePasswordState> {
  final CreatePassword _createPassword;

  // Form field values
  String _password = '';
  String _confirmPassword = '';

  // Field error messages
  String? _passwordError;
  String? _confirmPasswordError;

  CreatePasswordCubit({required CreatePassword createPassword})
    : _createPassword = createPassword,
      super(const CreatePasswordInitial());

  /// Update password field
  void updatePassword(String password) {
    _password = password;
    _passwordError = null; // Clear error when user types
  }

  /// Update confirm password field
  void updateConfirmPassword(String confirmPassword) {
    _confirmPassword = confirmPassword;
    _confirmPasswordError = null; // Clear error when user types
  }

  /// Validate password field
  String? validatePassword(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.validateNewPasswordRequired;
    }
    return Validators.validatePassword(value, l10n);
  }

  /// Validate confirm password field
  String? validateConfirmPassword(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.validateConfirmNewPasswordRequired;
    }
    if (value != _password) {
      return l10n.validateNewPasswordMismatch;
    }
    return null;
  }

  /// Get field error for password
  String? getPasswordError() => _passwordError;

  /// Get field error for confirm password
  String? getConfirmPasswordError() => _confirmPasswordError;

  /// Clear field errors
  void clearErrors() {
    _passwordError = null;
    _confirmPasswordError = null;
  }

  /// Create new password
  Future<void> createNewPassword({
    required String email,
    required String newPassword,
    required String confirmPassword,
    required String verifiedToken,
    required AppLocalizations l10n,
  }) async {
    // Clear previous errors
    clearErrors();

    // Validate fields
    final passwordValidation = validatePassword(newPassword, l10n);
    final confirmPasswordValidation = validateConfirmPassword(
      confirmPassword,
      l10n,
    );

    if (passwordValidation != null || confirmPasswordValidation != null) {
      _passwordError = passwordValidation;
      _confirmPasswordError = confirmPasswordValidation;
      emit(const CreatePasswordError(message: 'Validation failed'));
      return;
    }

    emit(const CreatePasswordLoading());

    final params = CreatePasswordParams(
      email: email,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      verifiedToken: verifiedToken,
    );

    final result = await _createPassword(params);

    result.fold(
      (failure) => emit(CreatePasswordError(message: failure.message)),
      (response) => emit(
        CreatePasswordSuccess(
          message: response.message ?? l10n.createPasswordSuccess,
        ),
      ),
    );
  }
}
