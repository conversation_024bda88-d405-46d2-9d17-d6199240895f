import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_password_state.freezed.dart';

@freezed
abstract class CreatePasswordState with _$CreatePasswordState {
  const factory CreatePasswordState.initial() = CreatePasswordInitial;
  const factory CreatePasswordState.loading() = CreatePasswordLoading;
  const factory CreatePasswordState.success({
    required String message,
  }) = CreatePasswordSuccess;
  const factory CreatePasswordState.error({
    required String message,
  }) = CreatePasswordError;
}
