import 'dart:developer' as developer;

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/utils/validators.dart';
import 'package:mcdc/features/user/domain/usecases/check_duplicate_id_card.dart';
import 'package:mcdc/features/user/presentation/bloc/check_id_card_state.dart';

class CheckIdCardCubit extends Cubit<CheckIdCardState> {
  final CheckDuplicateIdCard _checkDuplicateIdCard;

  CheckIdCardCubit({required CheckDuplicateIdCard checkDuplicateIdCard})
    : _checkDuplicateIdCard = checkDuplicateIdCard,
      super(const CheckIdCardState.idle());

  /// Validates ID card format and checks for duplicates
  /// Returns error message if validation fails, null if successful
  Future<void> validateAndCheckIdCard(
    String idCard, [
    AppLocalizations? l10n,
  ]) async {
    // First validate the format and checksum
    final validationError = Validators.validateThaiIdCard(idCard, l10n);

    if (validationError != null) {
      emit(CheckIdCardState.invalid(validationError));
    }

    // If format is valid, check for duplicates
    emit(const CheckIdCardState.loading());

    final result = await _checkDuplicateIdCard(
      CheckDuplicateIdCardParams(idCard: idCard),
    );

    return result.fold(
      (failure) {
        developer.log('CheckIdCardCubit: ${failure.message}');
        emit(CheckIdCardState.invalid(l10n?.error ?? ''));
      },
      (checkResult) {
        if (checkResult.isDuplicate) {
          final duplicateMessage =
              l10n?.validateIdCardDuplicate ?? 'ข้อมูลนี้มีอยู่ในระบบแล้ว';
          emit(CheckIdCardState.invalid(duplicateMessage));
        } else {
          emit(const CheckIdCardState.valid());
        }
      },
    );
  }

  /// Reset state to idle
  void reset() {
    emit(const CheckIdCardState.idle());
  }

  /// Check if current state indicates an error that should be shown
  bool get hasError {
    return state is CheckIdCardInvalid;
  }

  /// Get error message for current state
  String? getErrorMessage([AppLocalizations? l10n]) {
    if (state is CheckIdCardInvalid) {
      return (state as CheckIdCardInvalid).errorMessage;
    }
    return null;
  }
}
