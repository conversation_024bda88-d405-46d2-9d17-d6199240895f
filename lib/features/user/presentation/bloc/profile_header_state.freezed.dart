// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_header_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProfileHeaderState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileHeaderState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProfileHeaderState()';
}


}

/// @nodoc
class $ProfileHeaderStateCopyWith<$Res>  {
$ProfileHeaderStateCopyWith(ProfileHeaderState _, $Res Function(ProfileHeaderState) __);
}


/// @nodoc


class ProfileHeaderInitial implements ProfileHeaderState {
  const ProfileHeaderInitial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileHeaderInitial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProfileHeaderState.initial()';
}


}




/// @nodoc


class ProfileHeaderLoading implements ProfileHeaderState {
  const ProfileHeaderLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileHeaderLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProfileHeaderState.loading()';
}


}




/// @nodoc


class ProfileHeaderLoaded implements ProfileHeaderState {
  const ProfileHeaderLoaded({required this.user});
  

 final  User user;

/// Create a copy of ProfileHeaderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileHeaderLoadedCopyWith<ProfileHeaderLoaded> get copyWith => _$ProfileHeaderLoadedCopyWithImpl<ProfileHeaderLoaded>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileHeaderLoaded&&(identical(other.user, user) || other.user == user));
}


@override
int get hashCode => Object.hash(runtimeType,user);

@override
String toString() {
  return 'ProfileHeaderState.loaded(user: $user)';
}


}

/// @nodoc
abstract mixin class $ProfileHeaderLoadedCopyWith<$Res> implements $ProfileHeaderStateCopyWith<$Res> {
  factory $ProfileHeaderLoadedCopyWith(ProfileHeaderLoaded value, $Res Function(ProfileHeaderLoaded) _then) = _$ProfileHeaderLoadedCopyWithImpl;
@useResult
$Res call({
 User user
});




}
/// @nodoc
class _$ProfileHeaderLoadedCopyWithImpl<$Res>
    implements $ProfileHeaderLoadedCopyWith<$Res> {
  _$ProfileHeaderLoadedCopyWithImpl(this._self, this._then);

  final ProfileHeaderLoaded _self;
  final $Res Function(ProfileHeaderLoaded) _then;

/// Create a copy of ProfileHeaderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? user = null,}) {
  return _then(ProfileHeaderLoaded(
user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,
  ));
}


}

/// @nodoc


class ProfileHeaderError implements ProfileHeaderState {
  const ProfileHeaderError({required this.message});
  

 final  String message;

/// Create a copy of ProfileHeaderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfileHeaderErrorCopyWith<ProfileHeaderError> get copyWith => _$ProfileHeaderErrorCopyWithImpl<ProfileHeaderError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfileHeaderError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'ProfileHeaderState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $ProfileHeaderErrorCopyWith<$Res> implements $ProfileHeaderStateCopyWith<$Res> {
  factory $ProfileHeaderErrorCopyWith(ProfileHeaderError value, $Res Function(ProfileHeaderError) _then) = _$ProfileHeaderErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$ProfileHeaderErrorCopyWithImpl<$Res>
    implements $ProfileHeaderErrorCopyWith<$Res> {
  _$ProfileHeaderErrorCopyWithImpl(this._self, this._then);

  final ProfileHeaderError _self;
  final $Res Function(ProfileHeaderError) _then;

/// Create a copy of ProfileHeaderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(ProfileHeaderError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
