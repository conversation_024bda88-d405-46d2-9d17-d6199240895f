import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/domain/usecases/member_logout.dart';
import 'package:mcdc/features/user/presentation/bloc/user_logout_state.dart';

/// Cubit for managing user logout functionality
class UserLogoutCubit extends Cubit<UserLogoutState> {
  final MemberLogout _memberLogout;

  UserLogoutCubit({required MemberLogout memberLogout})
    : _memberLogout = memberLogout,
      super(const UserLogoutState.idle());

  /// Logout the current user
  Future<void> logout() async {
    emit(const UserLogoutState.loading());

    final result = await _memberLogout(NoParams());

    result.fold(
      (failure) => emit(UserLogoutState.error(failure.message)),
      (_) => emit(const UserLogoutState.success()),
    );
  }

  /// Reset to idle state
  void resetToIdle() {
    emit(const UserLogoutState.idle());
  }
}
