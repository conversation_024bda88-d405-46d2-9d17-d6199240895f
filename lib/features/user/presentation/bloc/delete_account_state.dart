import 'package:equatable/equatable.dart';

/// Base state for delete account
abstract class DeleteAccountState extends Equatable {
  const DeleteAccountState();

  @override
  List<Object> get props => [];
}

/// Initial state
class DeleteAccountInitial extends DeleteAccountState {
  const DeleteAccountInitial();
}

/// Loading state
class DeleteAccountLoading extends DeleteAccountState {
  const DeleteAccountLoading();
}

/// Success state
class DeleteAccountSuccess extends DeleteAccountState {
  const DeleteAccountSuccess();
}

/// Error state
class DeleteAccountError extends DeleteAccountState {
  final String message;

  const DeleteAccountError({required this.message});

  @override
  List<Object> get props => [message];
}
