import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/core/enums/user_type.dart';
import '../../domain/usecases/member_login.dart';
import '../../domain/usecases/consultant_login.dart';
import 'user_login_state.dart';

/// Cubit for managing user login functionality
class UserLoginCubit extends Cubit<UserLoginState> {
  final MemberLogin _memberLogin;
  final ConsultantLogin _consultantLogin;

  UserLoginCubit({
    required MemberLogin memberLogin,
    required ConsultantLogin consultantLogin,
  }) : _memberLogin = memberLogin,
       _consultantLogin = consultantLogin,
       super(const UserLoginState.idle());

  /// Login with username and password
  Future<void> login({
    required String username,
    required String password,
    required UserType userType,
  }) async {
    // Validate inputs
    if (username.trim().isEmpty || password.trim().isEmpty) {
      emit(const UserLoginState.showError('กรุณากรอกชื่อผู้ใช้และรหัสผ่าน'));
      return;
    }

    emit(const UserLoginState.loading());

    // Choose the appropriate login use case based on user type
    if (userType == UserType.member) {
      final params = MemberLoginParams(
        username: username.trim(),
        password: password.trim(),
      );

      final result = await _memberLogin(params);

      result.fold(
        (failure) => emit(UserLoginState.showError(failure.message)),
        (loginData) => emit(UserLoginState.success(loginData)),
      );
    } else if (userType == UserType.consultant) {
      final params = ConsultantLoginParams(
        username: username.trim(),
        password: password.trim(),
      );

      final result = await _consultantLogin(params);

      result.fold(
        (failure) => emit(UserLoginState.showError(failure.message)),
        (loginData) => emit(UserLoginState.success(loginData)),
      );
    } else {
      emit(const UserLoginState.showError('ประเภทผู้ใช้ไม่ถูกต้อง'));
    }
  }

  /// Reset to idle state
  void resetToIdle() {
    emit(const UserLoginState.idle());
  }

  /// Clear error state
  void clearError() {
    final stateType = state.runtimeType.toString();
    if (stateType == '_ShowError' || stateType == '_ShowFallback') {
      emit(const UserLoginState.idle());
    }
  }

  /// Check if current state is loading
  bool get isLoading => state.runtimeType.toString() == '_Loading';

  /// Check if current state is error
  bool get isError {
    final stateType = state.runtimeType.toString();
    return stateType == '_ShowError' || stateType == '_ShowFallback';
  }

  /// Get error message if current state is error
  String? get errorMessage {
    final stateType = state.runtimeType.toString();
    if (stateType == '_ShowError' || stateType == '_ShowFallback') {
      // Extract message from state string using regex for more reliable parsing
      final stateString = state.toString();
      final messageMatch = RegExp(r'message: (.+)\)').firstMatch(stateString);
      return messageMatch?.group(1);
    }
    return null;
  }
}
