// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'member_register_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MemberRegisterState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MemberRegisterState()';
}


}

/// @nodoc
class $MemberRegisterStateCopyWith<$Res>  {
$MemberRegisterStateCopyWith(MemberRegisterState _, $Res Function(MemberRegisterState) __);
}


/// @nodoc


class MemberRegisterIdle implements MemberRegisterState {
  const MemberRegisterIdle();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterIdle);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MemberRegisterState.idle()';
}


}




/// @nodoc


class MemberRegisterLoading implements MemberRegisterState {
  const MemberRegisterLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MemberRegisterState.loading()';
}


}




/// @nodoc


class MemberRegisterSuccess implements MemberRegisterState {
  const MemberRegisterSuccess({required this.loginData, this.message});
  

 final  LoginData loginData;
 final  String? message;

/// Create a copy of MemberRegisterState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemberRegisterSuccessCopyWith<MemberRegisterSuccess> get copyWith => _$MemberRegisterSuccessCopyWithImpl<MemberRegisterSuccess>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterSuccess&&(identical(other.loginData, loginData) || other.loginData == loginData)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,loginData,message);

@override
String toString() {
  return 'MemberRegisterState.success(loginData: $loginData, message: $message)';
}


}

/// @nodoc
abstract mixin class $MemberRegisterSuccessCopyWith<$Res> implements $MemberRegisterStateCopyWith<$Res> {
  factory $MemberRegisterSuccessCopyWith(MemberRegisterSuccess value, $Res Function(MemberRegisterSuccess) _then) = _$MemberRegisterSuccessCopyWithImpl;
@useResult
$Res call({
 LoginData loginData, String? message
});




}
/// @nodoc
class _$MemberRegisterSuccessCopyWithImpl<$Res>
    implements $MemberRegisterSuccessCopyWith<$Res> {
  _$MemberRegisterSuccessCopyWithImpl(this._self, this._then);

  final MemberRegisterSuccess _self;
  final $Res Function(MemberRegisterSuccess) _then;

/// Create a copy of MemberRegisterState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? loginData = null,Object? message = freezed,}) {
  return _then(MemberRegisterSuccess(
loginData: null == loginData ? _self.loginData : loginData // ignore: cast_nullable_to_non_nullable
as LoginData,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class MemberRegisterError implements MemberRegisterState {
  const MemberRegisterError({required this.message, this.errorCode, final  Map<String, List<String>>? validationErrors}): _validationErrors = validationErrors;
  

 final  String message;
 final  String? errorCode;
 final  Map<String, List<String>>? _validationErrors;
 Map<String, List<String>>? get validationErrors {
  final value = _validationErrors;
  if (value == null) return null;
  if (_validationErrors is EqualUnmodifiableMapView) return _validationErrors;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of MemberRegisterState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MemberRegisterErrorCopyWith<MemberRegisterError> get copyWith => _$MemberRegisterErrorCopyWithImpl<MemberRegisterError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MemberRegisterError&&(identical(other.message, message) || other.message == message)&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode)&&const DeepCollectionEquality().equals(other._validationErrors, _validationErrors));
}


@override
int get hashCode => Object.hash(runtimeType,message,errorCode,const DeepCollectionEquality().hash(_validationErrors));

@override
String toString() {
  return 'MemberRegisterState.error(message: $message, errorCode: $errorCode, validationErrors: $validationErrors)';
}


}

/// @nodoc
abstract mixin class $MemberRegisterErrorCopyWith<$Res> implements $MemberRegisterStateCopyWith<$Res> {
  factory $MemberRegisterErrorCopyWith(MemberRegisterError value, $Res Function(MemberRegisterError) _then) = _$MemberRegisterErrorCopyWithImpl;
@useResult
$Res call({
 String message, String? errorCode, Map<String, List<String>>? validationErrors
});




}
/// @nodoc
class _$MemberRegisterErrorCopyWithImpl<$Res>
    implements $MemberRegisterErrorCopyWith<$Res> {
  _$MemberRegisterErrorCopyWithImpl(this._self, this._then);

  final MemberRegisterError _self;
  final $Res Function(MemberRegisterError) _then;

/// Create a copy of MemberRegisterState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,Object? errorCode = freezed,Object? validationErrors = freezed,}) {
  return _then(MemberRegisterError(
message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,validationErrors: freezed == validationErrors ? _self._validationErrors : validationErrors // ignore: cast_nullable_to_non_nullable
as Map<String, List<String>>?,
  ));
}


}

// dart format on
