import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/master_data/domain/entities/department.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/ministry.dart';

part 'additional_data_state.freezed.dart';

@freezed
class AdditionalDataState with _$AdditionalDataState {
  const factory AdditionalDataState.initial() = AdditionalDataInitial;

  const factory AdditionalDataState.loading() = AdditionalDataLoading;

  const factory AdditionalDataState.loaded({
    required List<GovernmentSector> governmentAgencies,
    required List<Ministry> ministries,
    required List<Department> departments,
    MemberType? selectedMemberType,
    GovernmentSector? selectedGovernmentAgency,
    Ministry? selectedMinistry,
    Department? selectedDepartment,
  }) = AdditionalDataLoaded;

  const factory AdditionalDataState.error({required String message}) =
      AdditionalDataError;

  const factory AdditionalDataState.validationError({
    String? firstNameError,
    String? lastNameError,
    String? sectorTypeError,
    String? governmentSectorError,
    String? departmentNameError,
  }) = AdditionalDataValidationError;

  const factory AdditionalDataState.valid() = AdditionalDataValid;
}
