import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/utils/validators.dart';
import 'package:mcdc/features/user/domain/entities/login_validation_error.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_login_state.dart';

/// Cubit for managing login form validation
class ValidateLoginCubit extends Cubit<ValidateLoginState> {
  ValidateLoginCubit() : super(const ValidateLoginIdle());

  /// Validates login form fields (username and password)
  /// Only validates required fields as per requirements
  void validateLoginForm({
    required String username,
    required String password,
    required AppLocalizations l10n,
  }) {
    // Validate local form fields
    final errors = _validateLocalFields(
      username: username,
      password: password,
      l10n: l10n,
    );

    // Emit appropriate state based on validation results
    if (errors.hasErrors) {
      emit(ValidateLoginInvalid(errors));
    } else {
      emit(const ValidateLoginValid());
    }
  }

  /// Validates individual field and returns error message if invalid
  String? validateField({
    required String fieldName,
    required String value,
    required AppLocalizations l10n,
  }) {
    switch (fieldName) {
      case 'username':
        return Validators.validateRequired(value, l10n);
      case 'password':
        return Validators.validateRequired(value, l10n);
      default:
        return null;
    }
  }

  /// Reset state to idle
  void reset() {
    emit(const ValidateLoginIdle());
  }

  /// Clear field errors for a specific field when user starts typing
  void clearFieldError(String fieldName) {
    if (state is ValidateLoginInvalid) {
      final currentError = (state as ValidateLoginInvalid).error;

      // Create new error object without the specified field error
      LoginValidationError newError;
      switch (fieldName) {
        case 'username':
          newError = LoginValidationError(
            username: null,
            password: currentError.password,
          );
          break;
        case 'password':
          newError = LoginValidationError(
            username: currentError.username,
            password: null,
          );
          break;
        default:
          return;
      }

      // If no errors remain, reset to idle, otherwise update with new error
      if (!newError.hasErrors) {
        emit(const ValidateLoginIdle());
      } else {
        emit(ValidateLoginInvalid(newError));
      }
    }
  }

  /// Check if current state indicates an error that should be shown
  bool get hasError {
    return state is ValidateLoginInvalid;
  }

  /// Get error message for a specific field from current state
  String? getFieldError(String fieldName, [AppLocalizations? l10n]) {
    if (state is ValidateLoginInvalid) {
      final error = (state as ValidateLoginInvalid).error;
      switch (fieldName) {
        case 'username':
          return error.firstUsernameError;
        case 'password':
          return error.firstPasswordError;
        default:
          return null;
      }
    }
    return null;
  }

  /// Check if form is valid (no errors and not loading)
  bool get isFormValid {
    return state is ValidateLoginValid;
  }

  /// Check if validation is in progress
  bool get isLoading {
    return state is ValidateLoginLoading;
  }

  /// Validates local form fields (only required validation)
  LoginValidationError _validateLocalFields({
    required String username,
    required String password,
    required AppLocalizations l10n,
  }) {
    final List<String> usernameErrors = [];
    final List<String> passwordErrors = [];

    // Validate username (only required)
    final usernameError = Validators.validateRequired(username, l10n);
    if (usernameError != null) {
      usernameErrors.add(usernameError);
    }

    // Validate password (only required)
    final passwordError = Validators.validateRequired(password, l10n);
    if (passwordError != null) {
      passwordErrors.add(passwordError);
    }

    return LoginValidationError(
      username: usernameErrors.isEmpty ? null : usernameErrors,
      password: passwordErrors.isEmpty ? null : passwordErrors,
    );
  }
}
