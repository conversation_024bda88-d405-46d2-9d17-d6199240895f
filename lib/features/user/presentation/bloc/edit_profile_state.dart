import 'package:equatable/equatable.dart';
import '../../domain/entities/user_profile.dart';

/// States for edit profile functionality
abstract class EditProfileState extends Equatable {
  const EditProfileState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class EditProfileInitial extends EditProfileState {
  const EditProfileInitial();
}

/// Loading state
class EditProfileLoading extends EditProfileState {
  const EditProfileLoading();
}

/// Profile loaded state
class EditProfileLoaded extends EditProfileState {
  final UserProfile profile;

  const EditProfileLoaded({required this.profile});

  @override
  List<Object?> get props => [profile];
}

/// Profile updating state
class EditProfileUpdating extends EditProfileState {
  const EditProfileUpdating();
}

/// Profile update success state
class EditProfileUpdateSuccess extends EditProfileState {
  final UserProfile updatedProfile;

  const EditProfileUpdateSuccess({required this.updatedProfile});

  @override
  List<Object?> get props => [updatedProfile];
}

/// Error state
class EditProfileError extends EditProfileState {
  final String message;

  const EditProfileError({required this.message});

  @override
  List<Object?> get props => [message];
}
