import 'package:freezed_annotation/freezed_annotation.dart';

part 'forgot_password_state.freezed.dart';

@freezed
sealed class ForgotPasswordState with _$ForgotPasswordState {
  /// Initial state when forgot password flow starts
  const factory ForgotPasswordState.initial() = ForgotPasswordInitial;

  /// State when sending forgot password request
  const factory ForgotPasswordState.loading() = ForgotPasswordLoading;

  /// State when forgot password request was successful
  const factory ForgotPasswordState.success({
    required String token,
    required String refCode,
    required String message,
  }) = ForgotPasswordSuccess;

  /// State when an error occurs
  const factory ForgotPasswordState.error({
    required String message,
    String? emailError,
  }) = ForgotPasswordError;
}
