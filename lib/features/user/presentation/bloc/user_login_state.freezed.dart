// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_login_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UserLoginState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserLoginState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'UserLoginState()';
}


}

/// @nodoc
class $UserLoginStateCopyWith<$Res>  {
$UserLoginStateCopyWith(UserLoginState _, $Res Function(UserLoginState) __);
}


/// @nodoc


class _Idle implements UserLoginState {
  const _Idle();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Idle);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'UserLoginState.idle()';
}


}




/// @nodoc


class _Loading implements UserLoginState {
  const _Loading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Loading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'UserLoginState.loading()';
}


}




/// @nodoc


class _Success implements UserLoginState {
  const _Success(this.loginData);
  

 final  LoginData loginData;

/// Create a copy of UserLoginState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SuccessCopyWith<_Success> get copyWith => __$SuccessCopyWithImpl<_Success>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Success&&(identical(other.loginData, loginData) || other.loginData == loginData));
}


@override
int get hashCode => Object.hash(runtimeType,loginData);

@override
String toString() {
  return 'UserLoginState.success(loginData: $loginData)';
}


}

/// @nodoc
abstract mixin class _$SuccessCopyWith<$Res> implements $UserLoginStateCopyWith<$Res> {
  factory _$SuccessCopyWith(_Success value, $Res Function(_Success) _then) = __$SuccessCopyWithImpl;
@useResult
$Res call({
 LoginData loginData
});




}
/// @nodoc
class __$SuccessCopyWithImpl<$Res>
    implements _$SuccessCopyWith<$Res> {
  __$SuccessCopyWithImpl(this._self, this._then);

  final _Success _self;
  final $Res Function(_Success) _then;

/// Create a copy of UserLoginState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? loginData = null,}) {
  return _then(_Success(
null == loginData ? _self.loginData : loginData // ignore: cast_nullable_to_non_nullable
as LoginData,
  ));
}


}

/// @nodoc


class _ShowError implements UserLoginState {
  const _ShowError(this.message);
  

 final  String message;

/// Create a copy of UserLoginState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShowErrorCopyWith<_ShowError> get copyWith => __$ShowErrorCopyWithImpl<_ShowError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShowError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'UserLoginState.showError(message: $message)';
}


}

/// @nodoc
abstract mixin class _$ShowErrorCopyWith<$Res> implements $UserLoginStateCopyWith<$Res> {
  factory _$ShowErrorCopyWith(_ShowError value, $Res Function(_ShowError) _then) = __$ShowErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class __$ShowErrorCopyWithImpl<$Res>
    implements _$ShowErrorCopyWith<$Res> {
  __$ShowErrorCopyWithImpl(this._self, this._then);

  final _ShowError _self;
  final $Res Function(_ShowError) _then;

/// Create a copy of UserLoginState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(_ShowError(
null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ShowFallback implements UserLoginState {
  const _ShowFallback(this.message);
  

 final  String message;

/// Create a copy of UserLoginState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShowFallbackCopyWith<_ShowFallback> get copyWith => __$ShowFallbackCopyWithImpl<_ShowFallback>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShowFallback&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'UserLoginState.showFallback(message: $message)';
}


}

/// @nodoc
abstract mixin class _$ShowFallbackCopyWith<$Res> implements $UserLoginStateCopyWith<$Res> {
  factory _$ShowFallbackCopyWith(_ShowFallback value, $Res Function(_ShowFallback) _then) = __$ShowFallbackCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class __$ShowFallbackCopyWithImpl<$Res>
    implements _$ShowFallbackCopyWith<$Res> {
  __$ShowFallbackCopyWithImpl(this._self, this._then);

  final _ShowFallback _self;
  final $Res Function(_ShowFallback) _then;

/// Create a copy of UserLoginState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(_ShowFallback(
null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
