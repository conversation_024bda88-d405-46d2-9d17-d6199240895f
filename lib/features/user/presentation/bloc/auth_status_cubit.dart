import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/features/user/domain/usecases/check_login_status.dart';
import 'auth_status_state.dart';

/// Cubit for managing authentication status checking
class AuthStatusCubit extends Cubit<AuthStatusState> {
  final CheckLoginStatus _checkLoginStatus;

  AuthStatusCubit({required CheckLoginStatus checkLoginStatus})
      : _checkLoginStatus = checkLoginStatus,
        super(const AuthStatusState.initial());

  /// Check if user is currently logged in
  Future<void> checkAuthStatus() async {
    emit(const AuthStatusState.checking());

    final result = await _checkLoginStatus(NoParams());

    result.fold(
      (failure) => emit(AuthStatusState.error(failure.message)),
      (loginData) {
        if (loginData != null) {
          emit(AuthStatusState.authenticated(loginData));
        } else {
          emit(const AuthStatusState.unauthenticated());
        }
      },
    );
  }

  /// Reset to initial state
  void resetToInitial() {
    emit(const AuthStatusState.initial());
  }
}
