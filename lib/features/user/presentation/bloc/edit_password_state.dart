import 'package:equatable/equatable.dart';

/// Base state for edit password
abstract class EditPasswordState extends Equatable {
  const EditPasswordState();

  @override
  List<Object> get props => [];
}

/// Initial state
class EditPasswordInitial extends EditPasswordState {
  const EditPasswordInitial();
}

/// Loading state
class EditPasswordLoading extends EditPasswordState {
  const EditPasswordLoading();
}

/// Success state
class EditPasswordSuccess extends EditPasswordState {
  const EditPasswordSuccess();
}

/// Error state
class EditPasswordError extends EditPasswordState {
  final String message;

  const EditPasswordError({required this.message});

  @override
  List<Object> get props => [message];
}
