// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_id_card_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CheckIdCardState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckIdCardState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CheckIdCardState()';
}


}

/// @nodoc
class $CheckIdCardStateCopyWith<$Res>  {
$CheckIdCardStateCopyWith(CheckIdCardState _, $Res Function(CheckIdCardState) __);
}


/// @nodoc


class CheckIdCardIdle implements CheckIdCardState {
  const CheckIdCardIdle();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckIdCardIdle);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CheckIdCardState.idle()';
}


}




/// @nodoc


class CheckIdCardLoading implements CheckIdCardState {
  const CheckIdCardLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckIdCardLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CheckIdCardState.loading()';
}


}




/// @nodoc


class CheckIdCardValid implements CheckIdCardState {
  const CheckIdCardValid();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckIdCardValid);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CheckIdCardState.valid()';
}


}




/// @nodoc


class CheckIdCardInvalid implements CheckIdCardState {
  const CheckIdCardInvalid(this.errorMessage);
  

 final  String errorMessage;

/// Create a copy of CheckIdCardState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckIdCardInvalidCopyWith<CheckIdCardInvalid> get copyWith => _$CheckIdCardInvalidCopyWithImpl<CheckIdCardInvalid>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckIdCardInvalid&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'CheckIdCardState.invalid(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $CheckIdCardInvalidCopyWith<$Res> implements $CheckIdCardStateCopyWith<$Res> {
  factory $CheckIdCardInvalidCopyWith(CheckIdCardInvalid value, $Res Function(CheckIdCardInvalid) _then) = _$CheckIdCardInvalidCopyWithImpl;
@useResult
$Res call({
 String errorMessage
});




}
/// @nodoc
class _$CheckIdCardInvalidCopyWithImpl<$Res>
    implements $CheckIdCardInvalidCopyWith<$Res> {
  _$CheckIdCardInvalidCopyWithImpl(this._self, this._then);

  final CheckIdCardInvalid _self;
  final $Res Function(CheckIdCardInvalid) _then;

/// Create a copy of CheckIdCardState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = null,}) {
  return _then(CheckIdCardInvalid(
null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
