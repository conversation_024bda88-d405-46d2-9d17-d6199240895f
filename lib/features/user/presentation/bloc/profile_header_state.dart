import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';

part 'profile_header_state.freezed.dart';

/// States for profile header functionality
@freezed
class ProfileHeaderState with _$ProfileHeaderState {
  /// Initial state
  const factory ProfileHeaderState.initial() = ProfileHeaderInitial;

  /// Loading user data state
  const factory ProfileHeaderState.loading() = ProfileHeaderLoading;

  /// User data loaded successfully
  const factory ProfileHeaderState.loaded({
    required User user,
  }) = ProfileHeaderLoaded;

  /// Error loading user data
  const factory ProfileHeaderState.error({
    required String message,
  }) = ProfileHeaderError;
}
