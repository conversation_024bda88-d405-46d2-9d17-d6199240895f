import 'package:equatable/equatable.dart';
import 'package:mcdc/features/user/domain/entities/login_validation_error.dart';

/// State for login form validation
abstract class ValidateLoginState extends Equatable {
  const ValidateLoginState();

  @override
  List<Object?> get props => [];
}

/// Initial state - no validation performed yet
class ValidateLoginIdle extends ValidateLoginState {
  const ValidateLoginIdle();
}

/// Validation in progress
class ValidateLoginLoading extends ValidateLoginState {
  const ValidateLoginLoading();
}

/// Validation passed - form is valid
class ValidateLoginValid extends ValidateLoginState {
  const ValidateLoginValid();
}

/// Validation failed - form has errors
class ValidateLoginInvalid extends ValidateLoginState {
  const ValidateLoginInvalid(this.error);

  final LoginValidationError error;

  @override
  List<Object?> get props => [error];
}
