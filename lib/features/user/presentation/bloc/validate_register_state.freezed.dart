// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'validate_register_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ValidateRegisterState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateRegisterState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateRegisterState()';
}


}

/// @nodoc
class $ValidateRegisterStateCopyWith<$Res>  {
$ValidateRegisterStateCopyWith(ValidateRegisterState _, $Res Function(ValidateRegisterState) __);
}


/// @nodoc


class ValidateRegisterIdle implements ValidateRegisterState {
  const ValidateRegisterIdle();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateRegisterIdle);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateRegisterState.idle()';
}


}




/// @nodoc


class ValidateRegisterLoading implements ValidateRegisterState {
  const ValidateRegisterLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateRegisterLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateRegisterState.loading()';
}


}




/// @nodoc


class ValidateRegisterValid implements ValidateRegisterState {
  const ValidateRegisterValid();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateRegisterValid);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ValidateRegisterState.valid()';
}


}




/// @nodoc


class ValidateRegisterInvalid implements ValidateRegisterState {
  const ValidateRegisterInvalid(this.error);
  

 final  RegisterValidationError error;

/// Create a copy of ValidateRegisterState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ValidateRegisterInvalidCopyWith<ValidateRegisterInvalid> get copyWith => _$ValidateRegisterInvalidCopyWithImpl<ValidateRegisterInvalid>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ValidateRegisterInvalid&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,error);

@override
String toString() {
  return 'ValidateRegisterState.invalid(error: $error)';
}


}

/// @nodoc
abstract mixin class $ValidateRegisterInvalidCopyWith<$Res> implements $ValidateRegisterStateCopyWith<$Res> {
  factory $ValidateRegisterInvalidCopyWith(ValidateRegisterInvalid value, $Res Function(ValidateRegisterInvalid) _then) = _$ValidateRegisterInvalidCopyWithImpl;
@useResult
$Res call({
 RegisterValidationError error
});




}
/// @nodoc
class _$ValidateRegisterInvalidCopyWithImpl<$Res>
    implements $ValidateRegisterInvalidCopyWith<$Res> {
  _$ValidateRegisterInvalidCopyWithImpl(this._self, this._then);

  final ValidateRegisterInvalid _self;
  final $Res Function(ValidateRegisterInvalid) _then;

/// Create a copy of ValidateRegisterState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = null,}) {
  return _then(ValidateRegisterInvalid(
null == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as RegisterValidationError,
  ));
}


}

// dart format on
