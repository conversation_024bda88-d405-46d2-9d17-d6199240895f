import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_success.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_warning.dart';
import 'bloc/create_password_cubit.dart';
import 'bloc/create_password_state.dart';

@RoutePage()
class CreatePasswordPage extends StatefulWidget {
  final String email;
  final String otpToken;

  const CreatePasswordPage({
    super.key,
    required this.email,
    required this.otpToken,
  });

  @override
  State<CreatePasswordPage> createState() => _CreatePasswordPageState();
}

class _CreatePasswordPageState extends State<CreatePasswordPage> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late CreatePasswordCubit _createPasswordCubit;

  @override
  void initState() {
    super.initState();
    _createPasswordCubit = AppInjector.get<CreatePasswordCubit>();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _createPasswordCubit.close();
    super.dispose();
  }

  void _onConfirmPressed() async {
    if (_formKey.currentState!.validate()) {
      final l10n = AppLocalizations.of(context)!;

      await _createPasswordCubit.createNewPassword(
        email: widget.email,
        newPassword: _passwordController.text,
        confirmPassword: _confirmPasswordController.text,
        verifiedToken: widget.otpToken,
        l10n: l10n,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocProvider<CreatePasswordCubit>(
      create: (context) => _createPasswordCubit,
      child: Scaffold(
        backgroundColor: AppColors.backgroundDefault,
        appBar: AppBarCommon(title: l10n.createPasswordPageTitle),
        body: BlocConsumer<CreatePasswordCubit, CreatePasswordState>(
          listener: (context, state) {
            switch (state) {
              case CreatePasswordInitial():
                break;
              case CreatePasswordLoading():
                break;
              case CreatePasswordSuccess():
                // Show success dialog with auto-dismiss and navigate to login
                DialogSuccess.show(
                  context: context,
                  title: state.message,
                  autoDismissDuration: const Duration(seconds: 3),
                  afterDismiss: () {
                    // Navigate to login page after dialog closes
                    context.router.replaceAll([LoginRoute()]);
                  },
                );
                break;
              case CreatePasswordError():
                // Show error dialog if it's not a validation error
                if (state.message != 'Validation failed') {
                  DialogWarning.show(
                    context: context,
                    title: null,
                    message: state.message,
                  );
                }
                break;
            }
          },
          builder: (context, state) {
            final isLoading = state is CreatePasswordLoading;

            return SafeArea(
              child: Column(
                children: [
                  // Main content
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 32.h),

                              // Title
                              Text(
                                l10n.createPasswordInstruction,
                                style: TextStyle(
                                  fontFamily: AppFonts.notoSansThai,
                                  fontSize: 20.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textDefaultDark,
                                  height: 1.5,
                                ),
                              ),

                              SizedBox(height: 24.h),

                              // New Password field
                              BlocBuilder<
                                CreatePasswordCubit,
                                CreatePasswordState
                              >(
                                builder: (context, state) {
                                  return CustomTextField(
                                    controller: _passwordController,
                                    label: l10n.newPasswordLabel,
                                    obscureText: true,
                                    isRequired: true,
                                    validator:
                                        (value) => _createPasswordCubit
                                            .validatePassword(value, l10n),
                                    forceErrorText:
                                        _createPasswordCubit.getPasswordError(),
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _createPasswordCubit.updatePassword(
                                        value,
                                      );
                                    },
                                  );
                                },
                              ),

                              SizedBox(height: 24.h),

                              // Confirm Password field
                              BlocBuilder<
                                CreatePasswordCubit,
                                CreatePasswordState
                              >(
                                builder: (context, state) {
                                  return CustomTextField(
                                    controller: _confirmPasswordController,
                                    label: l10n.confirmNewPasswordLabel,
                                    obscureText: true,
                                    isRequired: true,
                                    validator:
                                        (value) => _createPasswordCubit
                                            .validateConfirmPassword(
                                              value,
                                              l10n,
                                            ),
                                    forceErrorText:
                                        _createPasswordCubit
                                            .getConfirmPasswordError(),
                                    textInputAction: TextInputAction.done,
                                    onChanged: (value) {
                                      _createPasswordCubit
                                          .updateConfirmPassword(value);
                                    },
                                    onSubmitted: (_) => _onConfirmPressed(),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Bottom button
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 4,
                          offset: const Offset(0, -1),
                        ),
                      ],
                    ),
                    child: PrimaryButton(
                      text: l10n.confirm,
                      width: 345.w,
                      height: 52.h,
                      borderRadius: 50,
                      onPressed: isLoading ? null : _onConfirmPressed,
                      isLoading: isLoading,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
