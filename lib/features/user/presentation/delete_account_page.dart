import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_confirm.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_success.dart';
import 'package:mcdc/shared/presentation/widgets/loading/loading_overlay.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'bloc/delete_account_cubit.dart';
import 'bloc/delete_account_state.dart';

@RoutePage()
class DeleteAccountPage extends StatelessWidget {
  const DeleteAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DeleteAccountCubit>(
      create: (context) => AppInjector.get<DeleteAccountCubit>(),
      child: const _DeleteAccountPageContent(),
    );
  }
}

class _DeleteAccountPageContent extends StatefulWidget {
  const _DeleteAccountPageContent();

  @override
  State<_DeleteAccountPageContent> createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState extends State<_DeleteAccountPageContent> {
  final _formKey = GlobalKey<FormState>();

  // Controller for password field
  late final TextEditingController _passwordController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.deleteAccount),
      body: BlocConsumer<DeleteAccountCubit, DeleteAccountState>(
        listener: (context, state) {
          if (state is DeleteAccountSuccess) {
            _showSuccessDialog(l10n);
          } else if (state is DeleteAccountError) {
            _showErrorSnackBar(state.message);
          }
        },
        builder: (context, state) {
          return LoadingOverlay(
            isLoading: state is DeleteAccountLoading,
            child: _buildBody(l10n),
          );
        },
      ),
    );
  }

  Widget _buildBody(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Password Confirmation Section
            _buildPasswordConfirmationSection(l10n),
            SizedBox(height: 32.h),

            // Action Buttons
            _buildActionButtons(l10n),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordConfirmationSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'กรอกรหัสผ่านเพื่อยืนยันการลบบัญชี',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),

        // Current Password field
        CustomTextField(
          controller: _passwordController,
          label: l10n.currentPasswordLabel,
          hintText: l10n.currentPasswordHint,
          obscureText: true,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateCurrentPasswordRequired;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.cancel,
            onPressed: () => context.router.back(),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: PrimaryButton(
            text: l10n.saveButton,
            onPressed: () => _onDeletePressed(l10n),
          ),
        ),
      ],
    );
  }

  void _onDeletePressed(AppLocalizations l10n) async {
    if (_formKey.currentState?.validate() ?? false) {
      final confirmed = await _showConfirmationDialog(l10n);
      if (!confirmed) return;
      if (confirmed && mounted) {
        context.read<DeleteAccountCubit>().deleteAccount(
          password: _passwordController.text,
        );
      }
    }
  }

  Future<bool> _showConfirmationDialog(AppLocalizations l10n) async {
    final confirmed = await DialogConfirm.show(
      context: context,
      title: l10n.confirmDeleteAccount,
      message: l10n.confirmDeleteAccountMessage,
    );

    return confirmed ?? false;
  }

  void _showSuccessDialog(AppLocalizations l10n) {
    DialogSuccess.show(
      context: context,
      afterDismiss: () {
        context.read<DeleteAccountCubit>().resetToInitial();
        // Navigate to login or welcome screen after account deletion
        context.router.pushPath('/member-welcome');
      },
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.critical),
    );
  }
}
