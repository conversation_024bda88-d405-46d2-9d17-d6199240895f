import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/features/user/presentation/components/bottom_sheet_privacy_policy.dart';

@RoutePage()
class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  State<PrivacyPolicyPage> createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage> {
  bool _isPrivacyPolicyAccepted = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 24.h),
                      _buildPrivacyPolicyContent(l10n),
                      SizedBox(height: 24.h),
                      _buildCheckbox(l10n),
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
            ),
            _buildBottomButton(context, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: const BoxDecoration(color: Colors.white),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: SvgPicture.asset(
              'assets/icons/arrow_left.svg',
              width: 24.w,
              height: 24.h,
              colorFilter: const ColorFilter.mode(
                AppColors.iconDefault,
                BlendMode.srcIn,
              ),
            ),
            onPressed: () => context.router.pop(),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildPrivacyPolicyContent(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.privacyPolicyDate,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          l10n.privacyPolicyDescription,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 24.h),
        Text(
          l10n.personalDataUsageTitle,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          l10n.personalDataUsageContent,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildCheckbox(AppLocalizations l10n) {
    return Row(
      children: [
        SizedBox(
          width: 24.w,
          height: 24.w,
          child: Checkbox(
            value: _isPrivacyPolicyAccepted,
            onChanged: (value) {
              setState(() {
                _isPrivacyPolicyAccepted = value ?? false;
              });
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4.r),
              side: const BorderSide(color: AppColors.borderDefault),
            ),
            activeColor: AppColors.buttonPrimary,
            side: const BorderSide(color: AppColors.borderDefault, width: 1.5),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _isPrivacyPolicyAccepted = !_isPrivacyPolicyAccepted;
              });
            },
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '${l10n.accept} ',
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textDefault,
                      height: 1.5,
                    ),
                  ),
                  TextSpan(
                    text: l10n.privacyPolicyTitle,
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textPrimary,
                      height: 1.5,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer:
                        TapGestureRecognizer()
                          ..onTap = () {
                            BottomSheetPrivacyPolicy.show(context);
                          },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButton(BuildContext context, AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: PrimaryButton(
        text: l10n.accept,
        onPressed:
            _isPrivacyPolicyAccepted
                ? () {
                  context.router.replace(const MemberRegisterRoute());
                }
                : null,
        isDisabled: !_isPrivacyPolicyAccepted,
        height: 52.h,
        borderRadius: 50.r,
      ),
    );
  }
}
