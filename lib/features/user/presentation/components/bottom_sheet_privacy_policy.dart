import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

class BottomSheetPrivacyPolicy extends StatelessWidget {
  const BottomSheetPrivacyPolicy({super.key});

  /// Shows a modal bottom sheet with privacy policy content
  static Future<void> show(BuildContext context) async {
    await showMaterialModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      backgroundColor: Colors.white,
      builder: (context) => const BottomSheetPrivacyPolicy(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context, l10n),
          Flexible(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 24.h),
                    _buildContent(l10n),
                    SizedBox(height: 24.h),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, AppLocalizations l10n) {
    return Container(
      padding: EdgeInsets.only(
        left: 24.w,
        right: 16.w,
        top: 48.h,
        bottom: 24.h,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            l10n.privacyPolicyTitle,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: SvgPicture.asset(
                'assets/icons/x_close.svg',
                width: 24.w,
                height: 24.h,
                colorFilter: const ColorFilter.mode(
                  AppColors.iconDefault,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.privacyPolicyDate,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          l10n.privacyPolicyDescription,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 24.h),
        _buildSectionTitle(l10n.personalDataCollectionTitle),
        SizedBox(height: 16.h),
        _buildSectionContent(l10n.personalDataCollectionContent),
        SizedBox(height: 24.h),
        _buildSectionTitle(l10n.personalDataUsageTitle),
        SizedBox(height: 16.h),
        _buildSectionContent(l10n.personalDataUsageContent),
        SizedBox(height: 24.h),
        _buildSectionTitle(l10n.privacyPolicyUpdateTitle),
        SizedBox(height: 16.h),
        _buildSectionContent(l10n.privacyPolicyUpdateContent),
        SizedBox(height: 16.h),
        _buildSectionContent(l10n.contactInfoContent),
        SizedBox(height: 16.h),
        _buildSectionContent(l10n.contactDetails),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }

  Widget _buildSectionContent(String content) {
    return Text(
      content,
      style: TextStyle(
        fontFamily: AppFonts.notoSansThai,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
    );
  }
}
