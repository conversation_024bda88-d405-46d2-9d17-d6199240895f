import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/constants/styles.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';

@RoutePage()
class MyProfilePage extends StatelessWidget {
  const MyProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.myAccount),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Header Section
              _buildProfileHeader(),
              SizedBox(height: 24.h),

              // User Information Section
              _buildUserInformationSection(context),
              SizedBox(height: 24.h),
              // Action Buttons Section
              _buildActionButtons(context),
              0.15.sh.verticalSpace,
              _buildDeleteAccountButton(context, l10n),
              32.h.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Row(
      children: [
        // Avatar with camera overlay
        Stack(
          children: [
            Container(
              width: 94.w,
              height: 94.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.surfacePrimarySubdude,
              ),
              child: Center(
                child: Text(
                  'A',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 32.w,
                height: 32.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.backgroundDefault,
                  border: Border.all(color: AppColors.borderDefault, width: 1),
                ),
                child: Center(
                  child: SvgPicture.asset(
                    'assets/icons/camera.svg',
                    width: 16.w,
                    height: 16.w,
                    colorFilter: const ColorFilter.mode(
                      AppColors.iconDefault,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(width: 24.w),

        // Company Information
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'บริษัท วีวาสนาดี จำกัด',
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'ภาคเอกชน',
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUserInformationSection(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.userInformation,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 24.h),

        _buildInfoRow(l10n.fullName, 'Pattamon Wongwarang'),
        SizedBox(height: 16.h),

        _buildInfoRow(l10n.username, 'Pattamon'),
        SizedBox(height: 16.h),

        _buildInfoRow(l10n.idCardNumber, '1345567890123'),
        SizedBox(height: 16.h),

        _buildInfoRow(l10n.phoneNumber, '0819999898'),
        SizedBox(height: 16.h),

        _buildInfoRow(l10n.email, '<EMAIL>'),
        SizedBox(height: 24.h),

        Text(
          l10n.organizationInformation,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 24.h),

        _buildInfoRow(l10n.sectorType, 'ภาคเอกชน'),
        SizedBox(height: 16.h),

        _buildInfoRow(l10n.organizationName, 'วีวาสนาดี'),
        SizedBox(height: 16.h),

        _buildInfoRow(l10n.websiteName, '-'),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 149.w,
          child: Text(
            label,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textSubdude,
              height: 1.5,
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textDefaultDark,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        Expanded(
          child: PrimaryButton(
            text: l10n.editProfile,
            height: 40.h,
            onPressed: () {
              context.router.pushPath('/edit-profile');
            },
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: PrimaryButton(
            text: l10n.changePassword,
            height: 40.h,
            onPressed: () {
              context.router.pushPath('/edit-password');
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDeleteAccountButton(
    BuildContext context,
    AppLocalizations l10n,
  ) {
    return Align(
      alignment: Alignment.center,
      child: GestureDetector(
        onTap: () {
          context.router.pushPath('/delete-account');
        },
        child: Text(
          l10n.deleteAccount,
          style: kHeaderStyle.copyWith(
            color: AppColors.textCritical,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
