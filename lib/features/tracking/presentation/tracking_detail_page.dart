import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/features/tracking/presentation/components/tracking_status_card.dart';

@RoutePage()
class TrackingDetailPage extends StatelessWidget {
  final TrackingStatusData trackingData;

  const TrackingDetailPage({super.key, required this.trackingData});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: AppBarCommon(title: l10n.trackingDetails),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top tracking status card (similar to the original)
            TrackingStatusCard(trackingData: trackingData),
            24.h.verticalSpace,
            // Detailed timeline
            _buildTimelineSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      padding: EdgeInsets.all(24.w),
      child: Stack(
        children: [
          // Timeline line positioned behind content
          _buildTimelineLine(),
          // Timeline items
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTimelineItem(
                isCompleted: true,
                title: 'เปิดการแก้ไขข้อมูล',
                date: '3 มี.ค 2568 13:30',
                description:
                    'กรุณาดำเนินการแก้ไขข้อมูลและเอกสารตามที่ เจ้าหน้าที่ได้ดำเนินการแจ้งให้แก้ไข ภายในวันที่ 10 มีนาคม 2568 เวลา 10:00 น.',
                note:
                    'เอกสารที่ส่งมาไม่ครบถ้วน และบางเอกสาร หมดอายุแล้วอาจจะต้องทำการขอใหม่ อีกครั้งก่อนทำรายการ',
                hasAttachment: true,
                isTopItem: true,
              ),
              34.h.verticalSpace,
              _buildTimelineItem(
                isCompleted: false,
                title: 'เจ้าหน้าที่กำลังตรวจสอบ',
                date: '2 มี.ค 2568 13:30',
                description:
                    'เจ้าหน้าที่กำลังดำเนินการตวจสอบข้อมูลและ เอกสารการยื่นเรื่อง',
              ),
              34.h.verticalSpace,
              _buildTimelineItem(
                isCompleted: false,
                title: 'ยื่นเรื่อง',
                date: '1 มี.ค 2568 13:30',
                description: 'รอเจ้าหน้าทรับเรื่องเพื่อดำเนินการ',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineLine() {
    return Positioned(
      left: 8.w,
      top: 16.h,
      child: Container(
        width: 4.w,
        height: 400.h,
        decoration: BoxDecoration(
          color: AppColors.borderSubdude,
          borderRadius: BorderRadius.circular(100.r),
        ),
      ),
    );
  }

  Widget _buildTimelineItem({
    required bool isCompleted,
    required String title,
    required String date,
    required String description,
    String? note,
    bool hasAttachment = false,
    bool isTopItem = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline dot
        Container(
          width: 16.w,
          height: 16.w,
          decoration: BoxDecoration(
            color:
                isCompleted
                    ? AppColors.surfacePrimary
                    : AppColors.borderSubdude,
            shape: BoxShape.circle,
          ),
        ),
        16.w.horizontalSpace,
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and date row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.surfacePrimary,
                      height: 1.5,
                    ),
                  ),
                  SizedBox(
                    width: 93.w,
                    height: 24.h,
                    child: Text(
                      date,
                      style: TextStyle(
                        fontFamily: AppFonts.notoSansThai,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textSubdude,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
              8.h.verticalSpace,
              // Description
              Text(
                description,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textSubdude,
                  height: 1.5,
                ),
              ),
              // Note section (if exists)
              if (note != null) ...[
                8.h.verticalSpace,
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceDefault,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        note,
                        style: TextStyle(
                          fontFamily: AppFonts.notoSansThai,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textSubdude,
                          height: 1.5,
                        ),
                      ),
                      if (hasAttachment) ...[
                        8.h.verticalSpace,
                        Builder(
                          builder: (context) => _buildAttachmentRow(context),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentRow(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        SvgPicture.asset(
          'assets/icons/file_invoice.svg',
          width: 16.w,
          height: 16.w,
          colorFilter: const ColorFilter.mode(
            AppColors.surfacePrimary,
            BlendMode.srcIn,
          ),
        ),
        4.w.horizontalSpace,
        Text(
          l10n.attachment,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.surfacePrimary,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
