import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/routes/router.gr.dart';

/// Enum for tracking status types
enum TrackingStatusType {
  submitted, // ยื่นเรื่อง - Blue
  editing, // เปิดการแก้ไขข้อมูล - Yellow
  pendingPayment, // รอชำระเงิน - Yellow
  approved, // อนุมัติ - <PERSON>
  completed, // ยื่นเรื่องสำเร็จ - Dark Green
  returned, // คืนเรื่อง - Red
}

/// Data model for tracking status information
class TrackingStatusData {
  final String title;
  final String statusText;
  final TrackingStatusType statusType;
  final String date;
  final int currentStep;
  final int totalSteps;

  const TrackingStatusData({
    required this.title,
    required this.statusText,
    required this.statusType,
    required this.date,
    required this.currentStep,
    required this.totalSteps,
  });
}

class TrackingStatusCard extends StatelessWidget {
  final TrackingStatusData trackingData;

  const TrackingStatusCard({super.key, required this.trackingData});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _navigateToDetail(context),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(31, 34, 39, 0.08),
              blurRadius: 4.r,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        padding: EdgeInsets.all(24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _buildHeader(),
            24.h.verticalSpace,
            _buildProgressSection(),
            16.h.verticalSpace,
            _buildDateInfo(context),
          ],
        ),
      ),
    );
  }

  void _navigateToDetail(BuildContext context) {
    context.router.push(TrackingDetailRoute(trackingData: trackingData));
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [_buildStatusTag()],
        ),
        8.h.verticalSpace,
        Text(
          trackingData.title,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  Widget _buildStatusTag() {
    final statusConfig = _getStatusConfig();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(50.r),
        border: Border.all(color: AppColors.borderSubdude),
      ),
      child: Text(
        trackingData.statusText,
        style: TextStyle(
          fontFamily: AppFonts.notoSansThai,
          fontSize: 12.sp,
          fontWeight: FontWeight.w400,
          color: statusConfig.textColor,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    return Stack(
      alignment: Alignment.center,
      children: [
        _buildProgressBar(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildProgressIcon(0, 'file_invoice'),
            _buildProgressIcon(1, 'hourglass_start'),
            _buildProgressIcon(2, 'file_check'),
            _buildProgressIcon(3, 'bullhorn'),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressIcon(int stepIndex, String iconName) {
    final isCompleted = stepIndex < trackingData.currentStep;
    final isActive = stepIndex == trackingData.currentStep - 1;

    Color backgroundColor;
    Color iconColor;

    if (isCompleted || isActive) {
      backgroundColor = AppColors.surfacePrimary;
      iconColor = AppColors.textDefaultWhite;
    } else {
      backgroundColor = AppColors.surfaceProgressIndicator;
      iconColor = AppColors.surfacePrimary;
    }

    return Container(
      width: 32.w,
      height: 32.w,
      decoration: BoxDecoration(color: backgroundColor, shape: BoxShape.circle),
      child: Center(
        child: SvgPicture.asset(
          'assets/icons/$iconName.svg',
          width: 16.w,
          height: 16.w,
          colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      width: double.infinity,
      height: 4.h,
      decoration: BoxDecoration(
        color: AppColors.surfaceProgressIndicator,
        borderRadius: BorderRadius.circular(100.r),
      ),
      child: Stack(
        children: [
          // Progress fill
          FractionallySizedBox(
            widthFactor:
                (trackingData.currentStep - 1) / (trackingData.totalSteps - 1),
            child: Container(
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColors.surfacePrimary,
                borderRadius: BorderRadius.circular(100.r),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateInfo(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          l10n.submissionDate,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
        Text(
          trackingData.date,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  _StatusConfig _getStatusConfig() {
    switch (trackingData.statusType) {
      case TrackingStatusType.submitted:
        return const _StatusConfig(
          textColor: AppColors.textPrimary, // Blue
        );
      case TrackingStatusType.editing:
      case TrackingStatusType.pendingPayment:
        return const _StatusConfig(
          textColor: AppColors.textWarning, // Yellow
        );
      case TrackingStatusType.approved:
        return const _StatusConfig(
          textColor: AppColors.textSuccess, // Green
        );
      case TrackingStatusType.completed:
        return const _StatusConfig(
          textColor: AppColors.textSuccessDark, // Dark Green
        );
      case TrackingStatusType.returned:
        return const _StatusConfig(
          textColor: AppColors.textCritical, // Red
        );
    }
  }
}

class _StatusConfig {
  final Color textColor;

  const _StatusConfig({required this.textColor});
}
