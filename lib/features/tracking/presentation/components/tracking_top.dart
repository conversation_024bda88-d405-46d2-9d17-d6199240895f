import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/sliver_app_bar_home.dart';

class TrackingTop extends StatelessWidget {
  const TrackingTop({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return SliverAppBarHome(
      title: l10n.trackingStatus,
      fontFamily: AppFonts.notoSansThai,
    );
  }
}
