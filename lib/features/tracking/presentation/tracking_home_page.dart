import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/shared/presentation/widgets/background/main_back_ground.dart';
import 'package:mcdc/features/tracking/presentation/components/tracking_top.dart';
import 'package:mcdc/features/tracking/presentation/components/tracking_status_card.dart';

@RoutePage()
class TrackingHomePage extends StatefulWidget {
  const TrackingHomePage({super.key});

  @override
  State<TrackingHomePage> createState() => _TrackingHomePageState();
}

class _TrackingHomePageState extends State<TrackingHomePage> {
  late List<TrackingStatusData> _trackingItems;

  @override
  void initState() {
    super.initState();
    _initializeTrackingData();
  }

  void _initializeTrackingData() {
    _trackingItems = [
      const TrackingStatusData(
        title: 'ยื่นคำร้องขึ้นทะเบียน',
        statusText: 'ยื่นเรื่อง',
        statusType: TrackingStatusType.submitted,
        date: '1 ม.ค. 2568',
        currentStep: 1,
        totalSteps: 4,
      ),
      const TrackingStatusData(
        title: 'ยื่นคำร้องขึ้นทะเบียน',
        statusText: 'เปิดการแก้ไขข้อมูล',
        statusType: TrackingStatusType.editing,
        date: '1 ม.ค. 2568',
        currentStep: 2,
        totalSteps: 4,
      ),
      const TrackingStatusData(
        title: 'ยื่นคำร้องขึ้นทะเบียน',
        statusText: 'รอชำระเงิน',
        statusType: TrackingStatusType.pendingPayment,
        date: '1 ม.ค. 2568',
        currentStep: 2,
        totalSteps: 4,
      ),
      const TrackingStatusData(
        title: 'ยื่นคำร้องขึ้นทะเบียน',
        statusText: 'อนุมัติ',
        statusType: TrackingStatusType.approved,
        date: '1 ม.ค. 2568',
        currentStep: 3,
        totalSteps: 4,
      ),
      const TrackingStatusData(
        title: 'ยื่นคำร้องขึ้นทะเบียน',
        statusText: 'ยื่นเรื่องสำเร็จ',
        statusType: TrackingStatusType.completed,
        date: '1 ม.ค. 2568',
        currentStep: 4,
        totalSteps: 4,
      ),
      const TrackingStatusData(
        title: 'ยื่นคำร้องขึ้นทะเบียน',
        statusText: 'คืนเรื่อง',
        statusType: TrackingStatusType.returned,
        date: '1 ม.ค. 2568',
        currentStep: 3,
        totalSteps: 4,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      body: MainBackground(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            const TrackingTop(),
            SliverPadding(
              padding: EdgeInsets.fromLTRB(16.w, 32.h, 16.w, 24.h),
              sliver: SliverToBoxAdapter(child: _buildContent()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_buildSectionTitle(), _buildTrackingList()],
    );
  }

  Widget _buildSectionTitle() {
    final l10n = AppLocalizations.of(context)!;
    return Text(
      l10n.trackingProcessList,
      style: TextStyle(
        fontFamily: 'Noto Sans Thai',
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
        height: 1.5,
      ),
    );
  }

  Widget _buildTrackingList() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.only(top: 24.h),
      itemCount: _trackingItems.length,
      separatorBuilder: (context, index) => 24.h.verticalSpace,
      itemBuilder: (context, index) {
        return TrackingStatusCard(trackingData: _trackingItems[index]);
      },
    );
  }
}
