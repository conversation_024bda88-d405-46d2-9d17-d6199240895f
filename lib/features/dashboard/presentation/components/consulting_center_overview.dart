import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/styles.dart';

class ConsultingCenterOverview extends StatefulWidget {
  const ConsultingCenterOverview({super.key});

  @override
  State<ConsultingCenterOverview> createState() =>
      _ConsultingCenterOverviewState();
}

class _ConsultingCenterOverviewState extends State<ConsultingCenterOverview> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 345.w,
      height: 880.h,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            AppLocalizations.of(context)!.consultingCenterOverview,
            style: kHeaderStyle.copyWith(fontSize: 20.sp),
            textAlign: TextAlign.left,
          ),
          24.h.verticalSpace,
          // Statistics Cards
          Column(
            children: [
              _buildStatCard(
                context,
                iconPath: 'assets/icons/folder_icon.svg',
                count: '31,272',
                label: AppLocalizations.of(context)!.projectCount,
                gradientColors: [Color(0xFFFDBB12), Color(0xFFFE8B08)],
              ),
              SizedBox(height: 24.h),
              _buildStatCard(
                context,
                iconPath: 'assets/icons/consultant_icon.svg',
                count: '3,207',
                label: AppLocalizations.of(context)!.consultantCount,
                gradientColors: [Color(0xFFCE79FF), Color(0xFFA344F4)],
              ),
              SizedBox(height: 24.h),
              _buildStatCard(
                context,
                iconPath: 'assets/icons/user_group_icon.svg',
                count: '10,484',
                label: AppLocalizations.of(context)!.consultantPersonnelCount,
                gradientColors: [Color(0xFF71D4FF), Color(0xFF4EB0FF)],
              ),
            ],
          ),

          SizedBox(height: 24.h),

          // Chart Section
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  _buildConsultantTypeChart(),
                  _buildConsultantLevelChart(),
                  _buildCorporateConsultantChart(),
                ],
              ),
            ),
          ),

          // Page Indicator
          Padding(
            padding: EdgeInsets.symmetric(vertical: 24.h),
            child: _buildPageIndicator(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String iconPath,
    required String count,
    required String label,
    required List<Color> gradientColors,
  }) {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Color(0x141F2227),
            blurRadius: 4,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          SizedBox(
            width: 48.w,
            height: 48.w,
            child: Center(
              child: SvgPicture.asset(iconPath, width: 48.w, height: 48.w),
            ),
          ),
          24.w.horizontalSpace,
          // Text content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  count,
                  style: kDashboardBigNumStyle.copyWith(
                    fontSize: 28.sp,
                    color: AppColors.textDefaultDark,
                  ),
                ),
                Text(
                  label,
                  style: kSmallStyle.copyWith(color: AppColors.textDefaultDark),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConsultantTypeChart() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Color(0x141F2227),
            blurRadius: 4,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context)!.consultantType,
            style: kHeaderStyle.copyWith(
              fontSize: 20.sp,
              color: AppColors.textDefault,
            ),
          ),

          SizedBox(height: 24.h),

          // Custom Pie Chart
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Simple pie chart representation
                Container(
                  width: 200.w,
                  height: 200.h,
                  child: CustomPaint(
                    painter: SimplePieChartPainter(
                      data: [
                        ChartData(
                          AppLocalizations.of(context)!.corporateConsultant,
                          2601,
                          Color(0xFF6F58E9),
                        ),
                        ChartData(
                          AppLocalizations.of(context)!.independentConsultant,
                          607,
                          Color(0xFF2D99FE),
                        ),
                      ],
                    ),
                  ),
                ),

                // Center text
                Text(
                  '3,208',
                  style: kHeaderStyle.copyWith(
                    fontSize: 20.sp,
                    color: AppColors.textDefault,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),

          // Legend
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem(
                AppLocalizations.of(context)!.corporateConsultant,
                Color(0xFF6F58E9),
              ),
              _buildLegendItem(
                AppLocalizations.of(context)!.independentConsultant,
                Color(0xFF2D99FE),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConsultantLevelChart() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Color(0x141F2227),
            blurRadius: 4,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context)!.independentConsultantLevel,
            style: kHeaderStyle.copyWith(
              fontSize: 20.sp,
              color: AppColors.textDefault,
            ),
          ),

          SizedBox(height: 24.h),

          // Pie Chart
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: 200.w,
                  height: 200.h,
                  child: CustomPaint(
                    painter: SimplePieChartPainter(
                      data: [
                        ChartData(
                          AppLocalizations.of(context)!.level1,
                          33,
                          Color(0xFF4AC7FE),
                        ),
                        ChartData(
                          AppLocalizations.of(context)!.level2,
                          44,
                          Color(0xFF8CE6FF),
                        ),
                        ChartData(
                          AppLocalizations.of(context)!.level3,
                          530,
                          Color(0xFF2D99FE),
                        ),
                      ],
                    ),
                  ),
                ),

                Text(
                  '607',
                  style: kHeaderStyle.copyWith(
                    fontSize: 20.sp,
                    color: AppColors.textDefault,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),

          // Legend
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildLegendItem(
                    AppLocalizations.of(context)!.level1,
                    Color(0xFF4AC7FE),
                  ),
                  _buildLegendItem(
                    AppLocalizations.of(context)!.level2,
                    Color(0xFF8CE6FF),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              _buildLegendItem(
                AppLocalizations.of(context)!.level3,
                Color(0xFF2D99FE),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCorporateConsultantChart() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Color(0x141F2227),
            blurRadius: 4,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context)!.corporateConsultantLevel,
            style: kHeaderStyle.copyWith(
              fontSize: 20.sp,
              color: AppColors.textDefault,
            ),
          ),

          SizedBox(height: 24.h),

          // Pie Chart
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: 200.w,
                  height: 200.h,
                  child: CustomPaint(
                    painter: SimplePieChartPainter(
                      data: [
                        ChartData(
                          AppLocalizations.of(context)!.levelThat1,
                          1159,
                          Color(0xFF6F58E9),
                        ),
                        ChartData(
                          AppLocalizations.of(context)!.levelThat2,
                          636,
                          Color(0xFFCDC5F7),
                        ),
                        ChartData(
                          AppLocalizations.of(context)!.levelThat3,
                          806,
                          Color(0xFF9C8CF2),
                        ),
                      ],
                    ),
                  ),
                ),

                Text(
                  '2,601',
                  style: kHeaderStyle.copyWith(
                    fontSize: 20.sp,
                    color: AppColors.textDefault,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),

          // Legend
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildLegendItem(
                    AppLocalizations.of(context)!.levelThat1,
                    Color(0xFF6F58E9),
                  ),
                  _buildLegendItem(
                    AppLocalizations.of(context)!.levelThat2,
                    Color(0xFFCDC5F7),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              _buildLegendItem(
                AppLocalizations.of(context)!.levelThat3,
                Color(0xFF9C8CF2),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 10.w,
          height: 10.h,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        SizedBox(width: 8.w),
        Text(label, style: kSmallStyle.copyWith(color: AppColors.textDefault)),
      ],
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: index == _currentPage ? 28.w : 10.w,
          height: 10.h,
          decoration: BoxDecoration(
            color:
                index == _currentPage
                    ? AppColors.surfacePrimary
                    : AppColors.borderDefault,
            borderRadius: BorderRadius.circular(5.r),
          ),
        );
      }),
    );
  }
}

// Simple data class for chart data
class ChartData {
  final String label;
  final int value;
  final Color color;

  ChartData(this.label, this.value, this.color);
}

// Custom painter for simple pie chart
class SimplePieChartPainter extends CustomPainter {
  final List<ChartData> data;

  SimplePieChartPainter({required this.data});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    final total = data.fold<int>(0, (sum, item) => sum + item.value);
    double startAngle = -3.14159 / 2; // Start from top

    for (final item in data) {
      final sweepAngle = 2 * 3.14159 * item.value / total;

      final paint =
          Paint()
            ..color = item.color
            ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      // Draw white border between segments
      final borderPaint =
          Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        borderPaint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
