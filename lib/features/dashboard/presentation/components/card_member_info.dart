import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/features/dashboard/presentation/components/bottom_sheet_member_detail.dart';

class CardMemberInfo extends StatelessWidget {
  const CardMemberInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(31, 34, 39, 0.08),
              blurRadius: 4.r,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company name and member status
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'บริษัท วีวาสนาดี จำกัด',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0A4C9A),
                    height: 1.51,
                  ),
                ),
                Row(
                  children: [
                    // Green dot indicator
                    Container(
                      width: 8.r,
                      height: 8.r,
                      decoration: const BoxDecoration(
                        color: Color(0xFF16A54D),
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // Member status text
                    Text(
                      'สมาชิก',
                      style: TextStyle(
                        fontFamily: AppFonts.notoSansThai,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF36475A),
                        height: 1.51,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16.h),
            // Report info
            Row(
              children: [
                Text(
                  'รายงานสถานะครั้งถัดไป',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF36475A),
                    height: 1.51,
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/calendar.svg',
                      width: 16.r,
                      height: 16.r,
                      colorFilter: const ColorFilter.mode(
                        Color(0xFF36475A),
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '22 ก.ค 2568',
                      style: TextStyle(
                        fontFamily: AppFonts.notoSansThai,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF36475A),
                        height: 1.51,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Divider(height: 1.h, color: AppColors.borderDefault),
            SizedBox(height: 16.h),
            // More button
            Align(
              alignment: Alignment.centerLeft,
              child: OutlinedButton(
                onPressed: () {
                  // Show bottom sheet member detail
                  // Create sample member data
                  final memberData = MemberDetailData(
                    companyName: 'บริษัท วีวาสนาดี จำกัด',
                    isMember: true,
                    nextReportDate: '22/03/2568',
                    firstRegistrationDate: '22/09/2565',
                    consultantRegistrationNumber: '5703',
                    level: '1',
                    branch: 'ED, ICT',
                    phoneNumber: '************',
                    email: '<EMAIL>',
                  );

                  // Show the bottom sheet
                  BottomSheetMemberDetail.show(context, memberData: memberData);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  minimumSize: Size(0, 24.h),
                  side: BorderSide(color: AppColors.borderPrimary, width: 1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                child: Text(
                  'เพิ่มเติม',
                  style: TextStyle(
                    fontFamily: AppFonts.notoSansThai,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textPrimary,
                    height: 1.5,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
