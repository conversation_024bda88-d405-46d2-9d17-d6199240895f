import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_dimensions.dart';
import 'package:mcdc/core/constants/styles.dart';

/// Data model for dashboard news information
class DashboardNews {
  final String id;
  final String title;
  final String category;
  final String date;
  final VoidCallback? onTap;

  const DashboardNews({
    required this.id,
    required this.title,
    required this.category,
    required this.date,
    this.onTap,
  });
}

/// Section component for displaying news in dashboard
class SectionN<PERSON> extends StatelessWidget {
  final List<DashboardNews> newsList;
  final VoidCallback? onViewAllPressed;

  const SectionNews({
    super.key,
    this.newsList = const [],
    this.onViewAllPressed,
  });

  // Mock data for news - following the Figma design
  static const List<DashboardNews> _mockNews = [
    DashboardNews(
      id: '1',
      title:
          'โครงการสำรวจออกแบบเพื่อก่อสร้าง\nเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8...',
      category: 'ข่าวประกาศ/ข่าวประชาสัมพันธ์',
      date: '29 ม.ค. 2568',
    ),
    DashboardNews(
      id: '2',
      title:
          'โครงการสำรวจออกแบบเพื่อก่อสร้าง\nเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8...',
      category: 'ข่าวประกาศ/ข่าวประชาสัมพันธ์',
      date: '29 ม.ค. 2568',
    ),
    DashboardNews(
      id: '3',
      title:
          'โครงการสำรวจออกแบบเพื่อก่อสร้าง\nเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8...',
      category: 'ข่าวประกาศ/ข่าวประชาสัมพันธ์',
      date: '29 ม.ค. 2568',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final displayNewsList = newsList.isNotEmpty ? newsList : _mockNews;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          _buildSectionHeader(context),
          24.h.verticalSpace,
          _buildNewsList(displayNewsList),
        ],
      ),
    );
  }

  /// Build section header with title and "View All" button
  Widget _buildSectionHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          AppLocalizations.of(context)!.newsAnnouncement,
          style: kHeaderStyle.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
          ),
        ),
        GestureDetector(
          onTap: onViewAllPressed,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                AppLocalizations.of(context)!.allLabel,
                style: kSmallStyle.copyWith(
                  fontSize: 14.sp,
                  color: AppColors.textPrimary,
                ),
              ),
              6.w.horizontalSpace,
              SvgPicture.asset(
                'assets/icons/arrow_right.svg',
                width: 6.w,
                height: 10.h,
                colorFilter: const ColorFilter.mode(
                  AppColors.textPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build list of news cards
  Widget _buildNewsList(List<DashboardNews> displayNewsList) {
    return Column(
      children:
          displayNewsList
              .map(
                (news) => Padding(
                  padding: EdgeInsets.only(bottom: 24.h),
                  child: _NewsCard(news: news),
                ),
              )
              .toList(),
    );
  }
}

/// Individual news card widget
class _NewsCard extends StatelessWidget {
  final DashboardNews news;

  const _NewsCard({required this.news});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: news.onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(AppDimensions.paddingCard.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusCard.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0x141F2227),
              blurRadius: 4,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildNewsTitle(),
            16.h.verticalSpace,
            _buildNewsMetadata(),
          ],
        ),
      ),
    );
  }

  /// Build news title text
  Widget _buildNewsTitle() {
    return Text(
      news.title,
      style: TextStyle(
        fontFamily: 'NotoSansThai',
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Build news metadata (category and date)
  Widget _buildNewsMetadata() {
    return Row(
      children: [_buildCategoryTag(), 16.w.horizontalSpace, _buildDateInfo()],
    );
  }

  /// Build category tag with icon
  Widget _buildCategoryTag() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Tag icon placeholder - using a simple container for now
        SvgPicture.asset(
          'assets/icons/tag.svg',
          width: 16.w,
          height: 16.h,
          colorFilter: const ColorFilter.mode(
            AppColors.textDefaultDark,
            BlendMode.srcIn,
          ),
        ),
        8.w.horizontalSpace,
        Flexible(
          child: Text(
            news.category,
            style: kSmallStyle.copyWith(
              fontSize: 14.sp,
              color: AppColors.textDefaultDark,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Build date info with calendar icon
  Widget _buildDateInfo() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          'assets/icons/calendar.svg',
          width: 16.w,
          height: 16.h,
          colorFilter: const ColorFilter.mode(
            AppColors.textDefaultDark,
            BlendMode.srcIn,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          news.date,
          style: kSmallStyle.copyWith(
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
          ),
        ),
      ],
    );
  }
}
