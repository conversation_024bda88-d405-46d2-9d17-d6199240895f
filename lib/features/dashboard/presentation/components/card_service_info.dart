import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_dimensions.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/styles.dart';

class CardServiceInfo extends StatelessWidget {
  final String title;
  final String banner;
  final bool removeHorizontalMargin;

  const CardServiceInfo({
    super.key,
    required this.title,
    required this.banner,
    this.removeHorizontalMargin = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin:
          removeHorizontalMargin
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 24.w),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusCard.r),
        boxShadow: [
          BoxShadow(
            color: const Color(0x141F2227),
            blurRadius: 4,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(
              AppDimensions.borderRadiusCard.r,
            ),
            child: Image.asset(
              banner,
              width: double.infinity,
              height: 228.h,
              fit: BoxFit.cover,
            ),
          ),
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingCard.w),
            child: Text(
              title,
              style: kTitleStyle.copyWith(
                fontSize: 28.sp,
                color: AppColors.textDefaultDark,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
