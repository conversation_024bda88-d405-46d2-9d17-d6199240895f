import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_dimensions.dart';
import 'package:mcdc/shared/presentation/widgets/button/custom_button.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/styles.dart';

class CardMatchingResult extends StatelessWidget {
  /// Callback function called when the "เพิ่มเติม" button is pressed.
  /// If null, the button will be disabled.
  final VoidCallback? onPressed;

  /// Whether to remove horizontal margins from the card.
  /// When true, the card will take the full width of its parent.
  /// Defaults to false.
  final bool removeHorizontalMargin;

  const CardMatchingResult({
    super.key,
    this.onPressed,
    this.removeHorizontalMargin = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin:
          removeHorizontalMargin
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 24.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusCard.r),
        boxShadow: [
          BoxShadow(
            color: const Color(0x141F2227),
            blurRadius: 4,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(
              AppDimensions.borderRadiusCard.r,
            ),
            child: SizedBox(
              width: double.infinity,
              height: 197.h,
              child: Image.asset(
                'assets/images/card_matching_result.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
          // Content overlay
          Positioned(
            left: 26.w,
            top: 29.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main text content
                Text(
                  AppLocalizations.of(context)!.viewAutomaticConsultantMatching,
                  style: kHeaderStyle.copyWith(
                    fontSize: 20.sp,
                    color: AppColors.textDefaultWhite,
                    fontWeight: FontWeight.w600,
                    height: 1.51.sp,
                  ),
                ),
                8.h.verticalSpace,
                CustomButton(
                  text: AppLocalizations.of(context)!.moreDetailsButton,
                  onPressed: onPressed,
                  height: 24.h,
                  borderRadius: 20.r,
                  textStyle: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textPrimary,
                    height: 1.5.sp,
                  ),
                  backgroundColor: AppColors.backgroundDefault,
                  padding: EdgeInsets.symmetric(horizontal: 4.w),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
