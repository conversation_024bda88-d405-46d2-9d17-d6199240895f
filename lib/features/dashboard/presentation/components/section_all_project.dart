import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_dimensions.dart';
import 'package:mcdc/core/constants/styles.dart';
import 'package:mcdc/core/routes/router.gr.dart';

/// Data model for dashboard project information
class DashboardProject {
  final String id;
  final String title;
  final String date;
  final VoidCallback? onTap;

  const DashboardProject({
    required this.id,
    required this.title,
    required this.date,
    this.onTap,
  });
}

/// Section component for displaying all projects in dashboard
class SectionAllProject extends StatelessWidget {
  final List<DashboardProject> projects;
  final VoidCallback? onViewAllPressed;

  const SectionAllProject({
    super.key,
    required this.projects,
    this.onViewAllPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          _buildSectionHeader(context),
          24.h.verticalSpace,
          _buildProjectList(),
        ],
      ),
    );
  }

  /// Build section header with title and "View All" button
  Widget _buildSectionHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          AppLocalizations.of(context)!.allProjects,
          style: kHeaderStyle.copyWith(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
          ),
        ),
        GestureDetector(
          onTap:
              onViewAllPressed ??
              () {
                context.router.push(const SearchResultProjectRoute());
              },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                AppLocalizations.of(context)!.allLabel,
                style: kSmallStyle.copyWith(
                  fontSize: 14.sp,
                  color: AppColors.textPrimary,
                ),
              ),
              6.w.horizontalSpace,
              SvgPicture.asset(
                'assets/icons/arrow_right.svg',
                width: 6.w,
                height: 10.h,
                colorFilter: const ColorFilter.mode(
                  AppColors.textPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build list of project cards
  Widget _buildProjectList() {
    return Column(
      children:
          projects
              .map(
                (project) => Padding(
                  padding: EdgeInsets.only(bottom: 24.h),
                  child: _ProjectCard(project: project),
                ),
              )
              .toList(),
    );
  }
}

/// Individual project card widget
class _ProjectCard extends StatelessWidget {
  final DashboardProject project;

  const _ProjectCard({required this.project});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: project.onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(AppDimensions.paddingCard.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusCard.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0x141F2227),
              blurRadius: 4,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProjectTitle(),
            16.h.verticalSpace,
            _buildProjectDate(),
          ],
        ),
      ),
    );
  }

  /// Build project title text
  Widget _buildProjectTitle() {
    return Text(
      project.title,
      style: TextStyle(
        fontFamily: 'NotoSansThai',
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textDefaultDark,
        height: 1.5,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Build project date with calendar icon
  Widget _buildProjectDate() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          'assets/icons/calendar.svg',
          width: 16.w,
          height: 16.h,
          colorFilter: const ColorFilter.mode(
            AppColors.textDefaultDark,
            BlendMode.srcIn,
          ),
        ),
        8.w.horizontalSpace,
        Text(
          project.date,
          style: kSmallStyle.copyWith(
            fontSize: 14.sp,
            color: AppColors.textDefaultDark,
          ),
        ),
      ],
    );
  }
}
