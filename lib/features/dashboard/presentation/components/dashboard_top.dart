import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/styles.dart';
import 'package:mcdc/features/dashboard/presentation/components/card_member_info.dart';

class DashboardTop extends StatelessWidget {
  const DashboardTop({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      pinned: false,
      centerTitle: false,
      automaticallyImplyLeading: false,
      actions: const <Widget>[],
      surfaceTintColor: Colors.white,
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(260.h),
        child: Container(),
      ),
      flexibleSpace: Stack(
        children: [
          _buildBackgroundGradient(),
          _buildBackgroundImage(),
          Column(
            children: [
              // Top section with logo and action buttons
              Padding(
                padding: EdgeInsets.fromLTRB(24.w, 60.h, 24.w, 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildLogo(context),
                    const Spacer(),
                    _buildActionButtons(context),
                  ],
                ),
              ),
              // Company info card positioned at the bottom
              SizedBox(height: 24.h),
              const CardMemberInfo(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundGradient() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF006BFF), // From Figma gradient
              Color(0xFFF8F8F8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundImage() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Opacity(
        opacity: 0.8,
        child: SizedBox(
          height: 200.h,
          width: 1.sw,
          child: Image.asset(
            'assets/images/bg_home_top.png',
            width: 1.sw,
            height: 200.h,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  Widget _buildLogo(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: const BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
          ),
          child: Image.asset(
            'assets/images/logo.png',
            width: 32.w,
            height: 32.w,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 8.w),
          child: Text(
            AppLocalizations.of(context)!.consultingCenterName,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultWhite,
              fontFamily: kPageTitleStyle.fontFamily,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        _buildActionButton(
          iconPath: 'assets/icons/message_dots_new.svg',
          onPressed: () => context.router.pushPath('/chat'),
        ),
        SizedBox(width: 8.w),
        _buildActionButton(
          iconPath: 'assets/icons/bell_new.svg',
          onPressed: () => context.router.pushPath('/notification'),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String iconPath,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 40.w,
      height: 32.h,
      decoration: BoxDecoration(color: Colors.transparent),
      child: IconButton(
        onPressed: onPressed,
        iconSize: 24.w,
        icon: SvgPicture.asset(
          iconPath,
          width: 24.r,
          height: 24.r,
          colorFilter: const ColorFilter.mode(
            AppColors.iconWhite,
            BlendMode.srcIn,
          ),
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
}
