import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/styles.dart';
import 'card_service_info.dart';

class SectionServiceInfo extends StatefulWidget {
  const SectionServiceInfo({super.key});

  @override
  State<SectionServiceInfo> createState() => _SectionServiceInfoState();
}

class _SectionServiceInfoState extends State<SectionServiceInfo> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _navigateToServiceInfoPage() {
    context.router.pushPath('/service-info');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 316.h,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // Header with title and "ทั้งหมด" button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context)!.serviceInformation,
                  style: kHeaderStyle.copyWith(
                    fontSize: 20.sp,
                    color: AppColors.textDefaultDark,
                  ),
                ),
                GestureDetector(
                  onTap: _navigateToServiceInfoPage,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.allLabel,
                          style: kSmallStyle,
                        ),
                        SizedBox(width: 4.w),
                        SvgPicture.asset(
                          'assets/icons/arrow_right.svg',
                          width: 16.w,
                          height: 16.h,
                          colorFilter: const ColorFilter.mode(
                            AppColors.textPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Sliding content
          24.verticalSpace,
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              children: [
                CardServiceInfo(
                  title: AppLocalizations.of(context)!.satisfactionReport,
                  banner: 'assets/mock/service_info_banner1.png',
                ),
                CardServiceInfo(
                  title: AppLocalizations.of(context)!.consultantReport,
                  banner: 'assets/mock/service_info_banner2.png',
                ),
                CardServiceInfo(
                  title: AppLocalizations.of(context)!.consultantProjectReport,
                  banner: 'assets/mock/service_info_banner3.png',
                ),
              ],
            ),
          ),
          24.verticalSpace,
          // Page Indicator
          _buildPageIndicator(),
        ],
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: index == _currentPage ? 28.w : 10.w,
          height: 10.h,
          decoration: BoxDecoration(
            color:
                index == _currentPage
                    ? AppColors.surfacePrimary
                    : AppColors.borderDefault,
            borderRadius: BorderRadius.circular(5.r),
          ),
        );
      }),
    );
  }
}
