import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';

/// Data model for member detail information
///
/// Contains all the necessary information to display in the member detail bottom sheet.
/// This includes company information, registration details, contact information, and callbacks.
class MemberDetailData {
  final String companyName;
  final bool isMember;
  final String nextReportDate;
  final String firstRegistrationDate;
  final String consultantRegistrationNumber;
  final String level;
  final String branch;
  final String phoneNumber;
  final String email;

  const MemberDetailData({
    required this.companyName,
    required this.isMember,
    required this.nextReportDate,
    required this.firstRegistrationDate,
    required this.consultantRegistrationNumber,
    required this.level,
    required this.branch,
    required this.phoneNumber,
    required this.email,
  });
}

class BottomSheetMemberDetail extends StatelessWidget {
  final MemberDetailData memberData;

  const BottomSheetMemberDetail({super.key, required this.memberData});

  /// Shows a modal bottom sheet with member detail content
  static Future<void> show(
    BuildContext context, {
    required MemberDetailData memberData,
  }) async {
    await showMaterialModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      backgroundColor: Colors.white,
      builder: (context) => BottomSheetMemberDetail(memberData: memberData),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          Flexible(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildMemberInfo(),
                    SizedBox(height: 32.h),
                    _buildActionButton(context),
                    SizedBox(height: 24.h),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'รายละเอียด',
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: SvgPicture.asset(
                'assets/icons/x_close.svg',
                width: 24.w,
                height: 24.h,
                colorFilter: const ColorFilter.mode(
                  AppColors.iconDefault,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Company name and member status
        _buildCompanyHeader(),
        SizedBox(height: 24.h),

        // Divider
        Container(
          height: 1.h,
          width: double.infinity,
          color: AppColors.borderDefault,
        ),
        SizedBox(height: 24.h),

        // Information rows
        _buildInfoRow('รายงานสถานะครั้งถัดไป', memberData.nextReportDate),
        SizedBox(height: 24.h),
        _buildInfoRow(
          'วันที่ขึ้นทะเบียนครั้งแรก',
          memberData.firstRegistrationDate,
        ),
        SizedBox(height: 24.h),
        _buildInfoRow(
          'เลขทะเบียนที่ปรึกษา',
          memberData.consultantRegistrationNumber,
        ),
        SizedBox(height: 24.h),
        _buildInfoRow('ระดับ', memberData.level),
        SizedBox(height: 24.h),
        _buildInfoRow('สาขา', memberData.branch),
        SizedBox(height: 24.h),
        _buildInfoRow('เบอร์โทร', memberData.phoneNumber),
        SizedBox(height: 24.h),
        _buildInfoRow('อีเมล', memberData.email),
      ],
    );
  }

  Widget _buildCompanyHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          memberData.companyName,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.51,
          ),
        ),
        SizedBox(height: 8.h),
        if (memberData.isMember)
          Row(
            children: [
              // Green dot indicator
              Container(
                width: 10.w,
                height: 10.h,
                decoration: BoxDecoration(
                  color: const Color(0xFF16A54D),
                  borderRadius: BorderRadius.circular(5.r),
                ),
              ),
              SizedBox(width: 8.w),
              // Member status text
              Text(
                'สมาชิก',
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                  height: 1.51,
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.51,
          ),
        ),
        8.w.horizontalSpace,
        Expanded(
          flex: 1,
          child: Text(
            value,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textDefaultDark,
              height: 1.51,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return PrimaryButton(
      text: 'เพิ่มเติม',
      width: double.infinity,
      height: 40.h,
      borderRadius: 20.r,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
      onPressed: () => context.router.push(const ConsultantInfoRoute()),
    );
  }
}
