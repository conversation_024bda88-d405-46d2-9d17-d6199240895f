import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/styles.dart';

class InformationCard extends StatelessWidget {
  final String companyName;
  final String memberStatus;
  final String reportType;
  final String date;
  final VoidCallback? onTapMore;

  const InformationCard({
    Key? key,
    required this.companyName,
    required this.memberStatus,
    required this.reportType,
    required this.date,
    this.onTapMore,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      color: AppColors.surfaceDefault,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(companyName, style: kCardTopicStyle.copyWith(fontSize: 18.sp)),
            SizedBox(height: 8.h),
            _buildMemberStatus(),
            SizedBox(height: 16.h),
            Divider(color: AppColors.dividerColor, height: 1.h),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(child: _buildReportInfo()),
                _buildMoreButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberStatus() {
    return Row(
      children: [
        Container(
          width: 8.r,
          height: 8.r,
          decoration: const BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 8.w),
        Text(memberStatus, style: kCardValueStyle),
      ],
    );
  }

  Widget _buildReportInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(reportType, style: kNormalLabelStyle),
        SizedBox(height: 4.h),
        Row(
          children: [
            SvgPicture.asset(
              'assets/icons/calendar.svg',
              width: 16.r,
              height: 16.r,
              colorFilter: const ColorFilter.mode(
                AppColors.iconSubdude,
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 8.w),
            Text(date, style: kDateStyle),
          ],
        ),
      ],
    );
  }

  Widget _buildMoreButton() {
    return TextButton(
      onPressed: onTapMore,
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
          side: BorderSide(color: AppColors.buttonPrimary, width: 1.w),
        ),
        backgroundColor: Colors.transparent,
      ),
      child: Text(
        'เพิ่มเติม',
        style: kBodyStyle.copyWith(
          color: AppColors.buttonPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
