import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/presentation/widgets/appbar/app_bar_common.dart';
import '../../../shared/presentation/widgets/input/custom_dropdown_field.dart';
import '../domain/entities/service_category.dart';
import '../domain/entities/service_info.dart';
import 'components/card_service_info.dart';

@RoutePage()
class ServiceInfoPage extends StatefulWidget {
  const ServiceInfoPage({super.key});

  @override
  State<ServiceInfoPage> createState() => _ServiceInfoPageState();
}

class _ServiceInfoPageState extends State<ServiceInfoPage> {
  ServiceCategory? _selectedCategory;

  // Mock data for service categories
  final List<ServiceCategory> _categories = [
    const ServiceCategory(id: 'all', name: 'ทั้งหมด'),
    const ServiceCategory(id: 'satisfaction', name: 'รายงานความพึงพอใจ'),
    const ServiceCategory(id: 'consultant', name: 'รายงานที่ปรึกษา'),
    const ServiceCategory(id: 'project', name: 'รายงานโครงการ'),
  ];

  // Mock data for service information
  final List<ServiceInfo> _allServiceData = [
    const ServiceInfo(
      id: '1',
      title: 'รายงานความพึงพอใจ\nผู้ใช้งานเว็บไซต์',
      banner: 'assets/mock/service_info_banner1.png',
      categoryId: 'satisfaction',
    ),
    const ServiceInfo(
      id: '2',
      title: 'รายงานที่ปรึกษา\nศูนย์ข้อมูลที่ปรึกษา',
      banner: 'assets/mock/service_info_banner2.png',
      categoryId: 'consultant',
    ),
    const ServiceInfo(
      id: '3',
      title: 'รายงานข้อมูลที่ปรึกษา\nเข้าร่วมโครงการ',
      banner: 'assets/mock/service_info_banner3.png',
      categoryId: 'project',
    ),
    const ServiceInfo(
      id: '4',
      title: 'รายงานประสิทธิภาพ\nการให้บริการ',
      banner: 'assets/mock/service_info_banner1.png',
      categoryId: 'satisfaction',
    ),
    const ServiceInfo(
      id: '5',
      title: 'รายงานสถิติ\nที่ปรึกษาทั่วประเทศ',
      banner: 'assets/mock/service_info_banner2.png',
      categoryId: 'consultant',
    ),
  ];

  @override
  void initState() {
    super.initState();
    // Set default category to "ทั้งหมด"
    _selectedCategory = _categories.first;
  }

  /// Get filtered service data based on selected category
  List<ServiceInfo> get _filteredServiceData {
    if (_selectedCategory?.id == 'all') {
      return _allServiceData;
    }
    return _allServiceData
        .where((service) => service.categoryId == _selectedCategory!.id)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.surfaceDefault,
      appBar: const AppBarCommon(title: 'ข้อมูลการให้บริการ'),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category Dropdown
              CustomDropdownField<ServiceCategory>(
                label: 'หมวดหมู่',
                hintText: 'เลือกหมวดหมู่',
                selectedValue: _selectedCategory,
                items: _categories,
                displayText: (category) => category.name,
                onChange: (ServiceCategory category) {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
              ),
              24.h.verticalSpace,
              // Service Info Cards
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _filteredServiceData.length,
                separatorBuilder: (context, index) => SizedBox(height: 24.h),
                itemBuilder: (context, index) {
                  final serviceData = _filteredServiceData[index];
                  return CardServiceInfo(
                    title: serviceData.title,
                    banner: serviceData.banner,
                    removeHorizontalMargin: true,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
