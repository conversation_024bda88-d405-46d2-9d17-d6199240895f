import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_dimensions.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/features/dashboard/presentation/components/card_matching_result.dart';
import 'package:mcdc/features/dashboard/presentation/components/section_service_info.dart';
import 'package:mcdc/features/dashboard/presentation/components/section_all_project.dart';
import 'package:mcdc/features/dashboard/presentation/components/section_news.dart';
import 'package:mcdc/shared/presentation/widgets/background/main_back_ground.dart';
import 'package:mcdc/features/dashboard/presentation/components/consulting_center_overview.dart';
import 'package:mcdc/features/dashboard/presentation/components/dashboard_top.dart';

@RoutePage()
class DashboardHomePage extends StatefulWidget {
  const DashboardHomePage({super.key});

  @override
  State<DashboardHomePage> createState() => _DashboardHomePageState();
}

class _DashboardHomePageState extends State<DashboardHomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'DashboardHomePage',
  );

  // Mock data for dashboard projects
  final List<DashboardProject> _mockProjects = [
    const DashboardProject(
      id: '1',
      title:
          'โครงการสำรวจออกแบบเพื่อก่อสร้าง\nเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8...',
      date: '29 ม.ค. 2568',
    ),
    const DashboardProject(
      id: '2',
      title:
          'โครงการสำรวจออกแบบเพื่อก่อสร้าง\nเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8...',
      date: '29 ม.ค. 2568',
    ),
    const DashboardProject(
      id: '3',
      title:
          'โครงการสำรวจออกแบบเพื่อก่อสร้าง\nเขื่อนป้องกันตลิ่งพังที่บ้านใหม่ หมู่ที่ 3-8...',
      date: '29 ม.ค. 2568',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.backgroundColorSecondary,
      body: MainBackground(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            const DashboardTop(),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: const ConsultingCenterOverview(),
              ),
            ),
            SliverToBoxAdapter(child: const SectionServiceInfo()),
            SliverPadding(
              padding: EdgeInsets.symmetric(
                vertical: AppDimensions.paddingCard,
              ),
              sliver: SliverToBoxAdapter(child: const CardMatchingResult()),
            ),
            // Section all project
            SliverPadding(
              padding: EdgeInsets.only(bottom: AppDimensions.paddingCard),
              sliver: SliverToBoxAdapter(
                child: SectionAllProject(
                  projects: _mockProjects,
                  onViewAllPressed: () {
                    context.router.push(const SearchResultProjectRoute());
                  },
                ),
              ),
            ),
            // Section News
            SliverPadding(
              padding: EdgeInsets.only(bottom: AppDimensions.paddingCard),
              sliver: SliverToBoxAdapter(
                child: SectionNews(
                  onViewAllPressed: () {
                    // TODO: Navigate to news list page when available
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
