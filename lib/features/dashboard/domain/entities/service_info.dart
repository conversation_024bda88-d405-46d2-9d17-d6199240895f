/// Domain entity for service information
class ServiceInfo {
  final String id;
  final String title;
  final String banner;
  final String categoryId;

  const ServiceInfo({
    required this.id,
    required this.title,
    required this.banner,
    required this.categoryId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceInfo &&
        other.id == id &&
        other.title == title &&
        other.banner == banner &&
        other.categoryId == categoryId;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ banner.hashCode ^ categoryId.hashCode;
  }

  @override
  String toString() {
    return 'ServiceInfo(id: $id, title: $title, banner: $banner, categoryId: $categoryId)';
  }
}
