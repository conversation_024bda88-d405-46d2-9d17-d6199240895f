/// Domain entity for service category
class ServiceCategory {
  final String id;
  final String name;

  const ServiceCategory({
    required this.id,
    required this.name,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceCategory && other.id == id && other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;

  @override
  String toString() => 'ServiceCategory(id: $id, name: $name)';
}
