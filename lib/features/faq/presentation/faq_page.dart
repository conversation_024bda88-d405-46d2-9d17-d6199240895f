import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';

import '../domain/entities/faq.dart';
import 'components/faq_item.dart';

@RoutePage()
class FaqPage extends StatefulWidget {
  const FaqPage({super.key});

  @override
  State<FaqPage> createState() => _FaqPageState();
}

class _FaqPageState extends State<FaqPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  // Tab categories
  final List<String> _tabTitles = [
    'ทั้งหมด',
    'วิธีการคำนวณ',
    'การใช้งานระบบ',
    'ระบบจับคู่',
    'โครงการ',
    'ข่าวประกาศ',
  ];

  // FAQ data state
  late List<Faq> _allFaqs;
  late Map<String, List<Faq>> _groupedFaqs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabTitles.length, vsync: this);
    _tabController.addListener(() {
      if (_tabController.index != _currentTabIndex) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
    _initializeMockData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeMockData() {
    _allFaqs = [
      const Faq(
        id: '1',
        question: 'สามารถดูวิธีการคำนวณระบบจับคู่ที่ปรึกษา ได้อย่างไร',
        answer:
            'ท่านสามารถดาวน์โหลดวิธีการคำนวณระบบจับคู่ที่ปรึกษา ได้ที่นี่ คลิก',
        category: 'วิธีการคำนวณ',
        order: 1,
      ),
      const Faq(
        id: '2',
        question: 'พบที่ปรึกษาที่ใช่ อย่างไร',
        answer:
            'ระบบจะจับคู่ที่ปรึกษาที่เหมาะสมกับความต้องการของท่านโดยอัตโนมัติ ท่านสามารถดูรายละเอียดและเลือกที่ปรึกษาที่ต้องการได้',
        category: 'ระบบจับคู่',
        order: 2,
      ),
      const Faq(
        id: '3',
        question: 'สามารถสร้างโครงการ ได้อย่างไร ?',
        answer:
            'ท่านสามารถสร้างโครงการใหม่ได้โดยไปที่หน้าโครงการ และกดปุ่ม "สร้างโครงการใหม่" จากนั้นกรอกข้อมูลโครงการให้ครบถ้วน',
        category: 'โครงการ',
        order: 3,
      ),
      const Faq(
        id: '4',
        question: 'การสมัครสมาชิก ทำอย่างไร?',
        answer:
            'ท่านสามารถสมัครสมาชิกได้โดยกดปุ่ม "สมัครสมาชิก" ที่หน้าแรก จากนั้นกรอกข้อมูลส่วนตัวและยืนยันอีเมล',
        category: 'การใช้งานระบบ',
        order: 4,
      ),
      const Faq(
        id: '5',
        question: 'ข่าวประกาศล่าสุดดูได้ที่ไหน?',
        answer:
            'ท่านสามารถดูข่าวประกาศล่าสุดได้ที่หน้าข่าวประกาศ หรือรับการแจ้งเตือนผ่านระบบแจ้งเตือน',
        category: 'ข่าวประกาศ',
        order: 5,
      ),
    ];
    _groupFaqsByCategory();
  }

  void _groupFaqsByCategory() {
    _groupedFaqs = {};

    for (final category in _tabTitles) {
      if (category == 'ทั้งหมด') {
        _groupedFaqs[category] = List.from(_allFaqs);
      } else {
        _groupedFaqs[category] =
            _allFaqs.where((faq) => faq.category == category).toList();
      }

      // Sort by order
      _groupedFaqs[category]?.sort((a, b) => a.order.compareTo(b.order));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: const AppBarCommon(title: 'คำถามที่พบบ่อย'),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: List.generate(
                _tabTitles.length,
                (index) => _buildFaqList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.backgroundDefault,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      child: SizedBox(
        height: 48.h,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          itemCount: _tabTitles.length,
          separatorBuilder: (context, index) => 24.w.horizontalSpace,
          itemBuilder: (context, index) {
            final isSelected = _currentTabIndex == index;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _currentTabIndex = index;
                });
                _tabController.animateTo(index);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? AppColors.surfacePrimarySubdude
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(50.r),
                ),
                child: Center(
                  child: Text(
                    _tabTitles[index],
                    style: TextStyle(
                      fontFamily: AppFonts.notoSansThai,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      height: 1.5,
                      color:
                          isSelected
                              ? AppColors.textPrimary
                              : AppColors.textSubdude,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFaqList() {
    // Rebuild grouped FAQs when tab changes
    _groupFaqsByCategory();

    final currentCategory = _tabTitles[_currentTabIndex];
    final faqs = _groupedFaqs[currentCategory] ?? [];

    if (faqs.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return FaqItem(
          faq: faq,
          onExpansionChanged:
              (isExpanded) => _handleExpansionChanged(faq.id, isExpanded),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Text(
          'ไม่มีคำถามในหมวดหมู่นี้',
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.textSubdude,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  void _handleExpansionChanged(String faqId, bool isExpanded) {
    setState(() {
      final faqIndex = _allFaqs.indexWhere((faq) => faq.id == faqId);
      if (faqIndex != -1) {
        _allFaqs[faqIndex] = _allFaqs[faqIndex].copyWith(
          isExpanded: isExpanded,
        );
      }
    });
  }
}
