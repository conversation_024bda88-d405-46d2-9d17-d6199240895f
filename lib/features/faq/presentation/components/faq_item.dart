import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';

import '../../domain/entities/faq.dart';

/// A widget that displays a single FAQ item with expandable content
class FaqItem extends StatefulWidget {
  final Faq faq;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onExpansionChanged;

  const FaqItem({
    super.key,
    required this.faq,
    this.onTap,
    this.onExpansionChanged,
  });

  @override
  State<FaqItem> createState() => _FaqItemState();
}

class _FaqItemState extends State<FaqItem> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _iconRotationAnimation;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.faq.isExpanded;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _iconRotationAnimation = Tween<double>(begin: 0.0, end: 0.25).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    widget.onExpansionChanged?.call(_isExpanded);
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: _isExpanded ? AppColors.surfaceDefault : Colors.transparent,
        borderRadius: BorderRadius.circular(20.r),
      ),
      margin: EdgeInsets.only(bottom: 16.h),
      child: Column(children: [_buildQuestionSection(), _buildAnswerSection()]),
    );
  }

  Widget _buildQuestionSection() {
    return GestureDetector(
      onTap: _toggleExpansion,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: AppColors.surfacePrimary,
          borderRadius:
              _isExpanded
                  ? BorderRadius.all(Radius.circular(20.r))
                  : BorderRadius.circular(20.r),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                widget.faq.question,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDefaultWhite,
                  height: 1.5,
                ),
              ),
            ),
            SizedBox(width: 16.w),
            _buildExpandIcon(),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandIcon() {
    return AnimatedBuilder(
      animation: _iconRotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle:
              _iconRotationAnimation.value * 2 * 3.14159, // Convert to radians
          child: SvgPicture.asset(
            'assets/icons/arrow_right.svg',
            width: 16.r,
            height: 16.r,
            colorFilter: const ColorFilter.mode(
              AppColors.iconWhite,
              BlendMode.srcIn,
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnswerSection() {
    return AnimatedBuilder(
      animation: _expandAnimation,
      builder: (context, child) {
        return ClipRect(
          child: Align(
            alignment: Alignment.topCenter,
            heightFactor: _expandAnimation.value,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.surfaceDefault,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20.r),
                  bottomRight: Radius.circular(20.r),
                ),
              ),
              child: Text(
                widget.faq.answer,
                style: TextStyle(
                  fontFamily: AppFonts.notoSansThai,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
