import 'package:equatable/equatable.dart';

/// FAQ entity representing a frequently asked question item
class Faq extends Equatable {
  final String id;
  final String question;
  final String answer;
  final String category;
  final int order;
  final bool isExpanded;

  const Faq({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
    required this.order,
    this.isExpanded = false,
  });

  @override
  List<Object?> get props => [id, question, answer, category, order, isExpanded];

  Faq copyWith({
    String? id,
    String? question,
    String? answer,
    String? category,
    int? order,
    bool? isExpanded,
  }) {
    return Faq(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      category: category ?? this.category,
      order: order ?? this.order,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}
