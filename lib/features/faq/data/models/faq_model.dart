import '../../domain/entities/faq.dart';

/// FAQ data model for data layer operations
class FaqModel extends Faq {
  const FaqModel({
    required super.id,
    required super.question,
    required super.answer,
    required super.category,
    required super.order,
    super.isExpanded,
  });

  /// Creates a [FaqModel] from JSON
  factory FaqModel.fromJson(Map<String, dynamic> json) {
    return FaqModel(
      id: json['id'] as String,
      question: json['question'] as String,
      answer: json['answer'] as String,
      category: json['category'] as String,
      order: json['order'] as int,
      isExpanded: json['isExpanded'] as bool? ?? false,
    );
  }

  /// Converts [FaqModel] to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'answer': answer,
      'category': category,
      'order': order,
      'isExpanded': isExpanded,
    };
  }

  /// Creates a [FaqModel] from [Faq] entity
  factory FaqModel.fromEntity(Faq faq) {
    return FaqModel(
      id: faq.id,
      question: faq.question,
      answer: faq.answer,
      category: faq.category,
      order: faq.order,
      isExpanded: faq.isExpanded,
    );
  }

  /// Converts [FaqModel] to [Faq] entity
  Faq toEntity() {
    return Faq(
      id: id,
      question: question,
      answer: answer,
      category: category,
      order: order,
      isExpanded: isExpanded,
    );
  }
}
