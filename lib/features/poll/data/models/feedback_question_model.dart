import '../../domain/entities/feedback_question.dart';

/// Feedback question data model for data layer operations
class FeedbackQuestionModel extends FeedbackQuestion {
  const FeedbackQuestionModel({
    required super.id,
    required super.question,
    required super.order,
    super.selectedRating,
  });

  /// Creates a [FeedbackQuestionModel] from JSON
  factory FeedbackQuestionModel.fromJson(Map<String, dynamic> json) {
    return FeedbackQuestionModel(
      id: json['id'] as String,
      question: json['question'] as String,
      order: json['order'] as int,
      selectedRating: json['selectedRating'] as int?,
    );
  }

  /// Converts [FeedbackQuestionModel] to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'order': order,
      'selectedRating': selectedRating,
    };
  }

  /// Creates a [FeedbackQuestionModel] from [FeedbackQuestion] entity
  factory FeedbackQuestionModel.fromEntity(FeedbackQuestion question) {
    return FeedbackQuestionModel(
      id: question.id,
      question: question.question,
      order: question.order,
      selectedRating: question.selectedRating,
    );
  }

  /// Converts [FeedbackQuestionModel] to [FeedbackQuestion] entity
  FeedbackQuestion toEntity() {
    return FeedbackQuestion(
      id: id,
      question: question,
      order: order,
      selectedRating: selectedRating,
    );
  }

  @override
  FeedbackQuestionModel copyWith({
    String? id,
    String? question,
    int? order,
    int? selectedRating,
  }) {
    return FeedbackQuestionModel(
      id: id ?? this.id,
      question: question ?? this.question,
      order: order ?? this.order,
      selectedRating: selectedRating ?? this.selectedRating,
    );
  }
}
