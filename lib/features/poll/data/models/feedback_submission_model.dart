import '../../domain/entities/feedback_submission.dart';

/// Feedback submission data model for data layer operations
class FeedbackSubmissionModel extends FeedbackSubmission {
  const FeedbackSubmissionModel({
    required super.answers,
    super.suggestions,
    super.submittedAt,
  });

  /// Creates a [FeedbackSubmissionModel] from JSON
  factory FeedbackSubmissionModel.fromJson(Map<String, dynamic> json) {
    return FeedbackSubmissionModel(
      answers: Map<String, int>.from(json['answers'] as Map),
      suggestions: json['suggestions'] as String?,
      submittedAt: json['submittedAt'] != null
          ? DateTime.parse(json['submittedAt'] as String)
          : null,
    );
  }

  /// Converts [FeedbackSubmissionModel] to JSON
  Map<String, dynamic> toJson() {
    return {
      'answers': answers,
      'suggestions': suggestions,
      'submittedAt': submittedAt?.toIso8601String(),
    };
  }

  /// Creates a [FeedbackSubmissionModel] from [FeedbackSubmission] entity
  factory FeedbackSubmissionModel.fromEntity(FeedbackSubmission submission) {
    return FeedbackSubmissionModel(
      answers: submission.answers,
      suggestions: submission.suggestions,
      submittedAt: submission.submittedAt,
    );
  }

  /// Converts [FeedbackSubmissionModel] to [FeedbackSubmission] entity
  FeedbackSubmission toEntity() {
    return FeedbackSubmission(
      answers: answers,
      suggestions: suggestions,
      submittedAt: submittedAt,
    );
  }

  @override
  FeedbackSubmissionModel copyWith({
    Map<String, int>? answers,
    String? suggestions,
    DateTime? submittedAt,
  }) {
    return FeedbackSubmissionModel(
      answers: answers ?? this.answers,
      suggestions: suggestions ?? this.suggestions,
      submittedAt: submittedAt ?? this.submittedAt,
    );
  }
}
