import '../models/feedback_question_model.dart';
import '../models/feedback_submission_model.dart';

/// Local data source for feedback operations
abstract class FeedbackLocalDataSource {
  /// Get feedback questions from local storage
  Future<List<FeedbackQuestionModel>> getFeedbackQuestions();

  /// Submit feedback to local storage
  Future<void> submitFeedback(FeedbackSubmissionModel submission);
}

/// Implementation of [FeedbackLocalDataSource] with mock data
class FeedbackLocalDataSourceImpl implements FeedbackLocalDataSource {
  @override
  Future<List<FeedbackQuestionModel>> getFeedbackQuestions() async {
    // Mock data - in real app this would come from API
    await Future.delayed(const Duration(milliseconds: 500));

    return [
      const FeedbackQuestionModel(
        id: '1',
        question: 'ท่านพึงพอใจต่อการใช้งานแอปพลิเคชันนี้ในระดับใด',
        order: 1,
      ),
      const FeedbackQuestionModel(
        id: '2',
        question: 'แอปพลิเคชันนี้ตอบโจทย์ความต้องการของท่านหรือไม่',
        order: 2,
      ),
      const FeedbackQuestionModel(
        id: '3',
        question: 'การใช้งานเมนูต่าง ๆ ในแอปพลิเคชันง่าย และสะดวกหรือไม่',
        order: 3,
      ),
      const FeedbackQuestionModel(
        id: '4',
        question: 'ท่านพึงพอใจต่อการออกแบบหน้าจอของแอปพลิเคชันหรือไม่',
        order: 4,
      ),
      const FeedbackQuestionModel(
        id: '5',
        question: 'ท่านจะแนะนำแอปพลิเคชันนี้ให้ผู้อื่นหรือไม่',
        order: 5,
      ),
    ];
  }

  @override
  Future<void> submitFeedback(FeedbackSubmissionModel submission) async {
    // Mock submission - in real app this would send to API
    await Future.delayed(const Duration(milliseconds: 1000));

    // Simulate successful submission
    // In real app, this would send to API
  }
}
