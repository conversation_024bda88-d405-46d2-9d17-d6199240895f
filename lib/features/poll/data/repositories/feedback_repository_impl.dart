import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import '../../domain/entities/feedback_question.dart';
import '../../domain/entities/feedback_submission.dart';
import '../../domain/repositories/feedback_repository.dart';
import '../datasources/feedback_local_datasource.dart';
import '../models/feedback_submission_model.dart';

/// Implementation of [FeedbackRepository]
class FeedbackRepositoryImpl implements FeedbackRepository {
  final FeedbackLocalDataSource localDataSource;

  const FeedbackRepositoryImpl({
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, List<FeedbackQuestion>>> getFeedbackQuestions() async {
    try {
      final questions = await localDataSource.getFeedbackQuestions();
      return Right(questions.map((model) => model.toEntity()).toList());
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> submitFeedback(FeedbackSubmission submission) async {
    try {
      final model = FeedbackSubmissionModel.fromEntity(submission);
      await localDataSource.submitFeedback(model);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
