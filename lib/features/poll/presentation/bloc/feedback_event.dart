import 'package:equatable/equatable.dart';

/// Base class for feedback events
abstract class FeedbackEvent extends Equatable {
  const FeedbackEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load feedback questions
class LoadFeedbackQuestions extends FeedbackEvent {
  const LoadFeedbackQuestions();
}

/// Event to update rating for a question
class UpdateQuestionRating extends FeedbackEvent {
  final String questionId;
  final int rating;

  const UpdateQuestionRating({
    required this.questionId,
    required this.rating,
  });

  @override
  List<Object?> get props => [questionId, rating];
}

/// Event to update suggestions
class UpdateSuggestions extends FeedbackEvent {
  final String suggestions;

  const UpdateSuggestions({required this.suggestions});

  @override
  List<Object?> get props => [suggestions];
}

/// Event to submit feedback
class SubmitFeedbackEvent extends FeedbackEvent {
  const SubmitFeedbackEvent();
}

/// Event to reset form
class ResetFeedbackForm extends FeedbackEvent {
  const ResetFeedbackForm();
}
