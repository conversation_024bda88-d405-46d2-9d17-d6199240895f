import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../../domain/entities/feedback_submission.dart';
import '../../domain/usecases/get_feedback_questions.dart';
import '../../domain/usecases/submit_feedback.dart';
import 'feedback_event.dart';
import 'feedback_state.dart';

/// BLoC for managing feedback form state
class FeedbackBloc extends Bloc<FeedbackEvent, FeedbackState> {
  final GetFeedbackQuestions getFeedbackQuestions;
  final SubmitFeedback submitFeedback;

  FeedbackBloc({
    required this.getFeedbackQuestions,
    required this.submitFeedback,
  }) : super(const FeedbackInitial()) {
    on<LoadFeedbackQuestions>(_onLoadFeedbackQuestions);
    on<UpdateQuestionRating>(_onUpdateQuestionRating);
    on<UpdateSuggestions>(_onUpdateSuggestions);
    on<SubmitFeedbackEvent>(_onSubmitFeedback);
    on<ResetFeedbackForm>(_onResetFeedbackForm);
  }

  Future<void> _onLoadFeedbackQuestions(
    LoadFeedbackQuestions event,
    Emitter<FeedbackState> emit,
  ) async {
    emit(const FeedbackLoading());

    final result = await getFeedbackQuestions(NoParams());
    result.fold(
      (failure) => emit(FeedbackError(message: failure.message)),
      (questions) => emit(FeedbackQuestionsLoaded(questions: questions)),
    );
  }

  void _onUpdateQuestionRating(
    UpdateQuestionRating event,
    Emitter<FeedbackState> emit,
  ) {
    if (state is FeedbackQuestionsLoaded) {
      final currentState = state as FeedbackQuestionsLoaded;
      final updatedQuestions =
          currentState.questions.map((question) {
            if (question.id == event.questionId) {
              return question.copyWith(selectedRating: event.rating);
            }
            return question;
          }).toList();

      emit(currentState.copyWith(questions: updatedQuestions));
    }
  }

  void _onUpdateSuggestions(
    UpdateSuggestions event,
    Emitter<FeedbackState> emit,
  ) {
    if (state is FeedbackQuestionsLoaded) {
      final currentState = state as FeedbackQuestionsLoaded;
      emit(currentState.copyWith(suggestions: event.suggestions));
    }
  }

  Future<void> _onSubmitFeedback(
    SubmitFeedbackEvent event,
    Emitter<FeedbackState> emit,
  ) async {
    if (state is FeedbackQuestionsLoaded) {
      final currentState = state as FeedbackQuestionsLoaded;

      if (!currentState.isFormValid) {
        emit(const FeedbackError(message: 'กรุณาตอบคำถามให้ครบทุกข้อ'));
        return;
      }

      emit(currentState.copyWith(isSubmitting: true));

      // Create answers map
      final answers = <String, int>{};
      for (final question in currentState.questions) {
        if (question.selectedRating != null) {
          answers[question.id] = question.selectedRating!;
        }
      }

      final submission = FeedbackSubmission(
        answers: answers,
        suggestions:
            currentState.suggestions.isNotEmpty
                ? currentState.suggestions
                : null,
        submittedAt: DateTime.now(),
      );

      final result = await submitFeedback(
        SubmitFeedbackParams(submission: submission),
      );

      result.fold(
        (failure) => emit(FeedbackError(message: failure.message)),
        (_) => emit(const FeedbackSubmitted()),
      );
    }
  }

  void _onResetFeedbackForm(
    ResetFeedbackForm event,
    Emitter<FeedbackState> emit,
  ) {
    emit(const FeedbackInitial());
  }
}
