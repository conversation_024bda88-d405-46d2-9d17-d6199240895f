import 'package:equatable/equatable.dart';
import '../../domain/entities/feedback_question.dart';

/// Base class for feedback states
abstract class FeedbackState extends Equatable {
  const FeedbackState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class FeedbackInitial extends FeedbackState {
  const FeedbackInitial();
}

/// Loading state
class FeedbackLoading extends FeedbackState {
  const FeedbackLoading();
}

/// State when questions are loaded
class FeedbackQuestionsLoaded extends FeedbackState {
  final List<FeedbackQuestion> questions;
  final String suggestions;
  final bool isSubmitting;

  const FeedbackQuestionsLoaded({
    required this.questions,
    this.suggestions = '',
    this.isSubmitting = false,
  });

  FeedbackQuestionsLoaded copyWith({
    List<FeedbackQuestion>? questions,
    String? suggestions,
    bool? isSubmitting,
  }) {
    return FeedbackQuestionsLoaded(
      questions: questions ?? this.questions,
      suggestions: suggestions ?? this.suggestions,
      isSubmitting: isSubmitting ?? this.isSubmitting,
    );
  }

  /// Check if all questions are answered
  bool get isFormValid {
    return questions.every((question) => question.selectedRating != null);
  }

  @override
  List<Object?> get props => [questions, suggestions, isSubmitting];
}

/// State when feedback is submitted successfully
class FeedbackSubmitted extends FeedbackState {
  const FeedbackSubmitted();
}

/// Error state
class FeedbackError extends FeedbackState {
  final String message;

  const FeedbackError({required this.message});

  @override
  List<Object?> get props => [message];
}
