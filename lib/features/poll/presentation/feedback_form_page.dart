import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_confirm.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_success.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_radio_button.dart';
import 'package:mcdc/shared/presentation/widgets/loading/loading_indicator.dart';
import 'package:mcdc/features/poll/domain/entities/feedback_question.dart';
import 'bloc/feedback_bloc.dart';
import 'bloc/feedback_event.dart';
import 'bloc/feedback_state.dart';

@RoutePage()
class FeedbackFormPage extends StatefulWidget {
  const FeedbackFormPage({super.key});

  @override
  State<FeedbackFormPage> createState() => _FeedbackFormPageState();
}

class _FeedbackFormPageState extends State<FeedbackFormPage> {
  late final TextEditingController _suggestionsController;
  late final FeedbackBloc _feedbackBloc;

  @override
  void initState() {
    super.initState();
    _suggestionsController = TextEditingController();
    _feedbackBloc = AppInjector.sl<FeedbackBloc>();
    _feedbackBloc.add(const LoadFeedbackQuestions());
  }

  @override
  void dispose() {
    _suggestionsController.dispose();
    _feedbackBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocProvider.value(
      value: _feedbackBloc,
      child: Scaffold(
        backgroundColor: AppColors.surfaceDefault,
        appBar: AppBarCommon(title: l10n.satisfactionSurvey),
        body: SafeArea(
          child: BlocConsumer<FeedbackBloc, FeedbackState>(
            listener: (context, state) {
              if (state is FeedbackSubmitted) {
                _showSuccessDialog(context, l10n);
              } else if (state is FeedbackError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: AppColors.critical,
                  ),
                );
              }
            },
            builder: (context, state) {
              if (state is FeedbackLoading) {
                return const Center(child: LoadingIndicator());
              }

              if (state is FeedbackQuestionsLoaded) {
                return _buildForm(context, l10n, state);
              }

              if (state is FeedbackError) {
                return _buildErrorState(context, l10n, state);
              }

              return const SizedBox.shrink();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildForm(
    BuildContext context,
    AppLocalizations l10n,
    FeedbackQuestionsLoaded state,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.satisfactionSurvey,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
              height: 1.5.sp,
            ),
          ),
          SizedBox(height: 24.h),

          // Questions
          ...state.questions.map(
            (question) => _buildQuestionCard(context, l10n, question),
          ),

          SizedBox(height: 24.h),

          // Suggestions
          _buildSuggestionsSection(context, l10n, state),

          SizedBox(height: 24.h),

          // Action buttons
          _buildActionButtons(context, l10n, state),

          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  Widget _buildQuestionCard(
    BuildContext context,
    AppLocalizations l10n,
    FeedbackQuestion question,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 24.h),
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question.question,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textDefaultDark,
              height: 1.5.sp,
            ),
          ),
          SizedBox(height: 16.h),
          _buildRatingOptions(context, l10n, question),
        ],
      ),
    );
  }

  Widget _buildRatingOptions(
    BuildContext context,
    AppLocalizations l10n,
    FeedbackQuestion question,
  ) {
    final ratingLabels = [
      l10n.ratingMost,
      l10n.ratingMuch,
      l10n.ratingModerate,
      l10n.ratingLittle,
      l10n.ratingLeast,
    ];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: List.generate(5, (index) {
        final rating = 5 - index; // 5, 4, 3, 2, 1
        return Expanded(
          child: CustomRadioButton<int>(
            value: rating,
            groupValue: question.selectedRating,
            onChanged: (value) {
              if (value != null) {
                context.read<FeedbackBloc>().add(
                  UpdateQuestionRating(questionId: question.id, rating: value),
                );
              }
            },
            label: ratingLabels[index],
            isVertical: true,
          ),
        );
      }),
    );
  }

  Widget _buildSuggestionsSection(
    BuildContext context,
    AppLocalizations l10n,
    FeedbackQuestionsLoaded state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.additionalSuggestions,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),
        CustomTextField(
          controller: _suggestionsController,
          label: l10n.pleaseSpecify,
          maxLines: 5,
          minLines: 5,
          onChanged: (value) {
            context.read<FeedbackBloc>().add(
              UpdateSuggestions(suggestions: value),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    AppLocalizations l10n,
    FeedbackQuestionsLoaded state,
  ) {
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.cancel,
            height: 40.h,
            onPressed: () => context.router.pop(),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: PrimaryButton(
            text: l10n.submitSurvey,
            height: 40.h,
            isLoading: state.isSubmitting,
            onPressed:
                state.isFormValid && !state.isSubmitting
                    ? () => _handleSubmit(context, l10n)
                    : null,
          ),
        ),
      ],
    );
  }

  Future<void> _handleSubmit(
    BuildContext context,
    AppLocalizations l10n,
  ) async {
    final confirmed = await _showConfirmationDialog(context, l10n);

    if (confirmed && context.mounted) {
      context.read<FeedbackBloc>().add(const SubmitFeedbackEvent());
    }
  }

  Widget _buildErrorState(
    BuildContext context,
    AppLocalizations l10n,
    FeedbackError state,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.w, color: AppColors.critical),
          SizedBox(height: 16.h),
          Text(
            state.message,
            style: TextStyle(
              fontFamily: AppFonts.notoSansThai,
              fontSize: 16.sp,
              color: AppColors.textDefaultDark,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          PrimaryButton(
            text: l10n.retry,
            onPressed: () => _feedbackBloc.add(const LoadFeedbackQuestions()),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmationDialog(
    BuildContext context,
    AppLocalizations l10n,
  ) async {
    final confirmed = await DialogConfirm.show(
      context: context,
      title: l10n.confirmSubmitSurvey,
      message: l10n.confirmSubmitSurveyMessage,
    );

    return confirmed ?? false;
  }

  Future<void> _showSuccessDialog(
    BuildContext context,
    AppLocalizations l10n,
  ) async {
    await DialogSuccess.show(
      context: context,
      afterDismiss: () => context.router.pop(),
    );
  }
}
