import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/feedback_submission.dart';
import '../repositories/feedback_repository.dart';

/// Parameters for submitting feedback
class SubmitFeedbackParams {
  final FeedbackSubmission submission;

  const SubmitFeedbackParams({required this.submission});
}

/// Use case for submitting feedback
class SubmitFeedback implements UseCase<void, SubmitFeedbackParams> {
  final FeedbackRepository repository;

  const SubmitFeedback(this.repository);

  @override
  Future<Either<Failure, void>> call(SubmitFeedbackParams params) async {
    return await repository.submitFeedback(params.submission);
  }
}
