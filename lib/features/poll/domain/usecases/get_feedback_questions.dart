import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/feedback_question.dart';
import '../repositories/feedback_repository.dart';

/// Use case for getting feedback questions
class GetFeedbackQuestions implements UseCase<List<FeedbackQuestion>, NoParams> {
  final FeedbackRepository repository;

  const GetFeedbackQuestions(this.repository);

  @override
  Future<Either<Failure, List<FeedbackQuestion>>> call(NoParams params) async {
    return await repository.getFeedbackQuestions();
  }
}
