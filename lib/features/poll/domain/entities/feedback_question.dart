import 'package:equatable/equatable.dart';

/// Feedback question entity for domain layer
class FeedbackQuestion extends Equatable {
  const FeedbackQuestion({
    required this.id,
    required this.question,
    required this.order,
    this.selectedRating,
  });

  final String id;
  final String question;
  final int order;
  final int? selectedRating;

  FeedbackQuestion copyWith({
    String? id,
    String? question,
    int? order,
    int? selectedRating,
  }) {
    return FeedbackQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      order: order ?? this.order,
      selectedRating: selectedRating ?? this.selectedRating,
    );
  }

  @override
  List<Object?> get props => [id, question, order, selectedRating];
}
