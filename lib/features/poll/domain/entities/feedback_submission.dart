import 'package:equatable/equatable.dart';

/// Feedback submission entity for domain layer
class FeedbackSubmission extends Equatable {
  const FeedbackSubmission({
    required this.answers,
    this.suggestions,
    this.submittedAt,
  });

  final Map<String, int> answers; // questionId -> rating
  final String? suggestions;
  final DateTime? submittedAt;

  FeedbackSubmission copyWith({
    Map<String, int>? answers,
    String? suggestions,
    DateTime? submittedAt,
  }) {
    return FeedbackSubmission(
      answers: answers ?? this.answers,
      suggestions: suggestions ?? this.suggestions,
      submittedAt: submittedAt ?? this.submittedAt,
    );
  }

  @override
  List<Object?> get props => [answers, suggestions, submittedAt];
}
