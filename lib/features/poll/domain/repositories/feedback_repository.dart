import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import '../entities/feedback_question.dart';
import '../entities/feedback_submission.dart';

/// Repository interface for feedback operations
abstract class FeedbackRepository {
  /// Get feedback questions
  Future<Either<Failure, List<FeedbackQuestion>>> getFeedbackQuestions();

  /// Submit feedback
  Future<Either<Failure, void>> submitFeedback(FeedbackSubmission submission);
}
