name: mcdc
description: "Matching System Consultant Mobile Application Data Center Consulting"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 0.0.3+3

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # For multi-language
  flutter_localizations:
    sdk: flutter
  intl: any
  # Automatically generated routing
  auto_route: ^10.0.1
  # For adapting screen and font size on different screen sizes
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.17
  # Object equality by values instead of Dart's default referential equality
  equatable: ^2.0.7
  flutter_inappwebview: ^6.1.5
  # For models
  json_annotation: ^4.9.0
  # For code generator for data-classes/unions/pattern-matching/cloning
  freezed: ^3.0.6
  # annotation @freezed for freezed code generator
  freezed_annotation: ^3.0.0
  # Functional programming to error handling (We'll use only the "Either" type)
  dartz: ^0.10.1
  # For http request
  dio: ^5.8.0+1
  # BLOC state management on Presentation layer
  flutter_bloc: ^9.1.0
  # Inversion of control
  get_it: ^8.0.3
  # For environment variables
  flutter_dotenv: ^5.2.1
  # For shimmer effect
  shimmer: ^3.0.0
  # For date picker
  flutter_rounded_date_picker: ^3.0.4
  # For image picker
  image_picker: ^1.1.2
  # For modal bottom sheet
  modal_bottom_sheet: ^3.0.0
  # For launching URLs
  url_launcher: ^6.3.1
  flutter_secure_storage: ^9.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  # for querying information about an application package
  auto_route_generator: ^10.0.1
  # for generating launcher icons
  flutter_launcher_icons: ^0.14.3
  # For models
  json_serializable: ^6.9.4
  # For testing BLoC/Cubit
  bloc_test: ^10.0.0
  # For mocking dependencies in tests
  mockito: ^5.4.6

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - assets/icons/
    - assets/mock/
    - .env
    - .env.uat

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To generate file like l10n/app_en.arb and l10n/app_th.arb
  generate: true

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: NotoSansThai
      fonts:
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Black.ttf
          weight: 900
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Bold.ttf
          weight: 700
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Medium.ttf
          weight: 500
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Regular.ttf
          weight: 400
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Light.ttf
          weight: 300
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Thin.ttf
          weight: 100
        # - asset: assets/fonts/Noto_Sans_Thai/NotoSansThai-Italic.ttf
        #   style: italic
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/launcher.png"
  min_sdk_android: 21 # android min sdk min:16, default 21