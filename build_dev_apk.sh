#!/bin/bash

# Exit on error
set -e

echo "Building Dev APK..."

# Extract version from pubspec.yaml
VERSION=$(grep "version:" pubspec.yaml | sed 's/version: //g' | sed 's/ #.*//g' | tr -d '[:space:]')

echo "Building version: $VERSION"

# Build the UAT APK
flutter build apk -t lib/main.dart

# Rename the APK to include "uat" and version in the filename
mv build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/mcdc-dev-v$VERSION.apk

echo "UAT APK build completed successfully!"
echo "APK location: build/app/outputs/flutter-apk/mcdc-dev-v$VERSION.apk" 